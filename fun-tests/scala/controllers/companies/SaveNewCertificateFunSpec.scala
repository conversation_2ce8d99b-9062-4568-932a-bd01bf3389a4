package controllers.companies

import com.qrsof.core.app.api.JsonSupport
import com.qrsof.core.http.{ClientHttpFactory, HttpResponse, JsonRequestBody}
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar.mock
import play.api.libs.json.Json

import java.io.{ByteArrayInputStream, File}
import java.net.URL
import scala.concurrent.duration.Duration
import scala.concurrent.Await
import scala.util.Using

class SaveNewCertificateFunSpec extends PathAnyFreeSpec with Matchers with JsonSupport {

  "Save new certificate" in {

    // dev
    //		val appKey = "empresalit-appkey"
    //		val appManagerUrl = "http://localhost:9000/qrapimanager/user/signin"
    //		val empresalitUrl = "http://localhost:9001/companies/certificates"
    //		val privateCertificate = new File("it/resources/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/CSD_XOCHILT_CASAS_CHAVEZ_CACX7605101P8_20190528_173544.key")
    //		val publicCertificate = new File("it/resources/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/30001000000400002335.cer")
    //		val username = "<EMAIL>"
    //		val password = "<EMAIL>"
    //		val certificatePassword = "12345678a"
    //		val certificateNumber = "30001000000400002335"

//sandbox
//		val appKey = "c270d690-c06d-11ea-b3de-0242ac130005"
//		val appManagerUrl = "https://sandbox.api.appmanager.mx/api/qrapimanager/user/signin"
//		val empresalitUrl = "https://sandbox.api.empresalit.com/companies/certificates "
//		val privateCertificate = new File("/Users/<USER>/LocalNextcloud/Qrsof/Qrsof-Dev/proyectos/merdiz/dimsa/llave.key")
//		val publicCertificate = new File("/Users/<USER>/LocalNextcloud/Qrsof/Qrsof-Dev/proyectos/merdiz/dimsa/public_key_dimsa.der")
//		val username = "<EMAIL>"
//		val password = "password"
//		val certificatePassword = "qrsof"

//prod

//    val testKit = ActorTestKit() //No in play
//    implicit val systemTest = testKit.system

    val appKey = "017c9f88-5d13-11e8-9c2d-fa8ae01bbebe"
    val appManagerUrl = "https://api.appmanager.mx/qrapimanager/user/signin"
    val empresalitUrl = "https://api.empresalit.com/companies/certificates"
    val privateCertificateUrl = "/Users/<USER>/LocalNextcloud/Qrsof/Qrsof-Dev/proyectos/merdiz/merdiz/00001000000403995203.key"
    val privateCertificateFile = new File("/Users/<USER>/LocalNextcloud/Qrsof/Qrsof-Dev/proyectos/merdiz/merdiz/00001000000403995203.key")
    val publicCertificateUrl = "/Users/<USER>/LocalNextcloud/Qrsof/Qrsof-Dev/proyectos/merdiz/merdiz/public_key.der"
    val publicCertificateFile = new File("/Users/<USER>/LocalNextcloud/Qrsof/Qrsof-Dev/proyectos/merdiz/merdiz/public_key.der")
    val username = "<EMAIL>"
    val password = "m3rD1z2022"
    val certificatePassword: String = "qrsof"
    //		val certificateNumber = "00001000000510284886"

    val akkaHttpFactory = mock[ClientHttpFactory]
//    val akkaHttpFactory = new AkkaHttpFactory().url(appManagerUrl)
    val response: HttpResponse[String] = Await.result(
      akkaHttpFactory
        .url(appManagerUrl)
        .post[String](
        Some(JsonRequestBody(s"""{"email":"$username","password":"$password","appKey":"$appKey"}"""))
      ),
      Duration.Inf
    )
    var privateCertificateInputStream = getFileFromUrlInBytes(privateCertificateUrl)
    var publicCertificateInputStream = getFileFromUrlInBytes(publicCertificateUrl)

    response.status shouldEqual 200
//    val token = Json.parse(response.body).\("token").as[String]
    val token = Json.fromJson[String](Json.parse(response.toString)).get

    /*val request = Source(
      FilePart("privateKey", privateCertificate.getName, None, FileIO.fromPath(privateCertificate.toPath))
        :: FilePart("publicKey", publicCertificate.getName, None, FileIO.fromPath(publicCertificate.toPath))
        :: DataPart("password", certificatePassword)
        :: List()
    )*/
    //

//    val empresalitResponse = Await.result(
//      new AkkaHttpFactory()
//        .url(empresalitUrl)
//        .withAuthentication(BearerAuthentication(token))
//        .post[String](
//          MultipartRequestBody(
//            Seq(
//              MultipartData(
//                dataKey = "password",
//                data = certificatePassword.getBytes,
//                contentType = "text/html; charset=utf-8"
//              ),
//              MultipartFile(
//                dataKey = "publicKey",
//                file = publicCertificateInputStream,
//                name = publicCertificateFile.getName,
//                mediaType = "application/pkix-cert"
//              ),
//              MultipartFile(
//                dataKey = "privateKey",
//                file = privateCertificateInputStream,
//                name = privateCertificateFile.getName,
//                mediaType = "application/pkcs8"
//              )
//            )
//          )
//        ),
//      Duration.Inf
//    )
//
//    println(empresalitResponse.body)
//    empresalitResponse.status shouldEqual 201

  }

  def getFileFromUrlInBytes(urlString: String): ByteArrayInputStream = {
    Using.resource(new URL(urlString).openStream()) { in =>
      new ByteArrayInputStream(in.readAllBytes())
    }
  }

}
