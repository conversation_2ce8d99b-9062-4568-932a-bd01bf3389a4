package controllers.invoices

import com.qrsof.core.app.api.JsonSupport
import com.qrsof.core.http.Authentication.BearerAuthentication
import com.qrsof.core.http.{ClientHttpFactory, JsonRequestBody}
import org.apache.pekko.actor.typed.ActorSystem
import org.apache.pekko.actor.typed.javadsl.Behaviors
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar.mock
import play.api.libs.json.Json

import scala.concurrent.duration.Duration
import scala.concurrent.Await

class GetInvoiceStatusFunSpec extends PathAnyFreeSpec with Matchers with JsonSupport {

  "Get the inovice status" - {
    val testKit = ActorSystem(Behaviors.empty, "testing")
    implicit val systemTest: ActorSystem[Nothing] = testKit
    val urlSignIn = "https://sandbox.api.appmanager.mx/api/qrapimanager/user/signin";
    val akkaHttpFactory = mock[ClientHttpFactory].url(urlSignIn)

    val urlEmpresalitStatus = "https://sandbox.api.empresalit.com/companies/invoices/uuid/status";

    val appKey = "c270d690-c06d-11ea-b3de-0242ac130005"
    val username = "<EMAIL>"
    val password = "password"

    val requestSignIn = s"""{"email":"$username","password":"$password","appKey":"$appKey"}"""

    val futureRequest = akkaHttpFactory.post[String](Some(JsonRequestBody(requestSignIn)))
    val authResponse = Await.result(futureRequest, Duration.Inf)
    println(authResponse.body)
    authResponse.status shouldEqual 200
//    val token = Json.parse(authResponse.body).\("token").as[String]
    val token = Json.fromJson[String](Json.parse(authResponse.toString)).get

    val empresalitResponse = Await.result(
      mock[ClientHttpFactory]
        .url(urlEmpresalitStatus)
        .withAuthentication(BearerAuthentication(token))
        .withQueryParameters(("rr", "AAA010101AAA"), ("fe", "sello"), ("tt", "10"))
        .get[String](),
      Duration.Inf
    )

    if (empresalitResponse.status != 200) {
      println(empresalitResponse.body)
      fail()
    } else {
      println(empresalitResponse.body)
      succeed
    }
  }
}
