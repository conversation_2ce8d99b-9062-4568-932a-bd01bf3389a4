package controllers.invoices

import com.qrsof.core.app.api.JsonSupport
import com.qrsof.core.http.Authentication.BearerAuthentication
import com.qrsof.core.http.{ClientHttpFactory, JsonRequestBody}
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import play.api.libs.json.Json
import org.apache.pekko.actor.typed.ActorSystem
import org.apache.pekko.actor.typed.javadsl.Behaviors
import org.scalatestplus.mockito.MockitoSugar.mock

import scala.concurrent.Await
import scala.concurrent.duration.Duration

class CancelInvoiceFunSpec extends PathAnyFreeSpec with Matchers with JsonSupport {

  "Cancel invoice by uuid" in {
    val testKit = ActorSystem(Behaviors.empty, "testing")
    implicit val systemTest: ActorSystem[Nothing] = testKit
//    val akkaHttpFactory = new AkkaHttpFactory().url("https://sandbox.api.appmanager.mx/api/qrapimanager/user/signin")
    val akkaHttpFactory = mock[ClientHttpFactory]

    val appKey = "c270d690-c06d-11ea-b3de-0242ac130005"
    val username = "<EMAIL>"
    val password = "m3rD1z2022"

    val requestSignIn = s"""{"email":"$username","password":"$password","appKey":"$appKey"}"""

    val futureRequest = akkaHttpFactory.url("https://sandbox.api.appmanager.mx/api/qrapimanager/user/signin").post[String](Some(JsonRequestBody(requestSignIn)))
    val authResponse = Await.result(futureRequest, Duration.Inf)
    println(authResponse.body)
    authResponse.status shouldEqual 200

//    val token = Json.parse(authResponse.body).\("token").as[String]
    val token = Json.fromJson[String](Json.parse(authResponse.toString)).get
    val uuid = "c270d690-c06d-11ea-b3de-0242ac130005"

    val empresalitResponse = Await.result(
      akkaHttpFactory
        .url(s"https://sandbox.api.empresalit.com/companies/invoices/${uuid}")
        .withAuthentication(BearerAuthentication(token))
        .delete[String](),
      Duration.Inf
    )

    val responseBody = empresalitResponse.body
    println(responseBody)
    empresalitResponse.status shouldEqual 200
  }
}
