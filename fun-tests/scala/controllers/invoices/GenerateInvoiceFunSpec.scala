package controllers.invoices

import com.qrsof.core.app.api.JsonSupport
import com.qrsof.core.http.Authentication.BearerAuthentication
import com.qrsof.core.http.{ClientHttpFactory, JsonRequestBody}
import com.qrsof.empresalit.invoicing.domain.Receptor
import com.qrsof.empresalit.invoicing.domain.complementos.pagos.DoctoRelacionado
import com.qrsof.empresalit.invoicing.domain.conceptos.{CuentaPredial, InformacionAduanera}
import com.qrsof.empresalit.invoicing.generateinvoice.ReceptorInvoice
import com.qrsof.empresalit.controllers.invoices.forms.*
import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.figuratransporte.{DomicilioFiguraForm, PartesTransporteForm, TiposFiguraForm}
import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.mercancias.*
import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.ubicaciones.{DomicilioForm, UbicacionForm}
import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.{CartaPorteForm, FiguratransporteForm, MercanciasForm, UbicacionesForm}
import com.qrsof.empresalit.controllers.invoices.forms.complementos.nomina.*
import com.qrsof.empresalit.controllers.invoices.forms.getinvoicestatus.GetInvoiceStatusForm
import org.apache.commons.compress.archivers.zip.ZipFile
import org.apache.commons.compress.utils.{IOUtils, SeekableInMemoryByteChannel}
import org.apache.pekko.NotUsed
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar.mock
import play.api.libs.json.{Json, OWrites}
import org.apache.pekko.actor.typed.ActorSystem
import org.apache.pekko.actor.typed.javadsl.Behaviors
import org.apache.pekko.stream.scaladsl.{Flow, Sink, Source, StreamConverters}
import org.apache.pekko.util.ByteString

import java.io.InputStream
import scala.concurrent.duration.Duration
import scala.concurrent.Await

class GenerateInvoiceFunSpec extends PathAnyFreeSpec with Matchers with JsonSupport {

  implicit val subsidioAlEmpleoFormWritter: OWrites[SubsidioAlEmpleoForm] = Json.writes[SubsidioAlEmpleoForm]
  implicit val subContratacionFormWritter: OWrites[SubContratacionForm] = Json.writes[SubContratacionForm]
  implicit val separacionIndemnizacionFormWritter: OWrites[SeparacionIndemnizacionForm] = Json.writes[SeparacionIndemnizacionForm]
  implicit val horasExtraFormWritter: OWrites[HorasExtraForm] = Json.writes[HorasExtraForm]
  implicit val accionesOTitulosFormWritter: OWrites[AccionesOTitulosForm] = Json.writes[AccionesOTitulosForm]
  implicit val percepcionFormWritter: OWrites[PercepcionForm] = Json.writes[PercepcionForm]
  implicit val compensacionSaldosFavorFormWritter: OWrites[CompensacionSaldosFavorForm] = Json.writes[CompensacionSaldosFavorForm]
  implicit val otroPagoFormWritter: OWrites[OtroPagoForm] = Json.writes[OtroPagoForm]
  implicit val jubilacionPensionRetiroFormWritter: OWrites[JubilacionPensionRetiroForm] = Json.writes[JubilacionPensionRetiroForm]
  implicit val incapacidadFormWritter: OWrites[IncapacidadForm] = Json.writes[IncapacidadForm]
  implicit val entidadSNCFFormWritter: OWrites[EntidadSNCFForm] = Json.writes[EntidadSNCFForm]
  implicit val deduccionFormWritter: OWrites[DeduccionForm] = Json.writes[DeduccionForm]
  implicit val incapacidadesFormWritter: OWrites[IncapacidadesForm] = Json.writes[IncapacidadesForm]
  implicit val otrosPagosFormWritter: OWrites[OtrosPagosForm] = Json.writes[OtrosPagosForm]
  implicit val deduccionesFormWritter: OWrites[DeduccionesForm] = Json.writes[DeduccionesForm]
  implicit val percepcionesFormWritter: OWrites[PercepcionesForm] = Json.writes[PercepcionesForm]
  implicit val receptorNomFormWritter: OWrites[ReceptorNomForm] = Json.writes[ReceptorNomForm]
  implicit val emisorFormWritter: OWrites[EmisorForm] = Json.writes[EmisorForm]
  implicit val nominaCompFormWritter: OWrites[NominaCompForm] = Json.writes[NominaCompForm]
  implicit val domicilioFiguraWritter: OWrites[DomicilioFiguraForm] = Json.writes[DomicilioFiguraForm]
  implicit val partesTransporteWritter: OWrites[PartesTransporteForm] = Json.writes[PartesTransporteForm]
  implicit val tiposFiguraWritter: OWrites[TiposFiguraForm] = Json.writes[TiposFiguraForm]
  implicit val cantidadTransportaWritter: OWrites[CantidadTransportaForm] = Json.writes[CantidadTransportaForm]
  implicit val contenedorCarroWritter: OWrites[ContenedorCarroForm] = Json.writes[ContenedorCarroForm]
  implicit val carroWritter: OWrites[CarroForm] = Json.writes[CarroForm]
  implicit val contenedorWritter: OWrites[ContenedorForm] = Json.writes[ContenedorForm]
  implicit val derechodePasoWritter: OWrites[DerechosDePasoForm] = Json.writes[DerechosDePasoForm]
  implicit val detalleMercanciaWritter: OWrites[DetalleMercanciaForm] = Json.writes[DetalleMercanciaForm]
  implicit val guiasIdentificacionWritter: OWrites[GuiasIdentificacionForm] = Json.writes[GuiasIdentificacionForm]
  implicit val identificacionVehicularWritter: OWrites[IdentificacionVehicularForm] = Json.writes[IdentificacionVehicularForm]
  implicit val pedimentoWritter: OWrites[PedimentoForm] = Json.writes[PedimentoForm]
  implicit val mercanciaWritter: OWrites[MercanciaForm] = Json.writes[MercanciaForm]
  implicit val remolqueWritter: OWrites[RemolqueForm] = Json.writes[RemolqueForm]
  implicit val remolquesWritter: OWrites[RemolquesForm] = Json.writes[RemolquesForm]
  implicit val segurosWritter: OWrites[SegurosForm] = Json.writes[SegurosForm]
  implicit val transporteAereoWriter: OWrites[TransporteAereoForm] = Json.writes[TransporteAereoForm]
  implicit val autotransporteWriter: OWrites[AutotransporteForm] = Json.writes[AutotransporteForm]
  implicit val transporteFerroviarioWriter: OWrites[TransporteFerroviarioForm] = Json.writes[TransporteFerroviarioForm]
  implicit val transporteMaritimoWriter: OWrites[TransporteMaritimoForm] = Json.writes[TransporteMaritimoForm]
  implicit val domicilioWritter: OWrites[DomicilioForm] = Json.writes[DomicilioForm]
  implicit val ubicacionWritter: OWrites[UbicacionForm] = Json.writes[UbicacionForm]
  implicit val mercanciasWritter: OWrites[MercanciasForm] = Json.writes[MercanciasForm]
  implicit val ubicacionesWritter: OWrites[UbicacionesForm] = Json.writes[UbicacionesForm]
  implicit val figuraTransporteWritter: OWrites[FiguratransporteForm] = Json.writes[FiguratransporteForm]
  implicit val cartaPorteWritter: OWrites[CartaPorteForm] = Json.writes[CartaPorteForm]
  implicit val doctoRelacionadoWritter: OWrites[DoctoRelacionado] = Json.writes[DoctoRelacionado]
  implicit val impuestoTrasladoWritter: OWrites[TrasladoForm] = Json.writes[TrasladoForm]
  implicit val impuestoRetencionWritter: OWrites[RetencionForm] = Json.writes[RetencionForm]
  implicit val impuestosWritter: OWrites[ImpuestosForm] = Json.writes[ImpuestosForm]
  implicit val doctoRelacionadoFormWritter: OWrites[DoctoRelacionadoForm] = Json.writes[DoctoRelacionadoForm]
  implicit val pagoWritter: OWrites[PagoForm] = Json.writes[PagoForm]
  implicit val recepcionPagosWritter: OWrites[RecepcionPagoForm] = Json.writes[RecepcionPagoForm]
  implicit val complementosWritter: OWrites[ComplementosForm] = Json.writes[ComplementosForm]
  implicit val inlineConceptosImpuestosRetencionWritter: OWrites[ConceptoRetencionForm] = Json.writes[ConceptoRetencionForm]
  implicit val inlineConceptosImpuestosTrasladoWritter: OWrites[ConceptoTrasladoForm] = Json.writes[ConceptoTrasladoForm]
  implicit val inlineConceptosCuentaPredialWritter: OWrites[CuentaPredial] = Json.writes[CuentaPredial]
  implicit val inlineConceptosInformacionAduaneraWritter: OWrites[InformacionAduanera] = Json.writes[InformacionAduanera]
  implicit val inlineConceptosImpuestosWritter: OWrites[ConceptosImpuestosForm] = Json.writes[ConceptosImpuestosForm]
  implicit val inlineConceptoWritter: OWrites[ConceptoForm] = Json.writes[ConceptoForm]
  implicit val inlineReceptorInvoiceWritter: OWrites[ReceptorInvoice] = Json.writes[ReceptorInvoice]
  implicit val inlineReceptorWritter: OWrites[Receptor] = Json.writes[Receptor]
  implicit val generateInvoiceFormWritter: OWrites[GenerateInvoiceForm] = Json.writes[GenerateInvoiceForm]
  implicit val getInvoiceStatusFormWritter: OWrites[GetInvoiceStatusForm] = Json.writes[GetInvoiceStatusForm]
  implicit val invoiceCancelationFormWritter: OWrites[InvoiceCancelationForm] = Json.writes[InvoiceCancelationForm]

  "Generate invoice" - {

    val receptor: ReceptorInvoice = ReceptorInvoice(
      rfc = "XAXX010101000",
      name = Some("ACME SA DE CV"),
      usoCfdi = "G02"
    )

    val impuestos: ImpuestosForm = ImpuestosForm(
      totalImpuestosTrasladados = Some("1.6"),
      traslados = Some(Seq(TrasladoForm(impuesto = "002", tipoFactor = "Tasa", tasaOCuota = "0.160000", importe = "1.6")))
    )

    val concepto: ConceptoForm = ConceptoForm(
      claveProductoServicio = "81111500",
      cantidad = "1",
      claveUnidad = "E48",
      valorUnitario = "10",
      descripcion = "descripcion",
      importe = "10",
      impuestos = Some(
        ConceptosImpuestosForm(
          traslados = Seq(ConceptoTrasladoForm("10", "002", "Tasa", "0.160000", "1.6"))
        )
      ),
      informacionAduanera = None,
      cuentaPredial = None
    )
    val conceptos = Seq(concepto)

    val incapacidad: IncapacidadForm = IncapacidadForm(
      diasIncapacidad = 4,
      tipoIncapacidad = "03",
      importeMonetario = Some("345")
    )

    val incapacidades: IncapacidadesForm = IncapacidadesForm(
      incapacidad = Seq(incapacidad)
    )

    val compensacionSaldosAFavor: CompensacionSaldosFavorForm = CompensacionSaldosFavorForm(
      saldoAFavor = "345",
      anio = 2020.toShort,
      remanenteSalFav = "345"
    )

    val subsidioAlEmpleo: SubsidioAlEmpleoForm = SubsidioAlEmpleoForm(
      subsidioCausado = "24"
    )

    val otroPago: OtroPagoForm = OtroPagoForm(
      tipoOtroPago = "001",
      clave = "clave",
      concepto = "concepto",
      importe = "200",
      subsidioAlEmpleo = Some(subsidioAlEmpleo),
      compensacionSaldosAFavor = Some(compensacionSaldosAFavor)
    )

    val otrospagos: OtrosPagosForm = OtrosPagosForm(
      otroPago = Seq(otroPago)
    )

    val deduccion: DeduccionForm = DeduccionForm(
      tipoDeduccion = "001",
      clave = "clave",
      concepto = "concepto",
      importe = "50"
    )

    val deducciones: DeduccionesForm = DeduccionesForm(
      totalOtrasDeducciones = Some("0"),
      totalImpuestosRetenidos = Some("0"),
      deduccion = Seq(deduccion)
    )

    val separacionIndemnizacion: SeparacionIndemnizacionForm = SeparacionIndemnizacionForm(
      totalPagado = "34.87",
      numAniosServicio = 4,
      ultimoSueldoMensOrd = "34.87",
      ingresoAcumulable = "34.87",
      ingresoNoAcumulable = "34.87"
    )

    val jubilacionPensionRetiro: JubilacionPensionRetiroForm = JubilacionPensionRetiroForm(
      totalUnaExhibicion = Some("34.87"),
      totalParcialidad = Some("34.87"),
      montoDiario = Some("34.87"),
      ingresoAcumulable = "34.87",
      ingresoNoAcumulable = "34.87"
    )

    val horasExtra: HorasExtraForm = HorasExtraForm(
      dias = 23,
      tipoHoras = "01",
      horasExtra = 43,
      importePagado = "43"
    )

    val accionesOTitulos: AccionesOTitulosForm = AccionesOTitulosForm(
      valorMercado = "23.21",
      precioAlOtorgarse = "32.65"
    )

    val percepcion: PercepcionForm = PercepcionForm(
      tipoPercepcion = "022",
      clave = "clave",
      concepto = "concepto",
      importeGravado = "100",
      importeExento = "0",
      accionesOTitulos = Some(accionesOTitulos),
      horasExtra = Seq(horasExtra)
    )

    val percepciones: PercepcionesForm = PercepcionesForm(
      totalSueldos = Some("100"),
      totalSeparacionIndemnizacion = Some("100"),
      totalJubilacionPensionRetiro = Some("100"),
      totalGravado = "300",
      totalExento = "0",
      percepcion = Seq(percepcion),
      jubilacionPensionRetiro = Some(jubilacionPensionRetiro),
      separacionIndemnizacion = Some(separacionIndemnizacion)
    )

    val subContratacion: SubContratacionForm = SubContratacionForm(
      rfcLabora = "CACX7605101P8",
      porcentajeTiempo = "100"
    )

    val receptorNom: ReceptorNomForm = ReceptorNomForm(
      curp = "XEXX010101MNEXXXA8",
      numSeguridadSocial = Some("123456789876543"),
      fechaInicioRelLaboral = Some("2022-01-01"),
      antiguedad = Some("P17D"),
      tipoContrato = "01",
      sindicalizado = Some("No"),
      tipoJornada = Some("01"),
      tipoRegimen = "02",
      numEmpleado = "numEmpleado",
      departamento = Some("departamento"),
      puesto = Some("puesto"),
      riesgoPuesto = Some("5"),
      periodicidadPago = "05",
      banco = Some("019"),
      cuentaBancaria = Some("87968098743".toLong),
      salarioBaseCotApor = Some("50"),
      salarioDiarioIntegrado = Some("32.35"),
      claveEntFed = "AGU",
      subContratacion = Seq(subContratacion)
    )

    val entidadSNCF: EntidadSNCFForm = EntidadSNCFForm(
      origenRecurso = "IP",
      montoRecursoPropio = Some("21.32")
    )

    val emisor: EmisorForm = EmisorForm(
      curp = Some("XEXX010101MNEXXXA8"),
      registroPatronal = Some("RegistroPatronal"),
      rfcPatronOrigen = Some("CACX7605101P8"),
      entidadSNCF = None
    )

    val nomina: NominaCompForm = NominaCompForm(
      version = "1.2",
      tipoNomina = "O",
      fechaPago = "2022-01-19",
      fechaInicialPago = "2022-01-09",
      fechaFinalPago = "2022-01-19",
      numDiasPagados = "5.500",
      totalPercepciones = Some("300"),
      totalDeducciones = Some("0"),
      totalOtrosPagos = Some("0"),
      emisor = Some(emisor),
      receptorNom = receptorNom,
      percepciones = Some(percepciones),
      deducciones = Some(deducciones),
      //			otrosPagos = Some(otrospagos),
      incapacidades = Some(incapacidades)
    )

    val detalle: DetalleMercanciaForm = DetalleMercanciaForm(
      unidadPesoMerc = "unidadPesoMerc",
      pesoBruto = "87",
      pesoNeto = "87",
      pesoTara = "20",
      numPiezas = Some(44)
    )
    val guia: GuiasIdentificacionForm = GuiasIdentificacionForm(
      numeroGuiaIdentificacion = "numeroGuiaIdentificacion",
      descripGuiaIdentificacion = "descripGuiaIdentificacion",
      pesoGuiaIdentificacion = "43"
    )
    val cantidad: CantidadTransportaForm = CantidadTransportaForm(
      cantidad = "34",
      idOrigen = "idOrigen",
      idDestino = "idDestino",
      cvesTransporte = Some("cvesTransporte")
    )
    val pedimento: PedimentoForm = PedimentoForm(
      pedimento = "pedimento"
    )
    val mercancia: MercanciaForm = MercanciaForm(
      bienesTransp = "bienesTransp",
      descripcion = "descripcion",
      cantidad = "80",
      claveUnidad = "claveUnidad",
      pesoEnKg = "12",
      clavesSTCC = Some("clavesSTCC"),
      unidad = Some("unidad"),
      dimensiones = Some("dimensiones"),
      materialPeligroso = Some("materialPeligroso"),
      cveMaterialPeligroso = Some("cveMaterialPeligroso"),
      embalaje = Some("embalaje"),
      descripEmbalaje = Some("descripEmbalaje"),
      valorMercancia = Some("45"),
      moneda = Some("MXN"),
      fraccionArancelaria = Some("fraccionArancelaria"),
      uuidComercioExt = Some("uuidComercioExt"),
      pedimentos = Option(Seq(pedimento)),
      guiasIdentificacion = Option(Seq(guia)),
      cantidadTransporta = Option(Seq(cantidad)),
      detalleMercancia = Option(Seq(detalle))
    )
    val ubicacionOrigen: UbicacionForm = UbicacionForm(
      tipoUbicacion = "Origen",
      idUbicacion = Some("OR000001"),
      rfcRemitenteDestinatario = "MCO081110B28",
      nombreRemitenteDestinatario = Some("nombreRemitenteDestinatario"),
      fechaHoraSalidaLlegada = "2021-04-09T23:25:42",
      domicilio = Some(
        DomicilioForm(
          calle = Some("calle"),
          numeroExterior = Some("numeroExterior"),
          numeroInterior = Some("numeroInterior"),
          colonia = Some("0150"),
          localidad = None,
          referencia = Some("referencia"),
          municipio = Some("048"),
          estado = "HID",
          pais = "MEX",
          codigoPostal = "42083"
        )
      )
    )

    val ubicacionDestino: UbicacionForm = UbicacionForm(
      tipoUbicacion = "Destino",
      idUbicacion = Some("DE000000"),
      rfcRemitenteDestinatario = "MCO081110B28",
      nombreRemitenteDestinatario = Some("nombreRemitenteDestinatario"),
      fechaHoraSalidaLlegada = "2021-04-09T23:25:42",
      distanciaRecorrida = Some("21"),
      domicilio = Some(
        DomicilioForm(
          calle = Some("calle"),
          numeroExterior = Some("numeroExterior"),
          numeroInterior = Some("numeroInterior"),
          colonia = Some("0150"),
          localidad = None,
          referencia = Some("referencia"),
          municipio = Some("048"),
          estado = "HID",
          pais = "MEX",
          codigoPostal = "42083"
        )
      )
    )

    val mercancias: MercanciasForm = MercanciasForm(
      pesoBrutoTotal = "24",
      unidadPeso = "Tu",
      pesoNetoTotal = Some("24"),
      numTotalMercancias = 1,
      cargoPorTasacion = Some("32.31"),
      mercancia = Seq(
        MercanciaForm(
          bienesTransp = "60121514",
          clavesSTCC = Some("000000"),
          descripcion = "descripcion",
          cantidad = "32.43",
          claveUnidad = "X44",
          unidad = Some("unidad"),
          dimensiones = Some("59/40/36cm"),
          //											materialPeligroso = Some("No"),
          //											cveMaterialPeligroso = Some("M0001"),
          //											embalaje = Some("1A1"),
          //											descripEmbalaje = Some("descripEmbalaje"),
          pesoEnKg = "24",
          valorMercancia = Some("23.23"),
          moneda = Some("MXN"),
          //											fraccionArancelaria = Some("8418699999"),
          uuidComercioExt = Some("550e8400-e29b-41d4-a716-************"),
          //											pedimentos = Some(
          //												Pedimento(
          //													pedimento = "10  47  3807  8003832"
          //												)),
          //											guiasIdentificacion = Some(
          //												GuiasIdentificacion(
          //													numeroGuiaIdentificacion = "numeroGuiaIdentificacion",
          //													descripGuiaIdentificacion = "descripGuiaIdentificacion",
          //													pesoGuiaIdentificacion = BigDecimal("12.11")
          //												)),
          cantidadTransporta = Option(
            Seq(
              CantidadTransportaForm(
                cantidad = "23.32",
                idOrigen = "OR000001",
                idDestino = "DE000000"
                //													cvesTransporte = Some("01")
              )
            )
          ),
          detalleMercancia = None,
          guiasIdentificacion = None,
          pedimentos = None
          //											detalleMercancia = Some(DetalleMercancia(
          //												unidadPesoMerc = "Tu",
          //												pesoBruto = BigDecimal("32.24"),
          //												pesoNeto = BigDecimal("34.23"),
          //												pesoTara = BigDecimal("43.34"),
          //												numPiezas = Some(23)
          //											))
        )
      ),
      autotransporte = Some(
        AutotransporteForm(
          permSCT = "TPAF_01",
          numPermisoSCT = "23",
          identificacionVehicular = IdentificacionVehicularForm(configVehicular = "VL", placaVM = "placaVM", anioModeloVM = 2020),
          seguros = SegurosForm(
            aseguraRespCivil = "aseguraRespCivil",
            polizaRespCivil = "polizaRespCivil",
            //													aseguraMedAmbiente = Some("aseguraMedAmbiente"),
            //													polizaMedAmbiente = Some("polizaMedioAmbiente"),
            aseguraCarga = Some("aseguraCarga"),
            polizaCarga = Some("polizaCarga"),
            primaSeguro = Some("23.23")
          ),
          remolques = Some(
            RemolquesForm(
              Seq(
                RemolqueForm(
                  subTipoRem = "CTR_004",
                  placa = "FM43535"
                )
              )
            )
          )
        )
      )
    )

    val cartaPorte: CartaPorteForm = CartaPorteForm(
      transpInternac = "No",
      totalDistRec = Some("21"),
      ubicaciones = UbicacionesForm(Seq(ubicacionOrigen, ubicacionDestino)),
      mercancias = mercancias,
      figuratransporte = Some(
        FiguratransporteForm(
          tiposFigura = Seq(
            TiposFiguraForm(
              tipoFigura = "01",
              rfcFigura = Some("MCO081110B28"),
              numLicencia = Some("342751"),
              nombreFigura = Some("nombreFigura"),
              domicilio = Some(
                DomicilioFiguraForm(
                  calleFigura = Some("calleFigura"),
                  numeroExteriorFigura = Some("24"),
                  numeroInteriorFigura = Some("34"),
                  coloniaFigura = Some("0150"),
                  localidadFigura = None,
                  referenciaFigura = Some("referenciaFigura"),
                  municipioFigura = Some("048"),
                  estadoFigura = "HID",
                  paisFigura = "MEX",
                  codigoPostalFigura = "42083"
                )
              ),
              partesTransporte = None
            )
          )
        )
      )
    )
    val pago: PagoForm = PagoForm(
      fechaPago = "2021-04-09T23:25:42",
      formaDePagoP = "01",
      monedaP = "MXN",
      monto = "200"
    )
    val recepcionPagos: RecepcionPagoForm = RecepcionPagoForm(
      version = "1.0",
      pagos = Seq(pago)
    )

    //		val appManagerUrl = "http://localhost:9000"
    val empresalitApiUrl = "http://localhost:9001"
    //		val appkey = "empresalit-appkey"
    val appManagerUrl = "https://sandbox.api.appmanager.mx/api"
    //		val empresalitApiUrl = "https://sandbox.api.empresalit.com"
    val appkey = "c270d690-c06d-11ea-b3de-0242ac130005"

    "Sin complementos" in {
      val testKit = ActorSystem(Behaviors.empty, "testing")
      implicit val systemTest: ActorSystem[Nothing] = testKit
//      val akkaHttpFactory = new AkkaHttpFactory().url(appManagerUrl + "/qrapimanager/user/signin")
      val akkaHttpFactory = mock[ClientHttpFactory].url(appManagerUrl + "/qrapimanager/user/signin")

      val generateInvoiceForm: GenerateInvoiceForm = GenerateInvoiceForm(
        serie = Some("A"),
        folio = Some("1"),
        lugarExpedicion = "42083",
        formaPago = Some("02"),
        metodoPago = Some("PUE"),
        moneda = "MXN",
        tipoComprobante = "I",
        condicionesDePago = Some("Condicion de pago"),
        subTotal = "10",
        total = "11.60",
        receptor = Some(receptor),
        conceptos = conceptos,
        impuestos = Some(impuestos),
        tipoCambio = None,
        descuento = None
        //				complementos = Some(complementos)
      )

      val username = "<EMAIL>"
      val password = "password"

      val requestSignIn = s"""{"email":"$username","password":"$password","appKey":"$appkey"}"""

      val futureRequest = akkaHttpFactory.post[String](Some(JsonRequestBody(requestSignIn)))
      val authResponse = Await.result(futureRequest, Duration.Inf)
      println(authResponse.body)
      authResponse.status shouldEqual 200
//      val token = Json.parse(authResponse.body).\("token").as[String]
      val token = Json.fromJson[String](Json.parse(authResponse.toString)).get

      val request: String =
        "{\n   \"serie\":\"A\",\n   \"folio\":\"09128\",\n   \"subTotal\":\"0\",\n   \"moneda\":\"XXX\",\n   \"total\":\"0\",\n   \"tipoComprobante\":\"T\",\n   \"exportacion\":\"01\",\n   \"lugarExpedicion\":\"42088\",\n   \"cfdiRelacionados\":[\n      {\n         \"tipoRelacion\":\"04\",\n         \"cfdiRelacionados\":[\n            {\n               \"uuid\":\"0983D8F6-93AC-4DFE-8780-2CCAB6AF9B4F\"\n            }\n         ]\n      }\n   ],\n   \"receptor\":{\n      \"rfc\":\"CIM100212P19\",\n      \"nombre\":\"COMERCIALIZADORA INDUSTRIAL MERDIZ\",\n      \"domicilioFiscalReceptor\":\"76220\",\n      \"regimenFiscalReceptor\":\"601\",\n      \"usoCfdi\":\"S01\"\n   },\n   \"conceptos\":[\n      {\n         \"claveProductoServicio\":\"15121900\",\n         \"noIdentificacion\":\"550053180\",\n         \"cantidad\":\"1.00\",\n         \"claveUnidad\":\"E48\",\n         \"unidad\":\"Unidad de servicio\",\n         \"descripcion\":\"transporte de carga\",\n         \"valorUnitario\":\"0.000000\",\n         \"importe\":\"0.000000\",\n         \"objetoImp\":\"01\"\n      }\n   ],\n   \"complementos\":{\n      \"cartaPorte\":{\n         \"version\":\"2.0\",\n         \"transpInternac\":\"No\",\n         \"totalDistRec\":52,\n         \"ubicaciones\":{\n            \"ubicacion\":[\n               {\n                  \"tipoUbicacion\":\"Origen\",\n                  \"rfcRemitenteDestinatario\":\"CIM100212P19\",\n                  \"fechaHoraSalidaLlegada\":\"2022-05-19T16:06:36\",\n                  \"domicilio\":{\n                     \"calle\":\"Carretera Pachuca-Actopan Km 7.1\",\n                     \"localidad\":\"03\",\n                     \"municipio\":\"048\",\n                     \"estado\":\"HID\",\n                     \"pais\":\"MEX\",\n                     \"codigoPostal\":\"42088\"\n                  }\n               },\n               {\n                  \"tipoUbicacion\":\"Destino\",\n                  \"rfcRemitenteDestinatario\":\"PDT140520R4A\",\n                  \"fechaHoraSalidaLlegada\":\"2022-05-19T17:06:36\",\n                  \"distanciaRecorrida\": \"52\",\n                  \"domicilio\":{\n                     \"calle\":\"Carretera Tula Refineria\",\n                     \"municipio\":\"076\",\n                     \"estado\":\"HID\",\n                     \"pais\":\"MEX\",\n                     \"codigoPostal\":\"42820\"\n                  }\n               }\n            ]\n         },\n         \"mercancias\":{\n            \"pesoBrutoTotal\":1,\n            \"unidadPeso\":\"KGM\",\n            \"numTotalMercancias\":1,\n            \"mercancia\":[\n               {\n                  \"bienesTransp\":\"15121900\",\n                  \"descripcion\":\"LUBRIONE GRASA CHASIS No. 2\",\n                  \"cantidad\":\"1.00\",\n                  \"claveUnidad\":\"H87\",\n                  \"pesoEnKg\":\"1.00\",\n                  \"pedimentos\":\"\"\n               }\n            ],\n            \"autotransporte\":{\n               \"permSCT\":\"TPAF_02\",\n               \"numPermisoSCT\":\"0\",\n               \"identificacionVehicular\":{\n                  \"configVehicular\":\"VL\",\n                  \"placaVM\":\"B94ABP\",\n                  \"anioModeloVM\":\"2015\"\n               },\n               \"seguros\":{\n                  \"aseguraRespCivil\":\"Qualitas\",\n                  \"polizaRespCivil\":\"1234\"\n               }\n            }\n         },\n         \"figuratransporte\":{\n            \"tiposFigura\":[\n               {\n                  \"tipoFigura\":\"01\",\n                  \"rfcFigura\":\"HETI6904049E2\",\n                  \"numLicencia\":\"01ea91344\",               \n                  \"domicilio\": {\n                      \"estadoFigura\": \"HID\",\n                      \"paisFigura\": \"MEX\",\n                      \"codigoPostalFigura\": \"42088\"\n                  }\n               }\n            ]\n         }\n      }\n   }\n}"

      val empresalitResponse = Await.result(
        mock[ClientHttpFactory]
          .url(empresalitApiUrl + "/companies/invoices")
          .withAuthentication(BearerAuthentication(token))
          .withHeaders(("Accept", "application/com.qrsof.empresalit.invoice.v40+json"))
          .post[String](
            Some(JsonRequestBody(request))
          ),
        Duration.Inf
      )

      if (empresalitResponse.status != 201) {
        println(empresalitResponse.body)
        fail()
      } else {
        // implicit val system: ActorSystem = ActorSystem("Test")
        val toUpperCase: Flow[ByteString, ByteString, NotUsed] = Flow[ByteString].map(_.map(_.toChar.toUpper.toByte))

        val source: Source[ByteString, NotUsed] = Source.single(ByteString(empresalitResponse.body.get))
        val sink: Sink[ByteString, InputStream] = StreamConverters.asInputStream()

        val inputStreamResponse: InputStream = source.via(toUpperCase).runWith(sink)

        val seekableInMemoryByteChannel = new SeekableInMemoryByteChannel(inputStreamResponse.readAllBytes())
        val zipFile = new ZipFile(seekableInMemoryByteChannel)
        val entries = zipFile.getEntries
        val archiveEntry = entries.nextElement()
        val inputStream = zipFile.getInputStream(archiveEntry)
        val bytes = IOUtils.toByteArray(inputStream)
        println(new String(bytes))

        empresalitResponse.status shouldEqual 201
      }
    }

    //		"Complemento pago" in {
    //			val complementos: ComplementosForm = ComplementosForm(
    //				Some(recepcionPagos)
    //			)
    //
    //			val generateInvoiceForm: GenerateInvoiceForm = GenerateInvoiceForm(
    //				serie = Some("A"),
    //				folio = Some("1"),
    //				lugarExpedicion = "42083",
    ////				formaPago = Some("02"),
    ////				metodoPago = Some("PUE"),
    //				moneda = "XXX",
    //				tipoComprobante = "P",
    //				subTotal = "0",
    //				total = "0",
    //				receptor = Some(receptor.copy(
    //					usoCfdi = "P01"
    //				)),
    //				conceptos = conceptos.map(c=>{
    //					c.copy(
    //						claveProductoServicio = "84111506",
    //						cantidad = "1",
    //						claveUnidad = "ACT",
    //						valorUnitario = "0",
    //						descripcion = "Pago",
    //						importe = "0"
    //					)
    //				}),
    //				impuestos = Some(impuestos),
    //				tipoCambio = None,
    //				descuento = None,
    //				complementos = Some(complementos)
    //			)
    //
    //
    //			WsTestClient.withClient { client =>
    //				val body = Map("email" -> Seq("<EMAIL>"), "password" -> Seq("password"), "appKey" -> Seq(appkey))
    //				val loginResponse: Future[WSResponse] = client.url(appManagerUrl + "/qrapimanager/user/signin").post(body)
    //				val responseLogin = Await.result(loginResponse, Duration.Inf)
    //				println(responseLogin.body)
    //				responseLogin.status shouldEqual Status.OK
    //				val token = Json.parse(responseLogin.body).\("token").as[String]
    //
    //
    //				val reslut: Future[WSResponse] = client.url(empresalitApiUrl + "/companies/invoices").withHttpHeaders(("Authorization", s"Bearer $token")).post(Json.toJson(generateInvoiceForm))
    //				val response = Await.result(reslut, Duration.Inf)
    //
    //				if (response.status != Status.CREATED) {
    //					println(response.body)
    //					fail()
    //				} else {
    //
    //					implicit val system: ActorSystem = ActorSystem("Test")
    //					val sink = StreamConverters.asInputStream(FiniteDuration(5, TimeUnit.SECONDS))
    //					val inputStreamResponse = response.bodyAsSource.runWith(sink)
    //
    //					val seekableInMemoryByteChannel = new SeekableInMemoryByteChannel(inputStreamResponse.readAllBytes())
    //					val zipFile = new ZipFile(seekableInMemoryByteChannel)
    //					val entries = zipFile.getEntries
    //					val archiveEntry = entries.nextElement()
    //					val inputStream = zipFile.getInputStream(archiveEntry)
    //					val bytes = IOUtils.toByteArray(inputStream)
    //					println(new String(bytes))
    //
    //					response.status shouldEqual Status.CREATED
    //				}
    //			}
    //		}
    //
    //		"Cartaporte 2.0" in {
    //			val complementos: ComplementosForm = ComplementosForm(
    //				cartaPorte = Some(cartaPorte)
    //			)
    //
    //			val generateInvoiceForm: GenerateInvoiceForm = GenerateInvoiceForm(
    //				serie = Some("A"),
    //				folio = Some("1"),
    //				lugarExpedicion = "42083",
    //				//				formaPago = Some("02"),
    //				//				metodoPago = Some("PUE"),
    //				moneda = "XXX",
    //				tipoComprobante = "T",
    //				subTotal = "0",
    //				total = "0",
    //				receptor = Some(receptor.copy(
    //					rfc = "CACX7605101P8", usoCfdi = "P01"
    //				)),
    //				conceptos = conceptos.map(cf => cf.copy(
    //					claveProductoServicio = "60121514",
    //					cantidad = "1",
    //					claveUnidad = "X44",
    //					valorUnitario = "0",
    //					descripcion = "Pago",
    //					importe = "0",
    //					impuestos = None
    //				)),
    //				complementos = Some(complementos)
    //			)
    //
    //
    //			WsTestClient.withClient { client =>
    //				val body = Map("email" -> Seq("<EMAIL>"), "password" -> Seq("password"), "appKey" -> Seq(appkey))
    //				val loginResponse: Future[WSResponse] = client.url(appManagerUrl + "/qrapimanager/user/signin").post(body)
    //				val responseLogin = Await.result(loginResponse, Duration.Inf)
    //				println(responseLogin.body)
    //				responseLogin.status shouldEqual Status.OK
    //				val token = Json.parse(responseLogin.body).\("token").as[String]
    //
    //				val reslut: Future[WSResponse] = client.url(empresalitApiUrl + "/companies/invoices").withHttpHeaders(("Authorization", s"Bearer $token")).post(Json.toJson(generateInvoiceForm))
    //				val response = Await.result(reslut, Duration.Inf)
    //
    //				if (response.status != Status.CREATED) {
    //					println(response.body)
    //					fail()
    //				} else {
    //
    //					implicit val system: ActorSystem = ActorSystem("Test")
    //					val sink = StreamConverters.asInputStream(FiniteDuration(5, TimeUnit.SECONDS))
    //					val inputStreamResponse = response.bodyAsSource.runWith(sink)
    //
    //					val seekableInMemoryByteChannel = new SeekableInMemoryByteChannel(inputStreamResponse.readAllBytes())
    //					val zipFile = new ZipFile(seekableInMemoryByteChannel)
    //					val entries = zipFile.getEntries
    //					val archiveEntry = entries.nextElement()
    //					val inputStream = zipFile.getInputStream(archiveEntry)
    //					val bytes = IOUtils.toByteArray(inputStream)
    //					println(new String(bytes))
    //
    //					response.status shouldEqual Status.CREATED
    //				}
    //			}
    //		}
    //
    //		"Nomina" in {
    //			val complementos: ComplementosForm = ComplementosForm(
    //				nomina = Some(nomina)
    //			)
    //
    //			val conceptoNomina: ConceptoForm = ConceptoForm(
    //				claveProductoServicio = "84111505",
    //				cantidad = "1",
    //				claveUnidad = "ACT",
    //				valorUnitario = "300",
    //				descripcion = "Pago de nómina",
    //				importe = "300",
    //				descuento = Some("0")
    //			)
    //
    //			val generateInvoiceForm: GenerateInvoiceForm = GenerateInvoiceForm(
    //				serie = Some("A"),
    //				folio = Some("1"),
    //				lugarExpedicion = "42083",
    //				formaPago = Some("99"),
    //				metodoPago = Some("PUE"),
    //				moneda = "MXN",
    //				tipoComprobante = "N",
    //				subTotal = "150",
    //				total = "1135",
    //				descuento = Some("0"),
    //				receptor = Some(receptor),
    //				conceptos = Seq(conceptoNomina),
    //				//				impuestos = Some(impuestos),
    //				//				tipoCambio = None,
    //				complementos = Some(complementos)
    //			)
    //
    //
    //			WsTestClient.withClient { client =>
    //				val body = Map("email" -> Seq("<EMAIL>"), "password" -> Seq("password"), "appKey" -> Seq(appkey))
    //				val loginResponse: Future[WSResponse] = client.url(appManagerUrl + "/qrapimanager/user/signin").post(body)
    //				val responseLogin = Await.result(loginResponse, Duration.Inf)
    //				println(responseLogin.body)
    //				responseLogin.status shouldEqual Status.OK
    //				val token = Json.parse(responseLogin.body).\("token").as[String]
    //
    //				val reslut: Future[WSResponse] = client.url(empresalitApiUrl + "/companies/invoices").withHttpHeaders(("Authorization", s"Bearer $token")).post(Json.toJson(generateInvoiceForm))
    //				val response = Await.result(reslut, Duration.Inf)
    //
    //				if (response.status != Status.CREATED) {
    //					println(response.body)
    //					fail()
    //				} else {
    //
    //					implicit val system: ActorSystem = ActorSystem("Test")
    //					val sink = StreamConverters.asInputStream(FiniteDuration(5, TimeUnit.SECONDS))
    //					val inputStreamResponse = response.bodyAsSource.runWith(sink)
    //
    //					val seekableInMemoryByteChannel = new SeekableInMemoryByteChannel(inputStreamResponse.readAllBytes())
    //					val zipFile = new ZipFile(seekableInMemoryByteChannel)
    //					val entries = zipFile.getEntries
    //					val archiveEntry = entries.nextElement()
    //					val inputStream = zipFile.getInputStream(archiveEntry)
    //					val bytes = IOUtils.toByteArray(inputStream)
    //					println(new String(bytes))
    //
    //					response.status shouldEqual Status.CREATED
    //				}
    //			}
    //		}

  }

}
