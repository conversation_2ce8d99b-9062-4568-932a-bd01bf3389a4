package com.qrsof.empresalit.invoicing.geteways.sifei

import com.google.inject.{AbstractModule, G<PERSON>ce, Provides}
import com.qrsof.core.certificates.CertificateUtilsImpl
import com.qrsof.empresalit.companies.CompanyCertificate
import com.qrsof.empresalit.invoicing.domain.conceptos.impuestos.ConceptoTraslado
import com.qrsof.empresalit.invoicing.domain.conceptos.{Concepto, ConceptosImpuestos}
import com.qrsof.empresalit.invoicing.domain.impuestos.{Impuestos, Traslado}
import com.qrsof.empresalit.invoicing.domain.invoicing.IssuerInvoicingData
import com.qrsof.empresalit.invoicing.domain.invoicing.v30.GenerateInvoiceRequest
import com.qrsof.empresalit.invoicing.domain.invoicing.xml.{InvoiceXmlGenerator, InvoiceXmlGeneratorModule}
import com.qrsof.empresalit.invoicing.domain.{EmpresalitUtils, EmpresalitUtilsImpl}
import com.qrsof.empresalit.invoicing.generateinvoice.{PrivateKeyFactoryImpl, ReceptorInvoice}
import com.qrsof.empresalit.invoicing.geteways.GatewayInvoiceCanceltaion
import com.qrsof.empresalit.invoicing.geteways.models.CfdiStatusGatewayRequest
import org.apache.commons.compress.archivers.zip.ZipFile
import org.apache.commons.compress.utils.SeekableInMemoryByteChannel
import org.apache.commons.io.IOUtils
import org.mockito.Mockito
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import java.security.PrivateKey

class SifeiPacGatewayTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "SifeiGateway" - {
    val invoiceXmlGenerator = mock[InvoiceXmlGenerator]
    val sifeiConfigurations = mock[SifeiConfigurations]
    // val privateKey = mock[PrivateKey]

    Mockito.when(sifeiConfigurations.idEquipo) thenReturn ("YzBiMGM1NWEtOTc0ZC1kYjllLWQ3N2EtNWNmYmRlZDIzYzQw")
    Mockito.when(sifeiConfigurations.password) thenReturn ("16aad203")
    Mockito.when(sifeiConfigurations.user) thenReturn ("HEPC880430CJ1")
    Mockito.when(sifeiConfigurations.wsdlUrl) thenReturn ("https://devcfdi.sifei.com.mx:8443/SIFEI33/SIFEI?wsdl")
    Mockito.when(sifeiConfigurations.wsdlCancellationUrl) thenReturn ("http://devcfdi.sifei.com.mx:8888/CancelacionSIFEI/Cancelacion?wsdl")

    val sifeiGateway = new SifeiPacGateway(invoiceXmlGenerator, sifeiConfigurations, new CertificateUtilsImpl())

    val urlResource =
      this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/CSD_XOCHILT_CASAS_CHAVEZ_CACX7605101P8_20190528_173544.key")
    val privateKeyContent = urlResource.openStream().readAllBytes()

    val publicResource = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/30001000000400002335.cer")
    val publicKeyContent = publicResource.openStream().readAllBytes()

    "Cancel cfdi" - {
      "when: cancel invoice by uuid" - {
        val invoiceUuid = "9DB49438-982B-4FD9-B295-A3C3E58FAC1A"
        val newuuid = Some("9DB49438-982B-4FD9-B295-A3C3E58FAC1B")
        val reason = Some("01")
        val companyCertificate = CompanyCertificate(
          key = "certificate-key",
          companyKey = "company-key",
          noCertificado = "30001000000400002335",
          privateKey = privateKeyContent,
          password = "12345678a",
          publicKey = publicKeyContent
        )
        val companyRfc = "CACX7605101P8"
        val gatewayInvoiceCanceltaion = GatewayInvoiceCanceltaion(companyCertificate, invoiceUuid, companyRfc, reason, newuuid)

        "then: get response" in {
          val response = sifeiGateway.cancel(gatewayInvoiceCanceltaion)
          response.uuid shouldEqual "9DB49438-982B-4FD9-B295-A3C3E58FAC1A"
          response.acuseCancelacion shouldNot be(null)
        }
      }
    }

    "Get cfdi status" - {
      "given: the cfdi does not exists" - {
        "when: get cfdi status from sat" - {
          val cfdiStatusResponse = sifeiGateway.getCfdiStatus(
            CfdiStatusGatewayRequest(
              uuid = "uuid",
              total = "10",
              sello = "sello",
              rfcReceptor = "receptor",
              rfcEmisor = "emisor"
            )
          )
          "then: get the status from response" in {
            cfdiStatusResponse.status shouldEqual "No Encontrado"
          }
        }
      }

      "given: the cfdi exists" - {
        "when: get cfdi status from sat" - {
          val cfdiStatusResponse = sifeiGateway.getCfdiStatus(
            CfdiStatusGatewayRequest(
              uuid = "38B3C307-B969-45E1-9FAF-E8E89CEEC75E",
              total = "000000000000009520.000000",
              sello = "W+Inng==",
              rfcReceptor = "LOI100617SA2",
              rfcEmisor = "FSR110307GB4"
            )
          )
          "then: get the status from response" in {
            cfdiStatusResponse.status shouldEqual "No Encontrado"
          }
          "and: get status code" in {
            cfdiStatusResponse.statusCode shouldEqual "N - 602: Comprobante no encontrado."
          }

          "and: get cancellable" in {
            cfdiStatusResponse.cancellable shouldEqual ""
          }

          "and: get status cancellation" in {
            cfdiStatusResponse.statusCancellation shouldEqual ""
          }
        }
      }
    }

    "stampInvoice v33" in {
      val injector = Guice.createInjector(
        new InvoiceXmlGeneratorModule(),
        new AbstractModule {
          @Provides
          def getEmpresalitUtils: EmpresalitUtils = {
            return new EmpresalitUtilsImpl()
          }
        }
      )

      val invoiceXmlGeneratorImpl = injector.getInstance(classOf[InvoiceXmlGenerator])

      val sifeiGateway = new SifeiPacGateway(invoiceXmlGeneratorImpl, sifeiConfigurations, new CertificateUtilsImpl())
      val emisorKey = "emisorKey"
      val generateInvoiceRequest = GenerateInvoiceRequest(
        version = "3.3",
        companyKey = emisorKey,
        serie = Some("A"),
        folio = Some("1"),
        lugarExpedicion = "42083",
        formaPago = Some("02"),
        metodoPago = Some("PUE"),
        moneda = "MXN",
        tipoComprobante = "I",
        subTotal = BigDecimal(10),
        total = BigDecimal(11.60),
        receptor = Some(ReceptorInvoice(rfc = "XAXX010101000", usoCfdi = "G01")),
        conceptos = Seq(
          Concepto(
            claveProductoServicio = "81111500",
            cantidad = BigDecimal(1),
            claveUnidad = "E48",
            valorUnitario = BigDecimal(10),
            descripcion = "descripcion",
            importe = BigDecimal(10),
            impuestos = Some(
              ConceptosImpuestos(
                traslados = Seq(ConceptoTraslado(BigDecimal(10), "002", "Tasa", BigDecimal("0.160000"), BigDecimal(1.6)))
              )
            )
          )
        ),
        impuestos = Some(
          Impuestos(
            totalImpuestosTrasladados = Some(BigDecimal(1.6)),
            traslados = Some(
              Seq(
                Traslado("002", "Tasa", BigDecimal("0.160000"), BigDecimal(1.6))
              )
            )
          )
        )
      )
      val urlResourcePk = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/30001000000400002335.cer")
      val publicKeyContent = urlResourcePk.openStream().readAllBytes()
      val privateKeyFactoryImpl = new PrivateKeyFactoryImpl
      val privateKey: PrivateKey = privateKeyFactoryImpl.getInstance(privateKeyContent, "12345678a")

      val issuerInvoicingData = IssuerInvoicingData(
        certificateNumber = "30001000000400002335",
        privateKey = privateKey,
        publicKey = publicKeyContent,
        rfc = "CACX7605101P8",
        name = "XOCHILT CASAS CHAVEZ",
        regimenFiscalClave = "621"
      )
      val stampedXml = sifeiGateway.stampInvoice(issuerInvoicingData, generateInvoiceRequest)

      val seekableInMemoryByteChannel = new SeekableInMemoryByteChannel(stampedXml)
      val zipFile = new ZipFile(seekableInMemoryByteChannel)
      val entries = zipFile.getEntries
      val archiveEntry = entries.nextElement()
      val inputStream = zipFile.getInputStream(archiveEntry)
      val bytes = IOUtils.toByteArray(inputStream)
      println("Stamped CFDI: " + new String(bytes))
    }

  }

}
