<?xml version="1.0" encoding="UTF-8"?>

<included>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date{yyyy-MM-dd HH:mm:ss ZZZZ} [%level] from %logger[%L] in %thread - %message%n%xException</pattern>
        </encoder>
    </appender>

    <logger name="com.qrsof" level="DEBUG"/>
    <logger name="org.apache.hc" level="DEBUG"/>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

</included>
