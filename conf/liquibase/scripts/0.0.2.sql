-- liquibase formatted sql
--changeset ezequiel:mvp01

CREATE TABLE IF NOT EXISTS users
(
    user_key    VARCHAR(36) PRIMARY KEY NOT NULL,
    modified_at  TIMESTAMP             NOT NULL,
    inserted_at  TIMESTAMP             NOT NULL
);

CREATE TABLE IF NOT EXISTS user_companies
(
    user_company_key VARCHAR(36) PRIMARY KEY NOT NULL,
    company_key  CHAR(36) NOT NULL,
    user_key VARCHAR(36) NOT NULL,
    modified_at    TIMESTAMP             NOT NULL,
    inserted_at    TIMESTAMP             NOT NULL,
    CONSTRAINT key_fk FOREIGN KEY (company_key) REFERENCES companies (key) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT user_fk FOREIGN KEY (user_key) REFERENCES users (user_key) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX user_companies_user_key_idx ON user_companies (user_key);
CREATE INDEX user_companies_company_key_idx ON user_companies (company_key);

--changeset ezequiel:mvp02
ALTER TABLE companies
    ADD modified_at TIMESTAMP,
    ADD inserted_at TIMESTAMP;

ALTER TABLE company_certificates
    ADD modified_at TIMESTAMP,
    ADD inserted_at TIMESTAMP;

UPDATE companies
SET modified_at = CURRENT_TIMESTAMP,
    inserted_at = CURRENT_TIMESTAMP;

UPDATE company_certificates
SET modified_at = CURRENT_TIMESTAMP,
    inserted_at = CURRENT_TIMESTAMP;

--changeset ezequiel:mvp03
CREATE TABLE IF NOT EXISTS company_addresses
(
    company_address_key VARCHAR(36) PRIMARY KEY NOT NULL,
    calle      VARCHAR(50)             NOT NULL,
    estado  VARCHAR(30) NOT NULL,
    municipio VARCHAR(30) NOT NULL,
    codigo_postal VARCHAR(5) NOT NULL,
    numero_exterior VARCHAR(5) NOT NULL,
    numero_interior VARCHAR(5) NOT NULL,
    company_key  VARCHAR(36) NOT NULL,
    modified_at    TIMESTAMP             NOT NULL,
    inserted_at    TIMESTAMP             NOT NULL,
    CONSTRAINT key_fk FOREIGN KEY (company_key) REFERENCES companies (key) ON DELETE CASCADE ON UPDATE CASCADE
    );
CREATE INDEX companies_address_company_key_idx ON companies (key);

--changeset ezequiel:mvp04

DROP INDEX companies_address_company_key_idx;
CREATE INDEX company_addresses_company_key_idx ON companies (key);

CREATE TABLE IF NOT EXISTS suppliers
(
    supplier_key VARCHAR(36) PRIMARY KEY NOT NULL,
    name      VARCHAR(100)            NOT NULL,
    regimen_fiscal_clave      VARCHAR(13)            NOT NULL,
    company_key  VARCHAR(36) NOT NULL,
    modified_at    TIMESTAMP             NOT NULL,
    inserted_at    TIMESTAMP             NOT NULL,
    CONSTRAINT key_fk FOREIGN KEY (company_key) REFERENCES companies (key) ON DELETE CASCADE ON UPDATE CASCADE
    );
CREATE INDEX suppliers_company_key_idx ON companies (key);

CREATE TABLE IF NOT EXISTS debts_to_pay
(
    debt_to_pay_key VARCHAR(36) PRIMARY KEY NOT NULL,
    amount      DECIMAL(10, 2)             NOT NULL,
    company_key  VARCHAR(36) NOT NULL,
    supplier_key  VARCHAR(36) NOT NULL,
    modified_at    TIMESTAMP             NOT NULL,
    inserted_at    TIMESTAMP             NOT NULL,
    CONSTRAINT key_fk FOREIGN KEY (company_key) REFERENCES companies (key) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT supplier_key_fk FOREIGN KEY (supplier_key) REFERENCES suppliers (supplier_key) ON DELETE CASCADE ON UPDATE CASCADE
    );
CREATE INDEX debs_to_pay_company_key_idx ON companies (key);

--changeset ezequiel:mvp05

ALTER TABLE debts_to_pay
ADD COLUMN debt_paid_status VARCHAR(50) DEFAULT 'due' NOT NULL

--changeset ezequiel:mvp06
DROP TABLE company_addresses;

CREATE TABLE IF NOT EXISTS addresses
(
    address_key VARCHAR(36) PRIMARY KEY NOT NULL,
    calle      VARCHAR(50)             NOT NULL,
    estado  VARCHAR(30) NOT NULL,
    municipio VARCHAR(30) NOT NULL,
    codigo_postal VARCHAR(5) NOT NULL,
    numero_exterior VARCHAR(5) NOT NULL,
    numero_interior VARCHAR(5) NOT NULL,
    modified_at    TIMESTAMP             NOT NULL,
    inserted_at    TIMESTAMP             NOT NULL
    );

ALTER TABLE companies ADD COLUMN address_key VARCHAR(36);

ALTER TABLE companies ADD CONSTRAINT fk_companies_addresses
        FOREIGN KEY (address_key)
            REFERENCES addresses (address_key) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE suppliers ADD COLUMN address_key VARCHAR(36);

ALTER TABLE suppliers ADD CONSTRAINT fk_suppliers_addresses
    FOREIGN KEY (address_key)
        REFERENCES addresses (address_key) ON DELETE CASCADE ON UPDATE CASCADE;


--changeset ezequiel:mvp07
CREATE TABLE IF NOT EXISTS clients
(
    client_key VARCHAR(36) PRIMARY KEY NOT NULL,
    name      VARCHAR(100)            NOT NULL,
    regimen_fiscal_clave      VARCHAR(13)            NOT NULL,
    rfc VARCHAR(13) NOT NULL,
    company_key  VARCHAR(36) NOT NULL,
    address_key VARCHAR(36) NOT NULL,
    modified_at    TIMESTAMP             NOT NULL,
    inserted_at    TIMESTAMP             NOT NULL,
    CONSTRAINT fk_clients_company FOREIGN KEY (company_key) REFERENCES companies (key) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_clients_addresses FOREIGN KEY (address_key) REFERENCES addresses (address_key) ON DELETE CASCADE ON UPDATE CASCADE
    );
CREATE INDEX clients_company_key_idx ON companies (key);

CREATE TABLE IF NOT EXISTS debts_receivables
(
    debt_receivable_key VARCHAR(36) PRIMARY KEY NOT NULL,
    amount      DECIMAL(10, 2)             NOT NULL,
    company_key  VARCHAR(36) NOT NULL,
    client_key  VARCHAR(36) NOT NULL,
    modified_at    TIMESTAMP             NOT NULL,
    inserted_at    TIMESTAMP             NOT NULL,
    CONSTRAINT fk_debts_receivable_company FOREIGN KEY (company_key) REFERENCES companies (key) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_debts_receivable_clients FOREIGN KEY (client_key) REFERENCES clients (client_key) ON DELETE CASCADE ON UPDATE CASCADE
    );
CREATE INDEX debs_receivables_company_key_idx ON companies (key);
--changeset ezequiel:mvp08
ALTER TABLE debts_receivables
    ADD COLUMN payment_status VARCHAR(50) DEFAULT 'pending' NOT NULL
--changeset jezer:mvp09
ALTER TABLE debts_receivables
    ALTER COLUMN payment_status SET DEFAULT 'pendiente';
ALTER TABLE suppliers
    ADD COLUMN rfc VARCHAR(13) DEFAULT '' NOT NULL;
ALTER TABLE debts_to_pay
    ALTER COLUMN debt_paid_status SET DEFAULT 'pendiente';
AlTER TABLE addresses
    ALTER COLUMN numero_interior DROP NOT NULL;
--changeset jezer:mvp10
CREATE TABLE IF NOT EXISTS empresalit.jobs(
                                              job_key CHAR(36) NOT NULL PRIMARY KEY,
                                              company_key CHAR(36) NOT NULL,
                                              name VARCHAR(36) NOT NULL,
                                              inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
                                              modified_at TIMESTAMP NOT NULL DEFAULT NOW(),
                                              CONSTRAINT company_fk FOREIGN KEY (company_key) REFERENCES companies (key) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX IF NOT EXISTS jobs_company_key_idx ON jobs (company_key);

CREATE TABLE IF NOT EXISTS empresalit.employee(
                                                  employee_key CHAR(36) NOT NULL PRIMARY KEY,
                                                  company_key CHAR(36) NOT NULL,
                                                  name VARCHAR(60) NOT NULL,
                                                  age VARCHAR(3) NOT NULL,
                                                  date TIMESTAMP NOT NULL DEFAULT NOW(),
                                                  job_key CHAR(36) NOT NULL,
                                                  amount DECIMAL(10,2) NOT NULL,
                                                  bonus DECIMAL(10,2) NOT NULL DEFAULT 0.0,
                                                  inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
                                                  modified_at TIMESTAMP NOT NULL DEFAULT NOW(),
                                                  CONSTRAINT job_fk FOREIGN KEY (job_key) REFERENCES jobs (job_key) ON DELETE CASCADE ON UPDATE CASCADE,
                                                  CONSTRAINT company_fk FOREIGN KEY (company_key) REFERENCES companies (key) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX IF NOT EXISTS employee_jobs_key_idx ON employee (job_key);
CREATE INDEX IF NOT EXISTS employee_company_key_idx ON employee (company_key);

--changeset jezer:mvp11
CREATE TABLE IF NOT EXISTS empresalit.stores(
    store_key CHAR(36) NOT NULL PRIMARY KEY,
    company_key CHAR(36) NOT NULL,
    code VARCHAR(20) NOT NULL,
    name VARCHAR(50) NOT NULL,
    country VARCHAR(30) NOT NULL,
    state VARCHAR(40) NOT NULL,
    city VARCHAR(50) NOT NULL,
    direction VARCHAR(50) NOT NULL,
    cellphone VARCHAR(12) NOT NULL,
    inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
    modified_at TIMESTAMP NOT NULL DEFAULT NOW(),
    CONSTRAINT company_fk FOREIGN KEY (company_key) REFERENCES companies (key) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX IF NOT EXISTS stores_company_key_idx ON stores (company_key);

--changeset jezer:mvp12
CREATE TABLE IF NOT EXISTS empresalit.quote
(
    quote_key         VARCHAR(36) PRIMARY KEY NOT NULL,
    store_key         VARCHAR(36)             NOT NULL,
    client_key        VARCHAR(36)             NOT NULL,
    folio             VARCHAR                 NOT NULL,
    date_emission     TIMESTAMP               NOT NULL,
    valid_offer       VARCHAR(3)              NOT NULL,
    email             VARCHAR                 NOT NULL,
    condition         VARCHAR                 NOT NULL,
    extra_information TEXT      DEFAULT 'Sin comentarios',
    time_send         VARCHAR                 NOT NULL,
    keys_products     TEXT[]                  NOT NULL,
    file_quote        VARCHAR                 NULL,
    total_quote       NUMERIC(10, 2)          NOT NULL,
    inserted_at       TIMESTAMP DEFAULT NOW() NOT NULL,
    modified_at       TIMESTAMP DEFAULT NOW() NOT NULL,
    CONSTRAINT store_key_fk FOREIGN KEY (store_key) REFERENCES stores (store_key) on DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT client_key_fk FOREIGN KEY (client_key) REFERENCES clients (client_key) on DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX IF NOT EXISTS quotes_store_key_idx ON quote (store_key);
CREATE INDEX IF NOT EXISTS quotes_client_key_idx ON clients (client_key);

--changeset jezer:mvp13
ALTER TABLE quote
    ADD COLUMN status VARCHAR NOT NULL DEFAULT 'Pendiente';

--changeset antoniv:empl13
ALTER TABLE debts_to_pay ADD COLUMN file_pdf VARCHAR DEFAULT NULL;
