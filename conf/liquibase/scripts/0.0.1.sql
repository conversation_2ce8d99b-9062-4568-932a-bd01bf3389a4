-- liquibase formatted sql

-- changeset carloshe:0.0.1 labels:v0.0.1

CREATE TABLE IF NOT EXISTS empresalit.companies
(
    key  CHAR(36)     NOT NULL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    rfc  VARCHAR(15)  NOT NULL,
    regimen_fiscal_clave  VARCHAR(10)  NOT NULL
);

CREATE INDEX companies_rfc_idx ON empresalit.companies (rfc);


CREATE TABLE IF NOT EXISTS empresalit.company_certificates
(
    key            CHAR(36)    NOT NULL PRIMARY KEY,
    company_key    CHAR(36)    NOT NULL REFERENCES empresalit.companies (key) ON DELETE CASCADE ON UPDATE CASCADE,
    no_certificado VARCHAR(50) NOT NULL,
    private_key    TEXT        NOT NULL,
    public_key     TEXT        NOT NULL
)


-- rollback drop table empresalit.companies;
-- rollback drop table empresalit.company_certificates;

-- changeset carloshe:0.0.2 labels:v0.0.1

ALTER TABLE empresalit.company_certificates ADD COLUMN password VARCHAR(20) not null default '';
-- rollback ALTER TABLE empresalit.company_certificates drop column password;
