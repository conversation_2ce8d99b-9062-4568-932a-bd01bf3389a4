-- liquibase formatted sql
--changeset misael:*******.1
create table if not EXISTS resources
(
    resource_key char(36) primary key,
    name         varchar(10),
    created_at   timestamp default now(),
    updated_at   timestamp default now(),
    company_key  char(36) references companies (key) on delete cascade,
    reference    char(50) not null
);

--rollback DROP TABLE IF EXISTS resources;

--changeset misael:*******.2
create table if not EXISTS equipment_groups
(
    equipment_group_key    char(36) primary key,
    folio                  varchar(10),
    entry_date             date     not null default now(),
    client_key             char(36) not null,
    seller                 char(36) not null,
    carrier                char(36) not null,
    technical_key          char(36)          default null,
    status                 varchar(20)       default null,
    company_key            char(36) references companies (key) on delete cascade,
    signature_resource_key char(36)          default null references resources (resource_key) on delete set null,
    created_at             timestamp         default now(),
    updated_at             timestamp         default now()
);

create table if not EXISTS equipments
(
    equipment_key       char(36) primary key,
    name                varchar(50) not null,
    quantity            int         not null default 1,
    observations        text        not null,
    created_at          timestamp            default now(),
    updated_at          timestamp            default now(),
    equipment_group_key char(36) references equipment_groups (equipment_group_key) on delete cascade
);
--rollback DROP TABLE IF EXISTS equipments,equipment_groups;


--changeset misael:*******.3
create table if not EXISTS equipment_resources
(
    equipment_resource_key char(36) primary key,
    resource_key           char(36) references resources (resource_key) on delete set null,
    equipment_key          char(36) references equipments (equipment_key) on delete set null,
    resource_type          varchar(20),
    created_at             timestamp default now(),
    updated_at             timestamp default now()
);
--rollback DROP TABLE IF EXISTS equipment_resources;

--changeset misael:*******.4
alter table resources
    ALTER COLUMN name TYPE varchar(50);


--changeset misael:*******.5
alter table if EXISTS equipment_resources
    drop column resource_type;
alter table if EXISTS equipment_resources
    rename column equipment_key to task_key;
alter table if EXISTS equipment_resources
    rename column equipment_resource_key to task_resource_key;

--changeset misael:*******.6
alter table equipment_resources
    rename to task_resources;


--changeset misael:*******.7
alter table equipments
    rename column equipment_key to task_key;
alter table equipments
    rename column equipment_group_key to group_task_key;
alter table equipments
    rename column name to title;
alter table equipments
    add column entry_date  date     default now(),
    add column output_date date     default null,
    add column responsible char(36) default null references employee (employee_key) on delete set null;

--changeset misael:*******.8
alter table equipments
    rename to tasks;

--changeset misael:*******.9
alter table equipment_groups
    rename column equipment_group_key to group_task_key;
alter table equipment_groups
    add column output_date date default null;
alter table equipment_groups
    drop column seller,
    drop column carrier,
    drop column technical_key,
    drop column status,
    drop column signature_resource_key;

--changeset misael:*******.10
alter table equipment_groups
    rename to group_tasks;


--changeset misael:*******.11
create table if not EXISTS group_task_resources
(
    group_task_resource_key char(36) primary key,
    resource_key            char(36) references resources (resource_key) on delete set null,
    group_task_key          char(36) references group_tasks (group_task_key) on delete set null,
    created_at              timestamp default now(),
    updated_at              timestamp default now()
);
--rollback DROP TABLE IF EXISTS group_task_resources;

--changeset Luis Gustavo:1.0.3 --comment: create table status. labels:v1.0.3
CREATE TABLE IF NOT EXISTS status
(
    status_key    char(36) primary key not null,
    status        varchar(60),
    order_process integer,
    company_key   char(36) references empresalit.companies (key) on delete cascade,
    created_at    timestamp default now(),
    updated_at    timestamp default now()
);

--rollback DROP TABLE IF EXISTS status

--changeset Luis Gustavo:******* --comment: adds column email to table clients labels:v1.0.5
ALTER TABLE empresalit.clients
    ADD COLUMN email varchar(40) default NULL;

--rollback ALTER TABLE empresalit.clients DROP COLUMN email;

--changeset Luis Gustavo:1.0.1.6 --comment: adds column status_key to tasks table labels:v1.0.6
ALTER TABLE empresalit.tasks
    ADD COLUMN status_key char(36) references empresalit.status (status_key);

--rollback ALTER TABLE empresalit.tasks DROP COLUMN status_key;


--changeset Luis Gustavo:1.0.1.7 --comment: add column employee_number to table employees  labels:v1.0.7
ALTER TABLE empresalit.employee
    ADD COLUMN employee_number varchar(50) default null unique;

--rollback ALTER TABLE empresalit.employee DROP COLUMN employee_number

--changeset misael:*******.12
alter table resources alter column reference TYPE varchar(50);
update resources set reference = TRIM(reference) where reference is not null;


--changeset misael:*******.13
alter table resources add column path varchar(150) default null;


--changeset Luis Gustavo:*******.14 --comment: create table logbook  labels:v*******.14
create table if not exists empresalit.logbook
(
    logbook_key  char(36)     not null
    primary key,
    autor        varchar(255) not null,
    task_key     char(36)     not null
    references empresalit.tasks (task_key) on delete cascade,
    logbook_type varchar(50)  not null,
    payload      text         not null,
    assigned     varchar(255),
    key_attachments text not null default 'Sin archivos adjuntos',
    created_at   varchar(50),
    updated_at   timestamp default now()
);

--rollback DROP TABLE IF EXISTS logbook


--changeset misael:1.0.1.74.1
alter table users add column username varchar(50) default null, add column name varchar(100) default null;

--changeset misael:1.0.1.74.2
create table if not EXISTS employee_resources
(
    employee_resource_key char(36) primary key,
    resource_key           char(36) references resources (resource_key) on delete set null,
    employee_key          char(36) references  employee ( employee_key) on delete set null,
    required          bool default false,
    created_at             timestamp default now(),
    updated_at             timestamp default now()
    );
--rollback drop table if EXISTS employee_resources;

--changeset misael:1.0.1.74.3
alter table if exists employee add column if not exists access_user_key char(36) default null references users(user_key) on delete set null;
--rollback alter table if EXISTS employee drop column if exists access_user_key;


--changeset misael:1.0.1.78.1
alter table if exists employee add column if not exists lastname varchar(50) not null default '', add column if not exists mother_lastname varchar(50) not null default '', add column if not exists fullname varchar(150) not null default '', add column if not exists account_number varchar(20) default null, add column if not exists birth_date timestamp default now();
--rollback alter table if exists employee drop column if exists lastname, drop column if exists mother_lastname, drop column if exists fullname, drop column if exists account_number, drop column if exists birth_date;

--changeset misael:1.0.1.78.2
alter table if exists employee rename column date to entry_date;
--rollback alter table if exists employee rename column entry_date to date;

--changeset misael:1.0.1.78.3
alter table if exists employee add column if not exists area_key char(36) default null;
--rollback alter table if exists employee drop column if exists area_key;

--changeset misael:1.0.1.78.4
alter table if exists employee alter column age drop not null;

--changeset misael:1.0.1.78.5
alter table if exists employee add column email varchar(100) default null;
--rollback alter table if exists employee drop column if exists email;
    
--changeset misael:1.0.1.80.1
create table if not EXISTS seniority_benefits
(
    years_id  smallint primary key,
    vacation_days  int default null,
    start_date  timestamp  default null,
    created_at          timestamp            default now(),
    updated_at          timestamp            default now()
    );
--rollback DROP TABLE IF EXISTS seniority_benefits;

--changeset misael:1.0.1.80.2
create table if not EXISTS inabilities_and_vacations
(
    inability_and_vacation_key  char(36) primary key,
    days  int default 0,
    request_type  varchar(255) not null,
    start_date  date  default now(),
    end_date  date  default null,
    created_at          timestamp            default now(),
    updated_at          timestamp            default now(),
    employee_key char(36) references employee(employee_key) on delete set null,
    registered_by char(36)  references employee(employee_key) on delete set null
    );
--rollback DROP TABLE IF EXISTS inabilities_and_vacations;


--changeset misael:1.0.1.80.3
insert into seniority_benefits(years_id, vacation_days, start_date)
values (1, 12, '2023-01-01'),
       (2, 14, '2023-01-01'),
       (3, 16, '2023-01-01'),
       (4, 18, '2023-01-01'),
       (5, 20, '2023-01-01'),
       (6, 22, '2023-01-01'),
       (11, 24, '2023-01-01'),
       (16, 26, '2023-01-01'),
       (21, 28, '2023-01-01'),
       (26, 30, '2023-01-01');
--rollback truncate TABLE IF EXISTS seniority_benefits;

--changeset misael:1.0.1.80.4
create table if not EXISTS days_to_request_catalog
(
    day_id serial primary key,
    name  varchar(50) not null,
    created_at          timestamp            default now(),
    updated_at          timestamp            default now()
    );
--rollback drop TABLE IF EXISTS days_to_request_catalog;


--changeset Luis Gustavo:1.0.1.80.5 -comment: create table brand_company. labels:v1.0.1.80.5
CREATE TABLE IF NOT EXISTS empresalit.brand_company
(
    company_key       VARCHAR(36) NOT NULL,
    brand_company_key VARCHAR(36) NOT NULL PRIMARY KEY UNIQUE,
    color_palette     TEXT[]      NULL,
    color             VARCHAR(50) NOT NULL,
    created_at        TIMESTAMP   NOT NULL,
    modified_at       TIMESTAMP   NOT NULL,
    logo_resource_key VARCHAR(36) default null,
    CONSTRAINT indexBrandCompany UNIQUE (company_key, brand_company_key),
    CONSTRAINT companyKeyFK FOREIGN KEY (company_key)
    REFERENCES empresalit.companies (key)
    ON UPDATE CASCADE
    ON DELETE CASCADE
    );

--rollback DROP TABLE IF EXISTS empresalit.brand_company;

--changeset misael:1.0.1.80.6
alter table inabilities_and_vacations drop column if exists registered_by;

--changeset misael:1.0.1.80.7 --comment:add column registered_by
alter table inabilities_and_vacations add column if not exists registered_by char(36) default null references users(user_key) on delete set null;
--rollback alter table inabilities_and_vacations drop column if exists registered_by;

--changeset Luis Gustavo:1.0.1.80.8 --comment:delete column employee number. labels:v1.0.1.80.8
ALTER TABLE empresalit.employee DROP COLUMN IF EXISTS employee_number;
--rollback ALTER TABLE empresalit.employee ADD COLUMN employee_number varchar(50) default null unique;

--changeset Luis Gustavo:1.0.1.80.9 --comment: add column employee number. labels:v1.0.1.80.9
ALTER TABLE empresalit.employee ADD COLUMN IF NOT EXISTS employee_number varchar(50) default null;
--rollback ALTER TABLE empresalit.employee DROP COLUMN IF EXISTS employee_number;

--changeset Luis Gustavo:1.0.1.80.10 --comment:Create table public_holidays_and_observances. labels:v1.0.1.80.10
CREATE TABLE IF NOT EXISTS holidays
(
    key        varchar(36)  NOT NULL PRIMARY KEY,
    name       VARCHAR(100) NOT NULL,
    date       DATE         NOT NULL,
    year       INTEGER      NOT NULL,
    created_at TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP    NOT NULL DEFAULT NOW()
    );
--rollback DROP TABLE IF EXISTS public_holidays_and_observances;
--changeset Luis Gustavo:1.0.1.80.11 --comment:Fill table public_holidays_and_observances  labels:v1.0.1.80.11
INSERT INTO empresalit.holidays (key, name, date, year)
VALUES (gen_random_uuid(), 'Año Nuevo', '2025-01-01', 2025),
       (gen_random_uuid(), 'Día de la Constitución', '2025-02-03', 2025),
       (gen_random_uuid(), 'Natalicio de Benito Juárez', '2025-03-17', 2025),
       (gen_random_uuid(), 'Día del Trabajo', '2025-05-01', 2025),
       (gen_random_uuid(), 'Día de la Independencia', '2025-09-16', 2025),
       (gen_random_uuid(), 'Día de la Revolución', '2025-11-17', 2025),
       (gen_random_uuid(), 'Navidad', '2025-12-25', 2025);
--rollback TRUNCATE TABLE public_holidays_and_observances;
