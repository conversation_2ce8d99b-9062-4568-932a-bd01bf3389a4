<?xml version="1.0" encoding="UTF-8"?>

<included>
    <conversionRule conversionWord="coloredLevel" converterClass="play.api.libs.logback.ColoredLevel"/>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/logs/empresalit.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/logs/empresalit-%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date{yyyy-MM-dd HH:mm:ss ZZZZ} [%level] from %logger[%L] in %thread - %message%n%xException
            </pattern>
        </encoder>
    </appender>

    <appender name="ASYNCFILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE"/>
    </appender>

    <logger name="play" level="INFO"/>
    <logger name="application" level="INFO"/>
    <logger name="com.gargoylesoftware.htmlunit.javascript" level="OFF"/>
    <logger name="org.hibernate.validator.messageinterpolation.ParameterMessageInterpolator" level="ERROR"/>

    <root level="INFO">
        <appender-ref ref="ASYNCFILE"/>
    </root>
</included>
