play.http.secret.key="A6y@BKaFIkbJiH^X>r@RB6DScdP5B_1^]k5SHhB]7KLr7KdI2Rd@N:9vL`xANde6"

db.user = postgres
db.user = ${?DB_USER}
db.password = qrsof
db.password = ${?DB_PASSWORD}
db.host = localhost
db.host = ${?DB_HOST}
db.name = qrsof
db.name = ${?DB_NAME}
db.schameName = empresalit
db.schameName = ${?DB_SCHEMA_NAME}
db.numThreads = 2
db.numThreads = ${?DB_NUM_THREADS}


db.postgres = {
  dataSourceClass = "org.postgresql.ds.PGSimpleDataSource"
  properties = {
    serverName = ${db.host}
    databaseName = ${db.name}
    user = ${db.user}
    password = ${db.password}
  }
  numThreads = ${db.numThreads}
}

app.environment = "development"
app.environment = ${?APP_ENVIRONMENT}

oauth.jwksUrl = "https://sandbox-api.authentiline.io/oauth/.well-known/jwks.json"
oauth.jwksUrl = ${?OAUTH_JWKS_URL}

apptack.url = "https://sandbox-api.apptack.io"
apptack.url = ${?APPTACK_URL}

apptack.appKey = "01971e41-da22-73d7-9332-0e8bcd429042"
apptack.appKey = ${?APPTACK_KEY}

apptack.secretKey = "01971e41-da2d-7fcc-b8ce-60ed29b9efb7"
apptack.secretKey = ${?APPTACK_SECRET_KEY}

apptack.relayKey = "01974137-1204-72d0-8dc4-354340c65c4a"
apptack.relayKey = ${?EMAIL_RELAY_KEY}

apptack.templateKey = "01974139-068e-7685-a100-ce0b3f219ab2"
apptack.templateKey = ${?EMAIL_TEMPLATE_KEY}

digitalocean.region=sfo3
digitalocean.region=${?DIGITALOCEAN_REGION}
digitalocean.secretKey=""
digitalocean.secretKey=${?DIGITALOCEAN_SECRET_KEY}
digitalocean.accessKey=""
digitalocean.accessKey=${?DIGITALOCEAN_ACCESS_KEY}
digitalocean.bucketName="sandbox-qrshop"
digitalocean.bucketName=${?DIGITALOCEAN_BUCKET_NAME}




#Play
play.filters.cors {
  allowedOrigins = ["*"]
}
