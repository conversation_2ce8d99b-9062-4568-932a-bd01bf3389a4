# Routes
# This file defines all application routes (Higher priority routes first)
# https://www.playframework.com/documentation/latest/ScalaRouting
# ~~~~

### NoDocs ###
GET           /status                                                                           com.qrsof.empresalit.controllers.ApiController.status()

#Swagger
GET           /api-docs/swagger.json                                                            com.qrsof.empresalit.swagger.SwaggerController.swaggerSpec()
GET           /                                                                                 com.qrsof.empresalit.swagger.SwaggerController.swaggerUi()

#Oauth
POST          /empresalit/oauth/register                                                        com.qrsof.empresalit.controllers.oauth.actions.OauthControllerImpl.register()
POST          /empresalit/oauth/login                                                           com.qrsof.empresalit.controllers.oauth.actions.OauthControllerImpl.login()
GET           /empresalit/oauth/account                                                         com.qrsof.empresalit.controllers.oauth.actions.OauthControllerImpl.getUserData()

#ViewMainContainerController
GET           /views/main-layout/getCompanies                                                   com.qrsof.empresalit.views.maincontainer.actions.ViewMainContainerControllerImpl.getUserCompanies()

#ViewOnboardingController
POST          /views/onboarding                                                                 com.qrsof.empresalit.views.onboarding.actions.ViewOnboardingControllerImpl.createNewCompany


#CompanyController
GET           /company/:companyKey                                                              com.qrsof.empresalit.actions.controllers.CompanyControllerImpl.getCompanyData(companyKey)
PUT           /company/:companyKey                                                              com.qrsof.empresalit.actions.controllers.CompanyControllerImpl.updateCompanyData(companyKey)

#ViewQuoteController
POST          /views/main-container/:company_key/quote                                          com.qrsof.empresalit.views.maincontainer.quote.actions.controllers.ViewQuoteControllerImpl.addQuote(company_key)
GET           /views/main-container/:company_key/quote                                          com.qrsof.empresalit.views.maincontainer.quote.actions.controllers.ViewQuoteControllerImpl.getQuote(company_key, page:Int?=0, pageSize:Int?=10, searchBar: Option[String]?=None, dateSearch: Option[String]?=None, store_key: Option[String]?=None, client_key: Option[String]?=None)
GET           /views/main-container/:company_key/allQuote                                       com.qrsof.empresalit.views.maincontainer.quote.actions.controllers.ViewQuoteControllerImpl.getAllQuote(company_key)
PUT           /views/main-container/:company_key/quote/:quote_key                               com.qrsof.empresalit.views.maincontainer.quote.actions.controllers.ViewQuoteControllerImpl.updateQuote(quote_key, company_key)
DELETE        /views/main-container/:company_key/quote/:quote_key                               com.qrsof.empresalit.views.maincontainer.quote.actions.controllers.ViewQuoteControllerImpl.deleteQuote(quote_key, company_key)
PUT           /views/main-container/:company_key/markAsFacture/:quote_key                       com.qrsof.empresalit.views.maincontainer.quote.actions.controllers.ViewQuoteControllerImpl.markAsFactureQuote(quote_key, company_key)
PUT           /views/main-container/:company_key/markAsReject/:quote_key                        com.qrsof.empresalit.views.maincontainer.quote.actions.controllers.ViewQuoteControllerImpl.markAsRejectQuote(quote_key, company_key)
PUT           /views/main-container/:company_key/markAsPending/:quote_key                       com.qrsof.empresalit.views.maincontainer.quote.actions.controllers.ViewQuoteControllerImpl.markAsPendingQuote(quote_key, company_key)

#ViewStoreController
POST          /views/main-container/:company_key/store                                          com.qrsof.empresalit.views.maincontainer.stores.actions.controllers.ViewStoreControllerImpl.addStore(company_key)
GET           /views/main-container/:company_key/store                                          com.qrsof.empresalit.views.maincontainer.stores.actions.controllers.ViewStoreControllerImpl.getStore(company_key, page:Int?=0, pageSize:Int?=10, searchBar: Option[String]?=None)
PUT           /views/main-container/:company_key/store/:store_key                               com.qrsof.empresalit.views.maincontainer.stores.actions.controllers.ViewStoreControllerImpl.updateStore(company_key, store_key)
DELETE        /views/main-container/:company_key/store/:store_key                               com.qrsof.empresalit.views.maincontainer.stores.actions.controllers.ViewStoreControllerImpl.deleteStore(company_key, store_key)

#ViewsClientsController
GET           /views/main-container/:company_key/clients                                        com.qrsof.empresalit.views.maincontainer.clients.actions.controllers.ViewsClientsControllerImpl.getClients(company_key, page:Int?=0, pageSize:Int?=10, clientKey:Option[String]?=None, searchBar:Option[String]?=None)
GET           /views/main-container/:company_key/allClients                                     com.qrsof.empresalit.views.maincontainer.clients.actions.controllers.ViewsClientsControllerImpl.getClientsAll(company_key)
POST          /views/main-container/:company_key/clients                                        com.qrsof.empresalit.views.maincontainer.clients.actions.controllers.ViewsClientsControllerImpl.newClient(company_key)
DELETE        /views/main-container/:company_key/clients/:client_key                            com.qrsof.empresalit.views.maincontainer.clients.actions.controllers.ViewsClientsControllerImpl.deleteClient(company_key, client_key)

#ViewDebtsReceivablesController
POST          /views/main-container/:company_key/debts-receivable                               com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.controllers.ViewDebtsReceivablesControllerImpl.newDebtReceivable(company_key)
GET           /views/main-container/:company_key/debts-receivable                               com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.controllers.ViewDebtsReceivablesControllerImpl.getDebtsReceivable(company_key, page: Int?=0, pageSize: Int?=10, clientKey: Option[String]?=None, paymentStatus: Option[String]?=None, startDate: Option[String]?=None, endDate: Option[String]?=None, orderBy: Option[String]?=None, searchBar: Option[String]?=None)
DELETE        /views/main-container/:company_key/debts-receivable/:debtToPay_Key                com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.controllers.ViewDebtsReceivablesControllerImpl.deleteDebtReceivable(company_key, debtToPay_Key)
PUT           /views/main-container/:company_key/debts-receivable/:debtToPay_Key                com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.controllers.ViewDebtsReceivablesControllerImpl.markAsPaid(company_key, debtToPay_Key)

#ViewSuppliersController
GET           /views/main-container/:company_key/suppliers                                      com.qrsof.empresalit.views.maincontainer.suppliers.actions.controllers.ViewSuppliersControllerImpl.getSuppliers(company_key, page: Int?=0, pageSize: Int?=10, supplierKey: Option[String]?=None, searchBar: Option[String]?=None)
GET           /views/main-container/:company_key/allSuppliers                                   com.qrsof.empresalit.views.maincontainer.suppliers.actions.controllers.ViewSuppliersControllerImpl.getAllSuppliers(company_key)
POST          /views/main-container/:company_key/suppliers                                      com.qrsof.empresalit.views.maincontainer.suppliers.actions.controllers.ViewSuppliersControllerImpl.addNewSupplier(company_key)

#ViewDebtsToPayController
POST          /views/main-container/:company_key/debts-to-pay                                   com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.controllers.ViewDebtsToPayControllerImpl.addDebtToPay(company_key)
PUT           /views/main-container/:company_key/debts-to-pay/:debtToPayKey                     com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.controllers.ViewDebtsToPayControllerImpl.markAsPaidDebtToPay(company_key, debtToPayKey)
GET           /views/main-container/:company_key/debts-to-pay                                   com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.controllers.ViewDebtsToPayControllerImpl.getDebtsToPay(company_key, page: Int?=0, pageSize: Int?=10, supplierKey: Option[String]?=None, paymentStatus: Option[String]?=None, startDate: Option[String]?=None, endDate: Option[String]?=None, orderBy: Option[String]?=None, searchBar: Option[String]?=None)
DELETE        /views/main-container/:company_key/debts-to-pay/:debtToPayKey                     com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.controllers.ViewDebtsToPayControllerImpl.deleteDebtToPay(company_key, debtToPayKey)
DELETE        /prueba                                                                           com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.controllers.ViewDebtsToPayControllerImpl.pruebaruta

#ViewEmployeeController
POST          /views/main-container/:companyKey/employee                                        com.qrsof.empresalit.views.maincontainer.employee.actions.controllers.ViewEmployeeControllerImpl.addEmployee(companyKey)
GET           /views/main-container/:company_key/employee                                       com.qrsof.empresalit.views.maincontainer.employee.actions.controllers.ViewEmployeeControllerImpl.getEmployee(company_key, job_key: Option[String]?=None, page: Int?=0, pageSize: Int?=10, searchBar: Option[String]?=None)
DELETE        /views/main-container/:company_key/employee/:employee_key                         com.qrsof.empresalit.views.maincontainer.employee.actions.controllers.ViewEmployeeControllerImpl.deleteEmployee(company_key, employee_key)
PUT           /views/main-container/:companyKey/employee/:employeeKey                           com.qrsof.empresalit.views.maincontainer.employee.actions.controllers.ViewEmployeeControllerImpl.updateEmployee(companyKey, employeeKey)
GET           /views/main-container/:companyKey/employee/:employeeKey/resources                 com.qrsof.empresalit.views.maincontainer.employee.actions.controllers.ViewEmployeeControllerImpl.getEmployeeResources(companyKey, employeeKey)
GET           /views/main-container/:companyKey/employee/:employeeKey/data                      com.qrsof.empresalit.views.maincontainer.employee.actions.controllers.ViewEmployeeControllerImpl.getEmployeeData(companyKey, employeeKey)

#ViewJobController
GET           /views/main-container/:company_key/job                                            com.qrsof.empresalit.views.maincontainer.job.actions.controllers.ViewJobControllerImpl.getJobs(company_key)
POST          /views/main-container/:company_key/job                                            com.qrsof.empresalit.views.maincontainer.job.actions.controllers.ViewJobControllerImpl.addJob(company_key)
DELETE        /views/main-container/:company_key/job/:job_key                                   com.qrsof.empresalit.views.maincontainer.job.actions.controllers.ViewJobControllerImpl.deleteJob(company_key, job_key)
PUT           /views/main-container/:company_key/job/:job_key                                   com.qrsof.empresalit.views.maincontainer.job.actions.controllers.ViewJobControllerImpl.updateJob(company_key, job_key)

#InvoicingController
GET           /companies/invoices/qrcode                                                        com.qrsof.empresalit.controllers.invoices.InvoicingControllerImpl.generateQrCodeByParameters(id: String?="", re: String?="", rr: Option[String]?=None, nr: Option[String]?=None, tt: String?="", fe: String?="")
GET           /companies/invoices/:uuid                                                         com.qrsof.empresalit.controllers.invoices.InvoicingControllerImpl.retrieveInvoiceMetadata(uuid)
DELETE        /companies/invoices/:uuid                                                         com.qrsof.empresalit.controllers.invoices.InvoicingControllerImpl.cancelInvoice(uuid)
POST          /companies/invoices                                                               com.qrsof.empresalit.controllers.invoices.InvoicingControllerImpl.generateInvoice
GET           /companies/invoices/:uuid/status                                                  com.qrsof.empresalit.controllers.invoices.InvoicingControllerImpl.getInvoiceStatus(uuid, rfcReceptor: String?="", total: String?="", sello: String?="")

#CompaniesController
POST          /companies/certificates                                                           com.qrsof.empresalit.controllers.companies.CompaniesControllerImpl.saveNewCertificate

#ViewCompaniesController
GET           /views/main-container/:company_key/companies                                      com.qrsof.empresalit.views.maincontainer.companies.actions.controllers.ViewCompaniesControllerImpl.getCompanies(company_key)

#ReceptionOfEquipmentsController
GET           /views/main-container/:companyKey/sales/reception-of-equipments/clients           com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.ViewReceptionOfEquipmentsController.getClientsOfCompany(companyKey)
GET           /views/main-container/:companyKey/sales/reception-of-equipments/carriers          com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.ViewReceptionOfEquipmentsController.getCarriersOfCompany(companyKey)
POST          /views/main-container/:companyKey/sales/reception-of-equipments/new               com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.ViewReceptionOfEquipmentsController.newReceptionOfEquipments(companyKey)
GET           /views/main-container/:companyKey/sales/reception-of-equipments                   com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.ViewReceptionOfEquipmentsController.getReceptionOfEquipments( companyKey, pageNumber: Int?=0, pageSize: Int?=10)

#KanbanTasksAnsStatusController
GET           /views/main-container/:companyKey/kanbanboard                                     com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.ViewKanbanBoardController.getAllTasksAndStatus(companyKey)
GET           /views/main-container/:companyKey/kanbanboardfilter                               com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.ViewKanbanBoardController.getTasksByFilter(companyKey, term: Option[String]?=None)
POST          /views/main-container/:companyKey/new-logbook                                     com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.ViewKanbanBoardController.newLogbook(companyKey)
GET           /views/main-container/:companyKey/kanbanboard/task/:taskKey                       com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.ViewKanbanBoardController.getTaskByConfirmation(companyKey, taskKey)
POST          /views/main-container/:companyKey/kanbanboard/task/:taskKey/confirmation          com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.ViewKanbanBoardController.taskConfirmation(companyKey, taskKey)
GET           /views/main-container/:companyKey/kanbanboard/tasks/:taskKey/logbook              com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.ViewKanbanBoardController.getLogbookAndResources(companyKey, taskKey)
PATCH         /views/main-container/:companyKey/kanbanboard/status/:statusKey/order             com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.ViewKanbanBoardController.changeStatusOrder(companyKey, statusKey)
PATCH         /views/main-container/:companyKey/kanbanboard/tasks/:taskKey                      com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.ViewKanbanBoardController.assignTask(companyKey: String, taskKey: String)
GET           /views/main-container/:companyKey/kanbanboard/assigned                            com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.ViewKanbanBoardController.getAllTasksAndStatusAssigned(companyKey)

#Resources
GET           /company/:companyKey/resources/:resourceKey                                       com.qrsof.empresalit.controllers.resources.ResourcesController.getResourceByCompanyKeyAndResourceKey(companyKey, resourceKey)

#PresenteeismController
POST          /views/main-container/:companyKey/presenteeism/new                                com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.saveEmployeePresenteeism(companyKey)
POST          /views/main-container/:companyKey/presenteeisms/new                               com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.saveEmployeePresenteeisms(companyKey)
PUT           /views/main-container/:companyKey/presenteeism/:presenteeismKey                   com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.updateEmployeePresenteeismByKey(companyKey, presenteeismKey)
GET           /views/main-container/:companyKey/presenteeism/:presenteeismKey/data              com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.getPresenteeismByKey(companyKey, presenteeismKey)
GET           /views/main-container/:companyKey/presenteeism/:employeeKey                       com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.getEmployeePresenteeismByEmployeeKey(companyKey, employeeKey)
GET           /views/main-container/:companyKey/presenteeisms                                   com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.getEmployeePresenteeismByCompanyKey(companyKey, page:Int?=0, pageSize:Int?=10)
GET           /views/main-container/:companyKey/employees/presenteeisms                         com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.getEmployeesWithPresenteeismByCompanyKey(companyKey, page:Int?=0, pageSize:Int?=10)
GET           /views/main-container/:companyKey/employees-and-presenteeisms                     com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.getEmployeesAndPresenteeismByCompanyKey(companyKey, pageNumber:Int?=0, pageSize:Int?=10)
POST          /views/main-container/presenteeism/validate-range-dates-excluding-holidays        com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.validateRangeDatesExcludingHolidays()
DELETE        /views/main-container/:companyKey/presenteeism/:presenteeismKey                 com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.deletePresenteeism(companyKey, presenteeismKey)

#HolyDaysController
GET           /views/main-container/holidays                                                    com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismControllerImpl.getHoliDays()
#INVOICING
###
#  summary: Invoice generation
#  description: This end point will generate an invoce
#  operationId: generate
#  tags:
#    - invoice
#  requestBody:
#    description: Request invoice generation
#    required: true
#    content:
#      'application/json':
#        schema:
#          $ref: '#/components/schemas/controllers.invoices.forms.GenerateInvoiceForm'
#  responses:
#    201:
#      description: Invoice was generated
#    400:
#      description: Bad request
#    401:
#      description: Not authenticated
#    403:
#      description: Not authorized
#    500:
#      description: Unexpected error
#  security:
#   - user_auth: []

### NoDocs ###
GET           /docs/swagger-ui/*file                                                            controllers.Assets.at(path:String="/public/lib/swagger-ui", file:String)

### NoDocs ###
# Map static resources from the /public folder to the /assets URL path
GET           /assets/*file                                                                     controllers.Assets.versioned(path="/public", file: Asset)
