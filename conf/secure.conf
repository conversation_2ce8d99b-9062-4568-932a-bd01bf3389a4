# Set up Play for HTTPS and locked down allowed hosts.
# Nothing in here is required for REST, but it's a good default.
play {
  http {
    cookies.strict = true

    session.secure = true
    session.httpOnly = true

    flash.secure = true
    flash.httpOnly = true

    forwarded.trustedProxies = ["::1", "127.0.0.1"]
  }

  i18n {
    langCookieSecure = true
    langCookieHttpOnly = true
  }

  filters {
    csrf {
      cookie.secure = true
    }

    hosts {
      allowed = ["localhost:9443", "localhost:9000"]
    }

    hsts {
      maxAge = 1 minute # don't interfere with other projects
      secureHost = "localhost"
      securePort = 9443
    }
  }
}
