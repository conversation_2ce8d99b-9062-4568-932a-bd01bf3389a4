import com.google.inject.AbstractModule
import com.qrsof.empresalit.{ApiModule, AppConfigurations, AppEnvironment, BusinessModule, DataModule}
import net.codingwell.scalaguice.ScalaModule
import org.apache.pekko.actor.typed.ActorSystem
import org.apache.pekko.actor.typed.javadsl.Behaviors
import play.api.{Configuration, Environment}

class Module(playEnvironment: Environment, configuration: Configuration) extends AbstractModule with ScalaModule {

  private val conf = new AppConfigurationsImpl(configuration)

  private val actorSystem: ActorSystem[Nothing] = ActorSystem(Behaviors.empty, "empresalit-api")

  override def configure(): Unit = {
    bind[ActorSystem[Nothing]].toInstance(actorSystem)
    bind[AppEnvironment].toInstance(conf.environment)
    bind[AppConfigurations].toInstance(conf)

    install(new ApiModule(conf))
    install(new BusinessModule(conf, Some(conf.clientUrl)))
    install(new DataModule(conf))
  }
}
