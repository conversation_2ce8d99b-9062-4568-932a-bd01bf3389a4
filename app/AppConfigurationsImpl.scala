import com.qrsof.empresalit.AppEnvironment.*
import com.qrsof.empresalit.domain.storage.digitalocean.DigitalOceanConfigs
import com.qrsof.empresalit.{AppConfigurations, AppEnvironment, OauthConfigs}
import play.api.Configuration

class AppConfigurationsImpl(configuration: Configuration) extends AppConfigurations {

  override def environment: AppEnvironment =
    configuration.get[String]("app.environment") match {
      case "development" => Development
      case "sandbox"     => Sandbox
      case "production"  => Production
      case "test"        => Test
      case "ci"          => CI
      case _             => throw new RuntimeException("Environment not found")
    }

  override def appPort: String = getAppPort.toString

  override def oauthConfigs: OauthConfigs = {
    new OauthConfigs {
      override def jwksUrl: String = configuration.get[String]("oauth.jwksUrl")
    }
  }

  override def appHost: String = {
    getAppHost
  }

  private def getAppHost: String = {
    val envPort = System.getenv("APP_HOST")
    val propPort = System.getProperty("APP_HOST")
    if (envPort != null) {
      envPort
    } else if (propPort != null) {
      propPort
    } else {
      s"http://localhost:$appPort"
    }
  }

  private def getAppPort: Int = {
    val envPort = System.getenv("APP_PORT")
    val propPort = System.getProperty("APP_PORT")
    if (envPort != null) {
      envPort.toInt
    } else if (propPort != null) {
      propPort.toInt
    } else {
      8080
    }
  }

  override def listenAddresses: String = {
    val listenAddressEnv = System.getenv("APP_LISTEN_ADDRESS")
    val listenAddressProp = System.getProperty("APP_LISTEN_ADDRESS")
    if (listenAddressEnv != null) {
      listenAddressEnv
    } else if (listenAddressProp != null) {
      listenAddressProp
    } else {
      "localhost"
    }
  }

  override def dbSchemaName: String = configuration.get[String]("db.schameName")

  override def digitalOceanConfigs: DigitalOceanConfigs = {
    DigitalOceanConfigs(
      region = configuration.get[String]("digitalocean.region"),
      secretKey = configuration.get[String]("digitalocean.secretKey"),
      accessKey = configuration.get[String]("digitalocean.accessKey"),
      bucketName = configuration.get[String]("digitalocean.bucketName"),
      schema = configuration.get[String]("db.schameName")
    )
  }

  override def apptackKey: String = configuration.get[String]("apptack.appKey")

  override def apptackSecret: String = configuration.get[String]("apptack.secretKey")

  override def clientUrl: String = configuration.get[String]("apptack.url")
}
