package com.qrsof.empresalit

import com.google.inject.{AbstractModule, Provides}
import com.qrsof.empresalit.actions.controllers.{CompanyController, CompanyControllerImpl}
import com.qrsof.empresalit.actions.ss3.{StorageCertificatesController, StorageCertificatesControllerImpl}
import com.qrsof.empresalit.controllers.companies.{CompaniesController, CompaniesControllerImpl}
import com.qrsof.empresalit.controllers.invoices.forms.v40.{FormsMapper, FormsMapperImpl}
import com.qrsof.empresalit.controllers.invoices.v33.{GenerateInvoiceV33, GenerateinvoiceV33Impl}
import com.qrsof.empresalit.controllers.invoices.v40.{GenerateInvoiceV40, GenerateInvoiceV40Impl}
import com.qrsof.empresalit.controllers.invoices.{InvoicingController, InvoicingControllerImpl}
import com.qrsof.empresalit.controllers.oauth.actions.{Oauth<PERSON><PERSON><PERSON>er, OauthControllerImpl}
import com.qrsof.empresalit.controllers.oauth.{OauthActions, OauthActionsImpl}
import com.qrsof.empresalit.controllers.resources.{ResourcesController, ResourcesControllerImpl}
import com.qrsof.empresalit.swagger.{SwaggerController, SwaggerControllerImpl}
import com.qrsof.empresalit.views.maincontainer.actions.{ViewMainContainerController, ViewMainContainerControllerImpl}
import com.qrsof.empresalit.views.maincontainer.clients.actions.controllers.{ViewsClientsController, ViewsClientsControllerImpl}
import com.qrsof.empresalit.views.maincontainer.companies.actions.controllers.{ViewCompaniesController, ViewCompaniesControllerImpl}
import com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.controllers.{ViewDebtsReceivablesController, ViewDebtsReceivablesControllerImpl}
import com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.controllers.{ViewDebtsToPayController, ViewDebtsToPayControllerImpl}
import com.qrsof.empresalit.views.maincontainer.employee.actions.controllers.{ViewEmployeeController, ViewEmployeeControllerImpl}
import com.qrsof.empresalit.views.maincontainer.job.actions.controllers.{ViewJobController, ViewJobControllerImpl}
import com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.{ViewKanbanBoardController, ViewKanbanBoardControllerImpl}
import com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.{ViewPresenteeismController, ViewPresenteeismControllerImpl}
import com.qrsof.empresalit.views.maincontainer.quote.actions.controllers.{ViewQuoteController, ViewQuoteControllerImpl}
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.{ViewReceptionOfEquipmentsController, ViewReceptionOfEquipmentsControllerImpl}
import com.qrsof.empresalit.views.maincontainer.stores.actions.controllers.{ViewStoreController, ViewStoreControllerImpl}
import com.qrsof.empresalit.views.maincontainer.suppliers.actions.controllers.{ViewSuppliersController, ViewSuppliersControllerImpl}
import com.qrsof.empresalit.views.onboarding.actions.{ViewOnboardingController, ViewOnboardingControllerImpl}
import com.qrsof.jwt.validation.*
import jakarta.inject.Singleton
import net.codingwell.scalaguice.ScalaModule

class ApiModule(appConfigurations: AppConfigurations) extends AbstractModule with ScalaModule {

  override def configure(): Unit = {
    // CONTROLLERS
    bind[InvoicingController].to[InvoicingControllerImpl].in(classOf[Singleton])
    bind[GenerateInvoiceV33].to[GenerateinvoiceV33Impl].in(classOf[Singleton])
    bind[GenerateInvoiceV40].to[GenerateInvoiceV40Impl].in(classOf[Singleton])
    bind[CompaniesController].to[CompaniesControllerImpl].in(classOf[Singleton])
    bind[FormsMapper].to[FormsMapperImpl].in(classOf[Singleton])
    bind[OauthController].to[OauthControllerImpl].in(classOf[Singleton])
    bind[OauthActions].to[OauthActionsImpl].in(classOf[Singleton])
    bind[CompanyController].to[CompanyControllerImpl].in(classOf[Singleton])
    bind[StorageCertificatesController].to[StorageCertificatesControllerImpl].in(classOf[Singleton])
    bind[ViewOnboardingController].to[ViewOnboardingControllerImpl].in(classOf[Singleton])
    bind[ViewMainContainerController].to[ViewMainContainerControllerImpl].in(classOf[Singleton])
    bind[ViewDebtsToPayController].to[ViewDebtsToPayControllerImpl].in(classOf[Singleton])
    bind[ViewSuppliersController].to[ViewSuppliersControllerImpl].in(classOf[Singleton])
    bind[ViewDebtsReceivablesController].to[ViewDebtsReceivablesControllerImpl].in(classOf[Singleton])
    bind[ViewsClientsController].to[ViewsClientsControllerImpl].in(classOf[Singleton])
    bind[ViewCompaniesController].to[ViewCompaniesControllerImpl].in(classOf[Singleton])
    bind[ViewEmployeeController].to[ViewEmployeeControllerImpl].in(classOf[Singleton])
    bind[ViewJobController].to[ViewJobControllerImpl].in(classOf[Singleton])
    bind[ViewStoreController].to[ViewStoreControllerImpl].in(classOf[Singleton])
    bind[ViewQuoteController].to[ViewQuoteControllerImpl].in(classOf[Singleton])
    bind[ViewKanbanBoardController].to[ViewKanbanBoardControllerImpl].in(classOf[Singleton])
    bind[SwaggerController].to[SwaggerControllerImpl].in(classOf[Singleton])
    bind[ViewReceptionOfEquipmentsController].to[ViewReceptionOfEquipmentsControllerImpl].in(classOf[Singleton])
    bind[ResourcesController].to[ResourcesControllerImpl].in(classOf[Singleton])
    bind[ViewPresenteeismController].to[ViewPresenteeismControllerImpl].in(classOf[Singleton])
  }

//  @Provides
//  def getSifeiConfigurations(): SifeiConfigurations = {
//    appConfigurations.sifeiConfigurations
//  }

  @Provides
  def jwkConfigs(): JwksConfigs = {
    new JwksConfigs {
      override def jwkUlr: String = appConfigurations.oauthConfigs.jwksUrl
    }
  }

}
