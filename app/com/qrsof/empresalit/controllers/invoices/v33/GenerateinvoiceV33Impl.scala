package com.qrsof.empresalit.controllers.invoices.v33

import com.qrsof.empresalit.controllers.invoices.forms.*
import com.qrsof.empresalit.invoicing.generateinvoice.GenerateInvoiceAction
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}


@Singleton()
class GenerateinvoiceV33Impl @Inject() (
    generateInvoiceAction: GenerateInvoiceAction
)() extends GenerateInvoiceV33 {

  val logger: Logger = LoggerFactory.getLogger(classOf[GenerateinvoiceV33Impl])

  override def execute(generateInvoiceForm: GenerateInvoiceForm, userKey: String): Array[Byte] = {
    null
  }

}
