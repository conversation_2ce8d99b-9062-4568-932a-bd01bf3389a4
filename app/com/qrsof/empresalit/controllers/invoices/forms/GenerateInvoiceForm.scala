package com.qrsof.empresalit.controllers.invoices.forms

import com.qrsof.empresalit.invoicing.generateinvoice.ReceptorInvoice

case class GenerateInvoiceForm(
                                serie: Option[String] = None,
                                folio: Option[String] = None,
                                lugarExpedicion: String,
                                formaPago: Option[String] = None,
                                metodoPago: Option[String] = None,
                                moneda: String,
                                tipoComprobante: String,
                                tipoCambio: Option[String] = None,
                                condicionesDePago: Option[String] = None,
                                subTotal: String,
                                total: String,
                                descuento: Option[String] = None,
                                receptor: Option[ReceptorInvoice] = None,
                                conceptos: Seq[ConceptoForm] = Nil,
                                impuestos: Option[ImpuestosForm] = None,
                                complementos: Option[ComplementosForm] = None
                              )
