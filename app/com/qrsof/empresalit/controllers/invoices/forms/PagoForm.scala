package com.qrsof.empresalit.controllers.invoices.forms

case class PagoForm(
                     fechaPago: String,
                     formaDePagoP: String,
                     monedaP: String,
                     tipoCambioP: Option[String] = None,
                     monto: String,
                     numOperacion: Option[String] = None,
                     rfcEmisorCtaOrd: Option[String] = None,
                     nomBancoOrdExt: Option[String] = None,
                     ctaOrdenante: Option[String] = None,
                     rfcEmisorCtaBen: Option[String] = None,
                     ctaBeneficiario: Option[String] = None,
                     tipoCadPago: Option[String] = None,
                     certPago: Option[String] = None,
                     cadPago: Option[String] = None,
                     selloPago: Option[String] = None,
                     doctosRelacionesdos: Seq[DoctoRelacionadoForm] = Nil,
                     impuestos: Option[ImpuestosForm] = None
                   )
