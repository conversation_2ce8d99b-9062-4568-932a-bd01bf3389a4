package com.qrsof.empresalit.controllers.invoices.forms

import com.qrsof.empresalit.invoicing.domain.complementos.Complementos
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.figura.transporte.{DomicilioFigura, PartesTransporte, TiposFigura}
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.mercancias.*
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.ubicaciones.{Domicilio, Ubicacion}
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.{CartaPorte, Figuratransporte, Mercancias, Ubicaciones}
import com.qrsof.empresalit.invoicing.domain.complementos.nomina.*
import com.qrsof.empresalit.invoicing.domain.complementos.pagos.{DoctoRelacionado, Pago, RecepcionPago}
import com.qrsof.empresalit.invoicing.domain.conceptos.impuestos.{ConceptoRetencion, ConceptoTraslado}
import com.qrsof.empresalit.invoicing.domain.conceptos.{Concepto, ConceptosImpuestos}
import com.qrsof.empresalit.invoicing.domain.impuestos.{Impuestos, Retencion, Traslado}

package object v33 {

  def getConceptos(conceptos: Seq[ConceptoForm]): Seq[Concepto] = {
    conceptos.map(conceptoForm => {
      Concepto(
        claveProductoServicio = conceptoForm.claveProductoServicio,
        noIdentificacion = conceptoForm.noIdentificacion,
        cantidad = BigDecimal(conceptoForm.cantidad),
        claveUnidad = conceptoForm.claveUnidad,
        unidad = conceptoForm.unidad,
        descripcion = conceptoForm.descripcion,
        valorUnitario = BigDecimal(conceptoForm.valorUnitario),
        importe = BigDecimal(conceptoForm.importe),
        descuento = conceptoForm.descuento.map(BigDecimal(_)),
        impuestos = getConceptoImpuestos(conceptoForm),
        informacionAduanera = conceptoForm.informacionAduanera.getOrElse(Nil),
        cuentaPredial = conceptoForm.cuentaPredial
      )
    })
  }

  def getConceptoImpuestos(conceptoForm: ConceptoForm): Option[ConceptosImpuestos] = {
    conceptoForm.impuestos.map(conceptoImpuestosForm => {
      ConceptosImpuestos(
        traslados = getImpuestoConceptoTraslados(conceptoImpuestosForm),
        retenciones = getImpuestosConceptoRetencion(conceptoImpuestosForm)
      )
    })
  }

  def getImpuestoConceptoTraslados(conceptoImpuestosForm: ConceptosImpuestosForm): Seq[ConceptoTraslado] = {
    conceptoImpuestosForm.traslados.map(conceptoTrasladoForm => {
      ConceptoTraslado(
        base = BigDecimal(conceptoTrasladoForm.base),
        impuesto = conceptoTrasladoForm.tipoImpuesto,
        tipoFactor = conceptoTrasladoForm.tipoFactor,
        tasaCuota = BigDecimal(conceptoTrasladoForm.tasaCuota),
        importe = BigDecimal(conceptoTrasladoForm.importe)
      )
    })
  }

  def getImpuestosConceptoRetencion(conceptoImpuestosForm: ConceptosImpuestosForm): Seq[ConceptoRetencion] = {
    conceptoImpuestosForm.retenciones.map(conceptoRetencionForm => {
      ConceptoRetencion(
        base = BigDecimal(conceptoRetencionForm.base),
        impuesto = conceptoRetencionForm.tipoImpuesto,
        tipoFactor = conceptoRetencionForm.tipoFactor,
        tasaCuota = BigDecimal(conceptoRetencionForm.tasaCuota),
        importe = BigDecimal(conceptoRetencionForm.importe)
      )
    })
  }

  def getImpuestos(impuestosForm: ImpuestosForm): Impuestos = {
    Impuestos(
      totalImpuestosRetenidos = impuestosForm.totalImpuestosRetenidos.map(BigDecimal(_)),
      totalImpuestosTrasladados = impuestosForm.totalImpuestosTrasladados.map(BigDecimal(_)),
      retenciones = impuestosForm.retenciones.map(retencionesForm => {
        retencionesForm.map(retencionForm => {
          Retencion(
            impuesto = retencionForm.impuesto,
            importe = BigDecimal(retencionForm.importe)
          )
        })
      }),
      traslados = impuestosForm.traslados.map(trasladosForm => {
        trasladosForm.map(trasladoForm => {
          Traslado(
            impuesto = trasladoForm.impuesto,
            tipoFactor = trasladoForm.tipoFactor,
            tasaOCuota = BigDecimal(trasladoForm.tasaOCuota),
            importe = BigDecimal(trasladoForm.importe)
          )
        })
      })
    )
  }

  def getComplementos(complementos: Option[ComplementosForm]): Option[Complementos] = {

    complementos.map(complementosForm => {
      Complementos(
        recepcionPagos = complementosForm.recepcionPagos.map(recepcionPagoForm => {
          RecepcionPago(
            version = recepcionPagoForm.version,
            pagos = recepcionPagoForm.pagos.map(pagoForm => {
              Pago(
                fechaPago = pagoForm.fechaPago,
                formaDePagoP = pagoForm.formaDePagoP,
                monedaP = pagoForm.monedaP,
                tipoCambioP = pagoForm.tipoCambioP.map(BigDecimal(_)),
                monto = BigDecimal(pagoForm.monto),
                numOperacion = pagoForm.numOperacion,
                rfcEmisorCtaOrd = pagoForm.rfcEmisorCtaOrd,
                nomBancoOrdExt = pagoForm.nomBancoOrdExt,
                ctaOrdenante = pagoForm.ctaOrdenante,
                rfcEmisorCtaBen = pagoForm.rfcEmisorCtaBen,
                ctaBeneficiario = pagoForm.ctaBeneficiario,
                tipoCadPago = pagoForm.tipoCadPago,
                certPago = pagoForm.certPago,
                cadPago = pagoForm.cadPago,
                selloPago = pagoForm.selloPago,
                doctosRelacionesdos = pagoForm.doctosRelacionesdos.map(drf => {
                  DoctoRelacionado(
                    idDocumento = drf.idDocumento,
                    serie = drf.serie,
                    folio = drf.folio,
                    monedaDR = drf.monedaDR,
                    metodoDePagoDR = drf.metodoDePagoDR,
                    tipoCambioDR = drf.tipoCambioDR.map(BigDecimal(_)),
                    numParcialidad = drf.numParcialidad,
                    impSaldoAnt = drf.impSaldoAnt.map(BigDecimal(_)),
                    impPagado = drf.impPagado.map(BigDecimal(_)),
                    impSaldoInsoluto = drf.impSaldoInsoluto.map(BigDecimal(_))
                  )
                }),
                impuestos = pagoForm.impuestos.map(impuestosForm => getImpuestos(impuestosForm))
              )
            })
          )
        }),
        cartaPorte20 = complementosForm.cartaPorte.map(cartaPorteForm => {
          CartaPorte(
            version = cartaPorteForm.version,
            transpInternac = cartaPorteForm.transpInternac,
            entradaSalidaMerc = cartaPorteForm.entradaSalidaMerc,
            paisOrigenDestino = cartaPorteForm.paisOrigenDestino,
            viaEntradaSalida = cartaPorteForm.viaEntradaSalida,
            totalDistRec = cartaPorteForm.totalDistRec.map(BigDecimal(_)),
            ubicaciones = Ubicaciones(
              cartaPorteForm.ubicaciones.ubicacion.map(ubicacionForm =>
                Ubicacion(
                  tipoUbicacion = ubicacionForm.tipoUbicacion,
                  idUbicacion = ubicacionForm.idUbicacion,
                  rfcRemitenteDestinatario = ubicacionForm.rfcRemitenteDestinatario,
                  nombreRemitenteDestinatario = ubicacionForm.nombreRemitenteDestinatario,
                  numRegIdTrib = ubicacionForm.numRegIdTrib,
                  residenciaFiscal = ubicacionForm.residenciaFiscal,
                  numEstacion = ubicacionForm.numEstacion,
                  nombreEstacion = ubicacionForm.nombreEstacion,
                  navegacionTrafico = ubicacionForm.navegacionTrafico,
                  fechaHoraSalidaLlegada = ubicacionForm.fechaHoraSalidaLlegada,
                  tipoEstacion = ubicacionForm.tipoEstacion,
                  distanciaRecorrida = ubicacionForm.distanciaRecorrida.map(BigDecimal(_)),
                  domicilio = ubicacionForm.domicilio.map(domicilioForm =>
                    Domicilio(
                      calle = domicilioForm.calle,
                      numeroExterior = domicilioForm.numeroExterior,
                      numeroInterior = domicilioForm.numeroInterior,
                      colonia = domicilioForm.colonia,
                      localidad = domicilioForm.localidad,
                      referencia = domicilioForm.referencia,
                      municipio = domicilioForm.municipio,
                      estado = domicilioForm.estado,
                      pais = domicilioForm.pais,
                      codigoPostal = domicilioForm.codigoPostal
                    )
                  )
                )
              )
            ),
            mercancias = Mercancias(
              pesoBrutoTotal = BigDecimal(cartaPorteForm.mercancias.pesoBrutoTotal),
              unidadPeso = cartaPorteForm.mercancias.unidadPeso,
              pesoNetoTotal = cartaPorteForm.mercancias.pesoNetoTotal.map(BigDecimal(_)),
              numTotalMercancias = cartaPorteForm.mercancias.numTotalMercancias,
              cargoPorTasacion = cartaPorteForm.mercancias.cargoPorTasacion.map(BigDecimal(_)),
              mercancia = cartaPorteForm.mercancias.mercancia.map(mercanciaForm =>
                Mercancia(
                  bienesTransp = mercanciaForm.bienesTransp,
                  clavesSTCC = mercanciaForm.clavesSTCC,
                  descripcion = mercanciaForm.descripcion,
                  cantidad = BigDecimal(mercanciaForm.cantidad),
                  claveUnidad = mercanciaForm.claveUnidad,
                  unidad = mercanciaForm.unidad,
                  dimensiones = mercanciaForm.dimensiones,
                  materialPeligroso = mercanciaForm.materialPeligroso,
                  cveMaterialPeligroso = mercanciaForm.cveMaterialPeligroso,
                  embalaje = mercanciaForm.embalaje,
                  descripEmbalaje = mercanciaForm.descripEmbalaje,
                  pesoEnKg = BigDecimal(mercanciaForm.pesoEnKg),
                  valorMercancia = mercanciaForm.valorMercancia.map(BigDecimal(_)),
                  moneda = mercanciaForm.moneda,
                  fraccionArancelaria = mercanciaForm.fraccionArancelaria,
                  uuidComercioExt = mercanciaForm.uuidComercioExt,
                  pedimentos = mercanciaForm.pedimentos
                    .getOrElse(Nil)
                    .map(pedimentoForm =>
                      Pedimento(
                        pedimento = pedimentoForm.pedimento
                      )
                    ),
                  guiasIdentificacion = mercanciaForm.guiasIdentificacion
                    .getOrElse(Nil)
                    .map(guiasForm =>
                      GuiasIdentificacion(
                        numeroGuiaIdentificacion = guiasForm.numeroGuiaIdentificacion,
                        descripGuiaIdentificacion = guiasForm.descripGuiaIdentificacion,
                        pesoGuiaIdentificacion = BigDecimal(guiasForm.pesoGuiaIdentificacion)
                      )
                    ),
                  cantidadTransporta = mercanciaForm.cantidadTransporta
                    .getOrElse(Nil)
                    .map(cantidadForm =>
                      CantidadTransporta(
                        cantidad = BigDecimal(cantidadForm.cantidad),
                        idOrigen = cantidadForm.idOrigen,
                        idDestino = cantidadForm.idDestino,
                        cvesTransporte = cantidadForm.cvesTransporte
                      )
                    ),
                  detalleMercancia = mercanciaForm.detalleMercancia
                    .getOrElse(Nil)
                    .map(detalleForm =>
                      DetalleMercancia(
                        unidadPesoMerc = detalleForm.unidadPesoMerc,
                        pesoBruto = BigDecimal(detalleForm.pesoBruto),
                        pesoNeto = BigDecimal(detalleForm.pesoNeto),
                        pesoTara = BigDecimal(detalleForm.pesoTara),
                        numPiezas = detalleForm.numPiezas
                      )
                    )
                )
              ),
              autotransporte = cartaPorteForm.mercancias.autotransporte.map(autoForm =>
                Autotransporte(
                  permSCT = autoForm.permSCT,
                  numPermisoSCT = autoForm.numPermisoSCT,
                  identificacionVehicular = IdentificacionVehicular(
                    configVehicular = autoForm.identificacionVehicular.configVehicular,
                    placaVM = autoForm.identificacionVehicular.placaVM,
                    anioModeloVM = autoForm.identificacionVehicular.anioModeloVM
                  ),
                  seguros = Seguros(
                    aseguraRespCivil = autoForm.seguros.aseguraRespCivil,
                    polizaRespCivil = autoForm.seguros.polizaRespCivil,
                    aseguraMedAmbiente = autoForm.seguros.aseguraMedAmbiente,
                    polizaMedAmbiente = autoForm.seguros.polizaMedAmbiente,
                    aseguraCarga = autoForm.seguros.aseguraCarga,
                    polizaCarga = autoForm.seguros.polizaCarga,
                    primaSeguro = autoForm.seguros.primaSeguro.map(BigDecimal(_))
                  ),
                  remolques = autoForm.remolques.map(remolForm =>
                    Remolques(
                      remolque = remolForm.remolque.map(r =>
                        Remolque(
                          subTipoRem = r.subTipoRem,
                          placa = r.placa
                        )
                      )
                    )
                  )
                )
              ),
              transporteMaritimo = cartaPorteForm.mercancias.transporteMaritimo.map(maritimoForm =>
                TransporteMaritimo(
                  permSCT = maritimoForm.permSCT,
                  numPermisoSCT = maritimoForm.numPermisoSCT,
                  nombreAseg = maritimoForm.nombreAseg,
                  numPolizaSeguro = maritimoForm.numPolizaSeguro,
                  tipoEmbarcacion = maritimoForm.tipoEmbarcacion,
                  matricula = maritimoForm.matricula,
                  numeroOMI = maritimoForm.numeroOMI,
                  anioEmbarcacion = maritimoForm.anioEmbarcacion,
                  nombreEmbarc = maritimoForm.nombreEmbarc,
                  nacionalidadEmbarc = maritimoForm.nacionalidadEmbarc,
                  unidadesDeArqBruto = BigDecimal(maritimoForm.unidadesDeArqBruto),
                  tipoCarga = maritimoForm.tipoCarga,
                  numCertITC = maritimoForm.numCertITC,
                  eslora = maritimoForm.eslora.map(BigDecimal(_)),
                  manga = maritimoForm.manga.map(BigDecimal(_)),
                  calado = maritimoForm.calado.map(BigDecimal(_)),
                  lineaNaviera = maritimoForm.lineaNaviera,
                  nombreAgenteNaviero = maritimoForm.nombreAgenteNaviero,
                  numAutorizacionNaviero = maritimoForm.numAutorizacionNaviero,
                  numViaje = maritimoForm.numViaje,
                  numConocEmbarc = maritimoForm.numConocEmbarc,
                  contenedor = maritimoForm.contenedor.map(contenedorForm =>
                    Contenedor(
                      matriculaContenedor = contenedorForm.matriculaContenedor,
                      tipoContenedor = contenedorForm.tipoContenedor,
                      numPrecinto = contenedorForm.numPrecinto
                    )
                  )
                )
              ),
              transporteAereo = cartaPorteForm.mercancias.transporteAereo.map(aereoForm =>
                TransporteAereo(
                  permSCT = aereoForm.permSCT,
                  numPermisoSCT = aereoForm.numPermisoSCT,
                  matriculaAeronave = aereoForm.matriculaAeronave,
                  nombreAseg = aereoForm.nombreAseg,
                  numPolizaSeguro = aereoForm.numPolizaSeguro,
                  numeroGuia = aereoForm.numeroGuia,
                  lugarContrato = aereoForm.lugarContrato,
                  codigoTransportista = aereoForm.codigoTransportista,
                  rfcEmbarcador = aereoForm.rfcEmbarcador,
                  numRegIdTribEmbarc = aereoForm.numRegIdTribEmbarc,
                  residenciaFiscalEmbarc = aereoForm.residenciaFiscalEmbarc,
                  nombreEmbarcador = aereoForm.nombreEmbarcador
                )
              ),
              transporteFerroviario = cartaPorteForm.mercancias.transporteFerroviario.map(ferroviarioForm =>
                TransporteFerroviario(
                  tipoServicio = ferroviarioForm.tipoServicio,
                  tipoDeTrafico = ferroviarioForm.tipoDeTrafico,
                  nombreAseg = ferroviarioForm.nombreAseg,
                  numPolizaSeguro = ferroviarioForm.numPolizaSeguro,
                  derechosDePaso = ferroviarioForm.derechosDePaso.map(derechosForm =>
                    DerechosDePaso(
                      tipoDerechoDePaso = derechosForm.tipoDerechoDePaso,
                      kilometrajePagado = BigDecimal(derechosForm.kilometrajePagado)
                    )
                  ),
                  carro = ferroviarioForm.carro.map(carroForm =>
                    Carro(
                      tipoCarro = carroForm.tipoCarro,
                      matriculaCarro = carroForm.matriculaCarro,
                      guiaCarro = carroForm.guiaCarro,
                      toneladasNetasCarro = BigDecimal(carroForm.toneladasNetasCarro),
                      contenedor = carroForm.contenedor.map(contenedorForm =>
                        ContenedorCarro(
                          tipoContenedor = contenedorForm.tipoContenedor,
                          pesoContenedorVacio = BigDecimal(contenedorForm.pesoContenedorVacio),
                          pesoNetoMercancia = BigDecimal(contenedorForm.pesoNetoMercancia)
                        )
                      )
                    )
                  )
                )
              )
            ),
            figuratransporte = cartaPorteForm.figuratransporte.map(figuraForm =>
              Figuratransporte(
                tiposFigura = figuraForm.tiposFigura.map(tiposForm =>
                  TiposFigura(
                    tipoFigura = tiposForm.tipoFigura,
                    rfcFigura = tiposForm.rfcFigura,
                    numLicencia = tiposForm.numLicencia,
                    nombreFigura = tiposForm.nombreFigura,
                    numRegIdTribFigura = tiposForm.numRegIdTribFigura,
                    residenciaFiscalFigura = tiposForm.residenciaFiscalFigura,
                    partesTransporte = tiposForm.partesTransporte
                      .getOrElse(Nil)
                      .map(partesForm =>
                        PartesTransporte(
                          parteTransporte = partesForm.parteTransporte
                        )
                      ),
                    domicilio = tiposForm.domicilio.map(domicilioForm =>
                      DomicilioFigura(
                        calleFigura = domicilioForm.calleFigura,
                        numeroExteriorFigura = domicilioForm.numeroExteriorFigura,
                        numeroInteriorFigura = domicilioForm.numeroInteriorFigura,
                        coloniaFigura = domicilioForm.coloniaFigura,
                        localidadFigura = domicilioForm.localidadFigura,
                        referenciaFigura = domicilioForm.referenciaFigura,
                        municipioFigura = domicilioForm.municipioFigura,
                        estadoFigura = domicilioForm.estadoFigura,
                        paisFigura = domicilioForm.paisFigura,
                        codigoPostalFigura = domicilioForm.codigoPostalFigura
                      )
                    )
                  )
                )
              )
            )
          )
        }),
        nomina12 = complementosForm.nomina.map(nominaCompForm => {
          NominaComp(
            version = nominaCompForm.version,
            tipoNomina = nominaCompForm.tipoNomina,
            fechaPago = nominaCompForm.fechaPago,
            fechaInicialPago = nominaCompForm.fechaInicialPago,
            fechaFinalPago = nominaCompForm.fechaFinalPago,
            numDiasPagados = BigDecimal(nominaCompForm.numDiasPagados),
            totalPercepciones = nominaCompForm.totalPercepciones.map(BigDecimal(_)),
            totalDeducciones = nominaCompForm.totalDeducciones.map(BigDecimal(_)),
            totalOtrosPagos = nominaCompForm.totalOtrosPagos.map(BigDecimal(_)),
            emisor = nominaCompForm.emisor.map(emisorForm =>
              Emisor(
                curp = emisorForm.curp,
                registroPatronal = emisorForm.registroPatronal,
                rfcPatronOrigen = emisorForm.rfcPatronOrigen,
                entidadSNCF = emisorForm.entidadSNCF.map(entidadSNCFForm =>
                  EntidadSNCF(
                    origenRecurso = entidadSNCFForm.origenRecurso,
                    montoRecursoPropio = entidadSNCFForm.montoRecursoPropio.map(BigDecimal(_))
                  )
                )
              )
            ),
            receptor = ReceptorNom(
              curp = nominaCompForm.receptorNom.curp,
              numSeguridadSocial = nominaCompForm.receptorNom.numSeguridadSocial,
              fechaInicioRelLaboral = nominaCompForm.receptorNom.fechaInicioRelLaboral,
              antiguedad = nominaCompForm.receptorNom.antiguedad,
              tipoContrato = nominaCompForm.receptorNom.tipoContrato,
              sindicalizado = nominaCompForm.receptorNom.sindicalizado,
              tipoJornada = nominaCompForm.receptorNom.tipoJornada,
              tipoRegimen = nominaCompForm.receptorNom.tipoRegimen,
              numEmpleado = nominaCompForm.receptorNom.numEmpleado,
              departamento = nominaCompForm.receptorNom.departamento,
              puesto = nominaCompForm.receptorNom.puesto,
              riesgoPuesto = nominaCompForm.receptorNom.riesgoPuesto,
              periodicidadPago = nominaCompForm.receptorNom.periodicidadPago,
              banco = nominaCompForm.receptorNom.banco,
              cuentaBancaria = nominaCompForm.receptorNom.cuentaBancaria,
              salarioBaseCotApor = nominaCompForm.receptorNom.salarioBaseCotApor.map(BigDecimal(_)),
              salarioDiarioIntegrado = nominaCompForm.receptorNom.salarioDiarioIntegrado.map(BigDecimal(_)),
              claveEntFed = nominaCompForm.receptorNom.claveEntFed,
              subContratacion = nominaCompForm.receptorNom.subContratacion.map(subcontratForm =>
                SubContratacion(
                  rfcLabora = subcontratForm.rfcLabora,
                  porcentajeTiempo = BigDecimal(subcontratForm.porcentajeTiempo)
                )
              )
            ),
            percepciones = nominaCompForm.percepciones.map(percepcionesForm =>
              Percepciones(
                totalSueldos = percepcionesForm.totalSueldos.map(BigDecimal(_)),
                totalSeparacionIndemnizacion = percepcionesForm.totalSeparacionIndemnizacion.map(BigDecimal(_)),
                totalJubilacionPensionRetiro = percepcionesForm.totalJubilacionPensionRetiro.map(BigDecimal(_)),
                totalGravado = BigDecimal(percepcionesForm.totalGravado),
                totalExento = BigDecimal(percepcionesForm.totalExento),
                percepcion = percepcionesForm.percepcion.map(percepcionForm =>
                  Percepcion(
                    tipoPercepcion = percepcionForm.tipoPercepcion,
                    clave = percepcionForm.clave,
                    concepto = percepcionForm.concepto,
                    importeGravado = BigDecimal(percepcionForm.importeGravado),
                    importeExento = BigDecimal(percepcionForm.importeExento),
                    accionesOTitulos = percepcionForm.accionesOTitulos.map(accionesForm =>
                      AccionesOTitulos(
                        valorMercado = BigDecimal(accionesForm.valorMercado),
                        precioAlOtorgarse = BigDecimal(accionesForm.precioAlOtorgarse)
                      )
                    ),
                    horasExtra = percepcionForm.horasExtra.map(horasForm =>
                      HorasExtra(
                        dias = horasForm.dias,
                        tipoHoras = horasForm.tipoHoras,
                        horasExtra = horasForm.horasExtra,
                        importePagado = BigDecimal(horasForm.importePagado)
                      )
                    )
                  )
                ),
                jubilacionPensionRetiro = percepcionesForm.jubilacionPensionRetiro.map(jubilacionForm =>
                  JubilacionPensionRetiro(
                    totalUnaExhibicion = jubilacionForm.totalUnaExhibicion.map(BigDecimal(_)),
                    totalParcialidad = jubilacionForm.totalParcialidad.map(BigDecimal(_)),
                    montoDiario = jubilacionForm.montoDiario.map(BigDecimal(_)),
                    ingresoAcumulable = BigDecimal(jubilacionForm.ingresoAcumulable),
                    ingresoNoAcumulable = BigDecimal(jubilacionForm.ingresoNoAcumulable)
                  )
                ),
                separacionIndemnizacion = percepcionesForm.separacionIndemnizacion.map(separacionForm =>
                  SeparacionIndemnizacion(
                    totalPagado = BigDecimal(separacionForm.totalPagado),
                    numAniosServicio = separacionForm.numAniosServicio,
                    ultimoSueldoMensOrd = BigDecimal(separacionForm.ultimoSueldoMensOrd),
                    ingresoAcumulable = BigDecimal(separacionForm.ingresoAcumulable),
                    ingresoNoAcumulable = BigDecimal(separacionForm.ingresoNoAcumulable)
                  )
                )
              )
            ),
            deducciones = nominaCompForm.deducciones.map(deduccionesForm =>
              Deducciones(
                totalOtrasDeducciones = deduccionesForm.totalOtrasDeducciones.map(BigDecimal(_)),
                totalImpuestosRetenidos = deduccionesForm.totalImpuestosRetenidos.map(BigDecimal(_)),
                deduccion = deduccionesForm.deduccion.map(deduccionForm =>
                  Deduccion(
                    tipoDeduccion = deduccionForm.tipoDeduccion,
                    clave = deduccionForm.clave,
                    concepto = deduccionForm.concepto,
                    importe = BigDecimal(deduccionForm.importe)
                  )
                )
              )
            ),
            otrosPagos = nominaCompForm.otrosPagos.map(otrosPagosForm =>
              OtrosPagos(
                otroPago = otrosPagosForm.otroPago.map(otroPagoForm =>
                  OtroPago(
                    tipoOtroPago = otroPagoForm.tipoOtroPago,
                    clave = otroPagoForm.clave,
                    concepto = otroPagoForm.concepto,
                    importe = BigDecimal(otroPagoForm.importe),
                    subsidioAlEmpleo = otroPagoForm.subsidioAlEmpleo.map(subsidioForm =>
                      SubsidioAlEmpleo(
                        subsidioCausado = BigDecimal(subsidioForm.subsidioCausado)
                      )
                    ),
                    compensacionSaldosAFavor = otroPagoForm.compensacionSaldosAFavor.map(compensacionForm =>
                      CompensacionSaldo(
                        saldoAFavor = BigDecimal(compensacionForm.saldoAFavor),
                        anio = compensacionForm.anio,
                        remanenteSalFav = BigDecimal(compensacionForm.remanenteSalFav)
                      )
                    )
                  )
                )
              )
            ),
            incapacidades = nominaCompForm.incapacidades.map(incapacidadesForm =>
              Incapacidades(
                incapacidad = incapacidadesForm.incapacidad.map(incapForm =>
                  Incapacidad(
                    diasIncapacidad = incapForm.diasIncapacidad,
                    tipoIncapacidad = incapForm.tipoIncapacidad,
                    importeMonetario = incapForm.importeMonetario.map(BigDecimal(_))
                  )
                )
              )
            )
          )
        })
      )
    })
  }

}
