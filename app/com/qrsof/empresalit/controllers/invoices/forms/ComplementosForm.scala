package com.qrsof.empresalit.controllers.invoices.forms

import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.CartaPorteForm
import com.qrsof.empresalit.controllers.invoices.forms.complementos.nomina.NominaCompForm


case class ComplementosForm(
                             recepcionPagos: Option[RecepcionPagoForm] = None,
                             cartaPorte: Option[CartaPorteForm] = None,
                             nomina: Option[NominaCompForm] = None
                           )
