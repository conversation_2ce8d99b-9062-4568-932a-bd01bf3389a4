package com.qrsof.empresalit.controllers.invoices.forms.v40.complementos

import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.CartaPorteForm
import com.qrsof.empresalit.controllers.invoices.forms.complementos.nomina.NominaCompForm
import com.qrsof.empresalit.controllers.invoices.forms.v40.concepto.v20.RecepcionPagoForm

case class ComplementosForm(
                             recepcionPagos: Option[RecepcionPagoForm] = None,
                             cartaPorte: Option[CartaPorteForm] = None,
                             nomina: Option[NominaCompForm] = None
                           )
