package com.qrsof.empresalit.controllers.invoices.forms.v40.concepto.v20

import com.qrsof.empresalit.controllers.invoices.forms.complementos.pagos.ImpuestosPForm

case class PagoForm(
                     fechaPago: String,
                     formaDePagoP: String,
                     monedaP: String,
                     tipoCambioP: Option[String] = None,
                     monto: String,
                     numOperacion: Option[String] = None,
                     rfcEmisorCtaOrd: Option[String] = None,
                     nomBancoOrdExt: Option[String] = None,
                     ctaOrdenante: Option[String] = None,
                     rfcEmisorCtaBen: Option[String] = None,
                     ctaBeneficiario: Option[String] = None,
                     tipoCadPago: Option[String] = None,
                     certPago: Option[String] = None,
                     cadPago: Option[String] = None,
                     selloPago: Option[String] = None,
                     doctosRelacionados: Seq[DoctoRelacionadoForm] = Nil,
                     impuestosP: Option[ImpuestosPForm] = None
                   )
