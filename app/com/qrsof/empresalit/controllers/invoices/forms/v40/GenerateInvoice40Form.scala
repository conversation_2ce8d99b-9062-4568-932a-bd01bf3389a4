package com.qrsof.empresalit.controllers.invoices.forms.v40

import com.qrsof.empresalit.controllers.invoices.forms.v40

case class GenerateInvoice40Form(
                                  serie: Option[String] = None,
                                  folio: Option[String] = None,
                                  fecha: Option[String] = None, // AAAA-MM-DDThh:mm:ss
                                  formaPago: Option[String] = None,
                                  condicionesDePago: Option[String] = None,
                                  subTotal: String,
                                  descuento: Option[String] = None,
                                  moneda: String,
                                  tipoCambio: Option[String] = None,
                                  total: String,
                                  tipoComprobante: String,
                                  exportacion: String,
                                  metodoPago: Option[String] = None,
                                  lugarExpedicion: String,
                                  confirmacion: Option[String] = None,
                                  informacionGlobal: Option[InformacionGlobalForm] = None,
                                  cfdiRelacionados: Option[Seq[CfdiRelacionadosForm]] = None,
                                  receptor: Option[ReceptorComprobante40Form] = None,
                                  conceptos: Seq[Concepto40Form] = Nil,
                                  impuestos: Option[v40.ImpuestosForm] = None,
                                  complementos: Option[v40.complementos.ComplementosForm] = None
                                )
