package com.qrsof.empresalit.controllers.invoices.forms.v40

import com.qrsof.empresalit.controllers.invoices.forms.*
import com.qrsof.empresalit.controllers.invoices.forms.complementos.pagos.ImpuestosPForm
import com.qrsof.empresalit.controllers.invoices.forms.v40.concepto.v20.{ImpuestosDRForm, TotalesForm}
import com.qrsof.empresalit.controllers.invoices.forms.v40.concepto.{ParteForm, v20}
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.figura.transporte.{DomicilioFigura, PartesTransporte, TiposFigura}
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.mercancias.*
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.ubicaciones.{Domicilio, Ubicacion}
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.{CartaPorte, Figuratransporte, Mercancias, Ubicaciones}
import com.qrsof.empresalit.invoicing.domain.complementos.nomina.*
import com.qrsof.empresalit.invoicing.domain.complementos.pagos20.*
import com.qrsof.empresalit.invoicing.domain.complementos.{Complementos, nomina}
import com.qrsof.empresalit.invoicing.domain.conceptos.impuestos.{ConceptoRetencion, ConceptoTraslado}
import com.qrsof.empresalit.invoicing.domain.conceptos.{ConceptosImpuestos, InformacionAduanera}
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.conceptos.{ACuentaTerceros, Parte}
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.*
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.impuestos.{Impuestos, Retencion, Traslado}

class FormsMapperImpl extends FormsMapper {
  override def toGenerateInvoiceRequest(companyKey: String, generateInvoice40Form: GenerateInvoice40Form): GenerateInvoiceRequest = {
    GenerateInvoiceRequest(
      companyKey = companyKey,
      serie = generateInvoice40Form.serie,
      folio = generateInvoice40Form.folio,
      fecha = generateInvoice40Form.fecha,
      condicionesDePago = generateInvoice40Form.condicionesDePago,
      lugarExpedicion = generateInvoice40Form.lugarExpedicion,
      formaPago = generateInvoice40Form.formaPago,
      metodoPago = generateInvoice40Form.metodoPago,
      moneda = generateInvoice40Form.moneda,
      tipoComprobante = generateInvoice40Form.tipoComprobante,
      tipoCambio = generateInvoice40Form.tipoCambio.map(BigDecimal(_)),
      subTotal = BigDecimal(generateInvoice40Form.subTotal),
      total = BigDecimal(generateInvoice40Form.total),
      descuento = generateInvoice40Form.descuento.map(BigDecimal(_)),
      exportacion = generateInvoice40Form.exportacion,
      confirmacion = generateInvoice40Form.confirmacion,
      informacionGlobal = getInformacionGeneral(generateInvoice40Form),
      cfdiRelacionados = getCfdiRelacionados(generateInvoice40Form),
      receptor = toReceptorComprobante(generateInvoice40Form.receptor),
      conceptos = toConceptos(generateInvoice40Form.conceptos),
      impuestos = toImpuestos(generateInvoice40Form.impuestos),
      complementos = toComplementos(generateInvoice40Form.complementos)
    )
  }

  private def getCfdiRelacionados(generateInvoice40Form: GenerateInvoice40Form) = {
    generateInvoice40Form.cfdiRelacionados.map(
      _.map(cr =>
        CfdiRelacionados(
          tipoRelacion = cr.tipoRelacion,
          cfdiRelacionado = cr.cfdiRelacionados.map(cr => CfdiRelacionado(cr.uuid))
        )
      )
    )
  }

  private def getInformacionGeneral(generateInvoice40Form: GenerateInvoice40Form) = {
    generateInvoice40Form.informacionGlobal.map(igf => {
      InformacionGlobal(
        periodicidad = igf.periodicidad,
        meses = igf.meses,
        anio = igf.anio.toShort
      )
    })
  }

  private def toPago(pagoForm: Seq[v20.PagoForm]): Seq[Pago] = {
    pagoForm.map(pf => {
      Pago(
        fechaPago = pf.fechaPago,
        formaDePagoP = pf.formaDePagoP,
        monedaP = pf.monedaP,
        tipoCambioP = pf.tipoCambioP.map(BigDecimal(_)),
        monto = BigDecimal(pf.monto),
        numOperacion = pf.numOperacion,
        rfcEmisorCtaOrd = pf.rfcEmisorCtaOrd,
        nomBancoOrdExt = pf.nomBancoOrdExt,
        ctaOrdenante = pf.ctaOrdenante,
        rfcEmisorCtaBen = pf.rfcEmisorCtaBen,
        ctaBeneficiario = pf.ctaBeneficiario,
        tipoCadPago = pf.tipoCadPago,
        certPago = pf.certPago,
        cadPago = pf.cadPago,
        selloPago = pf.selloPago,
        doctosRelacionesdos = toDoctosRelacionados(pf),
        impuestosP = toImpuestosP(pf)
      )
    })
  }

  private def toImpuestosP(pf: v20.PagoForm): Option[ImpuestosP] = {
    pf.impuestosP.map(impuestosP => {
      ImpuestosP(
        retencionesP = getRetencionesP(impuestosP),
        trasladosP = getTrasladosP(impuestosP)
      )
    })
  }

  private def getTrasladosP(impuestosP: ImpuestosPForm) = {
    impuestosP.trasladosP.map(_.map(tpf => {
      TrasladoP(
        baseP = BigDecimal(tpf.baseP),
        impuestoP = tpf.impuestoP,
        tipoFactorP = tpf.tipoFactorP,
        tasaOCuotaP = tpf.tasaOCuotaP.map(BigDecimal(_)),
        importeP = tpf.importeP.map(BigDecimal(_))
      )
    }))
  }

  private def getRetencionesP(impuestosP: ImpuestosPForm) = {
    impuestosP.retencionesP.map(_.map(rf => {
      RetencionP(
        impuestoP = rf.impuestoP,
        importeP = BigDecimal(rf.importeP)
      )
    }))
  }

  private def toDoctosRelacionados(pf: v20.PagoForm): Seq[DoctoRelacionado] = {
    pf.doctosRelacionados.map(dr => {
      DoctoRelacionado(
        idDocumento = dr.idDocumento,
        serie = dr.serie,
        folio = dr.folio,
        monedaDR = dr.monedaDR,
        equivalenciaDR = dr.equivalenciaDR.map(BigDecimal(_)),
        numParcialidad = dr.numParcialidad,
        impSaldoAnt = BigDecimal(dr.impSaldoAnt),
        impPagado = BigDecimal(dr.impPagado),
        impSaldoInsoluto = BigDecimal(dr.impSaldoInsoluto),
        objetoImpDR = dr.objetoImpDR,
        impuestosDR = toImpuestosDR(dr.impuestosDR)
      )
    })
  }

  private def toImpuestosDR(impuestosDR: Option[ImpuestosDRForm]): Option[ImpuestosDR] = {
    impuestosDR.map(idrf => {
      ImpuestosDR(
        retencionesDR = idrf.retencionesDR.map(rsdrf =>
          RetencionesDR(
            rsdrf.retenciones.map(rdrf =>
              RetencionDR(
                baseDR = BigDecimal(rdrf.baseDR),
                impuestoDR = rdrf.impuestoDR,
                tipoFactorDR = rdrf.tipoFactorDR,
                tasaOCuotaDR = BigDecimal(rdrf.tasaOCuotaDR),
                importeDR = BigDecimal(rdrf.importeDR)
              )
            )
          )
        ),
        trasladosDR = idrf.trasladosDR.map(rsdrf =>
          TrasladosDR(
            rsdrf.traslados.map(rdrf =>
              TrasladoDR(
                baseDR = BigDecimal(rdrf.baseDR),
                impuestoDR = rdrf.impuestoDR,
                tipoFactorDR = rdrf.tipoFactorDR,
                tasaOCuotaDR = rdrf.tasaOCuotaDR.map(BigDecimal(_)),
                importeDR = rdrf.importeDR.map(BigDecimal(_))
              )
            )
          )
        )
      )
    })
  }

  private def toTotales(totales: TotalesForm): Totales = {
    Totales(
      totalRetencionesIVA = totales.totalRetencionesIVA.map(BigDecimal(_)),
      totalRetencionesISR = totales.totalRetencionesISR.map(BigDecimal(_)),
      totalRetencionesIEPS = totales.totalRetencionesIEPS.map(BigDecimal(_)),
      totalTrasladosBaseIVA16 = totales.totalTrasladosBaseIVA16.map(BigDecimal(_)),
      totalTrasladosImpuestoIVA16 = totales.totalTrasladosImpuestoIVA16.map(BigDecimal(_)),
      totalTrasladosBaseIVA8 = totales.totalTrasladosBaseIVA8.map(BigDecimal(_)),
      totalTrasladosImpuestoIVA8 = totales.totalTrasladosImpuestoIVA8.map(BigDecimal(_)),
      totalTrasladosBaseIVA0 = totales.totalTrasladosBaseIVA0.map(BigDecimal(_)),
      totalTrasladosImpuestoIVA0 = totales.totalTrasladosImpuestoIVA0.map(BigDecimal(_)),
      totalTrasladosBaseIVAExento = totales.totalTrasladosBaseIVAExento.map(BigDecimal(_)),
      montoTotalPagos = totales.montoTotalPagos.map(BigDecimal(_))
    )
  }

  private def toComplelentoRecepcionPagos(recepcionPagosForm: Option[v20.RecepcionPagoForm]): Option[RecepcionPago] = {
    recepcionPagosForm.map(rpf => {
      RecepcionPago(
        totales = toTotales(rpf.totales),
        pagos = toPago(rpf.pagos)
      )
    })
  }

  private def toComplementos(complementosForm: Option[v40.complementos.ComplementosForm]): Option[Complementos] = {
    complementosForm.map(cf =>
      Complementos(
        recepcionPagos20 = toComplelentoRecepcionPagos(cf.recepcionPagos),
        cartaPorte20 = toCartaPorte(cf),
        nomina12 = toNomina(cf)
      )
    )
  }

  private def toNomina(cf: v40.complementos.ComplementosForm): Option[NominaComp] = {
    cf.nomina.map(nominaCompForm => {
      NominaComp(
        version = nominaCompForm.version,
        tipoNomina = nominaCompForm.tipoNomina,
        fechaPago = nominaCompForm.fechaPago,
        fechaInicialPago = nominaCompForm.fechaInicialPago,
        fechaFinalPago = nominaCompForm.fechaFinalPago,
        numDiasPagados = BigDecimal(nominaCompForm.numDiasPagados),
        totalPercepciones = nominaCompForm.totalPercepciones.map(BigDecimal(_)),
        totalDeducciones = nominaCompForm.totalDeducciones.map(BigDecimal(_)),
        totalOtrosPagos = nominaCompForm.totalOtrosPagos.map(BigDecimal(_)),
        emisor = nominaCompForm.emisor.map(emisorForm =>
          nomina.Emisor(
            curp = emisorForm.curp,
            registroPatronal = emisorForm.registroPatronal,
            rfcPatronOrigen = emisorForm.rfcPatronOrigen,
            entidadSNCF = emisorForm.entidadSNCF.map(entidadSNCFForm =>
              EntidadSNCF(
                origenRecurso = entidadSNCFForm.origenRecurso,
                montoRecursoPropio = entidadSNCFForm.montoRecursoPropio.map(BigDecimal(_))
              )
            )
          )
        ),
        receptor = ReceptorNom(
          curp = nominaCompForm.receptorNom.curp,
          numSeguridadSocial = nominaCompForm.receptorNom.numSeguridadSocial,
          fechaInicioRelLaboral = nominaCompForm.receptorNom.fechaInicioRelLaboral,
          antiguedad = nominaCompForm.receptorNom.antiguedad,
          tipoContrato = nominaCompForm.receptorNom.tipoContrato,
          sindicalizado = nominaCompForm.receptorNom.sindicalizado,
          tipoJornada = nominaCompForm.receptorNom.tipoJornada,
          tipoRegimen = nominaCompForm.receptorNom.tipoRegimen,
          numEmpleado = nominaCompForm.receptorNom.numEmpleado,
          departamento = nominaCompForm.receptorNom.departamento,
          puesto = nominaCompForm.receptorNom.puesto,
          riesgoPuesto = nominaCompForm.receptorNom.riesgoPuesto,
          periodicidadPago = nominaCompForm.receptorNom.periodicidadPago,
          banco = nominaCompForm.receptorNom.banco,
          cuentaBancaria = nominaCompForm.receptorNom.cuentaBancaria,
          salarioBaseCotApor = nominaCompForm.receptorNom.salarioBaseCotApor.map(BigDecimal(_)),
          salarioDiarioIntegrado = nominaCompForm.receptorNom.salarioDiarioIntegrado.map(BigDecimal(_)),
          claveEntFed = nominaCompForm.receptorNom.claveEntFed,
          subContratacion = nominaCompForm.receptorNom.subContratacion.map(subcontratForm =>
            SubContratacion(
              rfcLabora = subcontratForm.rfcLabora,
              porcentajeTiempo = BigDecimal(subcontratForm.porcentajeTiempo)
            )
          )
        ),
        percepciones = nominaCompForm.percepciones.map(percepcionesForm =>
          Percepciones(
            totalSueldos = percepcionesForm.totalSueldos.map(BigDecimal(_)),
            totalSeparacionIndemnizacion = percepcionesForm.totalSeparacionIndemnizacion.map(BigDecimal(_)),
            totalJubilacionPensionRetiro = percepcionesForm.totalJubilacionPensionRetiro.map(BigDecimal(_)),
            totalGravado = BigDecimal(percepcionesForm.totalGravado),
            totalExento = BigDecimal(percepcionesForm.totalExento),
            percepcion = percepcionesForm.percepcion.map(percepcionForm =>
              Percepcion(
                tipoPercepcion = percepcionForm.tipoPercepcion,
                clave = percepcionForm.clave,
                concepto = percepcionForm.concepto,
                importeGravado = BigDecimal(percepcionForm.importeGravado),
                importeExento = BigDecimal(percepcionForm.importeExento),
                accionesOTitulos = percepcionForm.accionesOTitulos.map(accionesForm =>
                  AccionesOTitulos(
                    valorMercado = BigDecimal(accionesForm.valorMercado),
                    precioAlOtorgarse = BigDecimal(accionesForm.precioAlOtorgarse)
                  )
                ),
                horasExtra = percepcionForm.horasExtra.map(horasForm =>
                  HorasExtra(
                    dias = horasForm.dias,
                    tipoHoras = horasForm.tipoHoras,
                    horasExtra = horasForm.horasExtra,
                    importePagado = BigDecimal(horasForm.importePagado)
                  )
                )
              )
            ),
            jubilacionPensionRetiro = percepcionesForm.jubilacionPensionRetiro.map(jubilacionForm =>
              JubilacionPensionRetiro(
                totalUnaExhibicion = jubilacionForm.totalUnaExhibicion.map(BigDecimal(_)),
                totalParcialidad = jubilacionForm.totalParcialidad.map(BigDecimal(_)),
                montoDiario = jubilacionForm.montoDiario.map(BigDecimal(_)),
                ingresoAcumulable = BigDecimal(jubilacionForm.ingresoAcumulable),
                ingresoNoAcumulable = BigDecimal(jubilacionForm.ingresoNoAcumulable)
              )
            ),
            separacionIndemnizacion = percepcionesForm.separacionIndemnizacion.map(separacionForm =>
              SeparacionIndemnizacion(
                totalPagado = BigDecimal(separacionForm.totalPagado),
                numAniosServicio = separacionForm.numAniosServicio,
                ultimoSueldoMensOrd = BigDecimal(separacionForm.ultimoSueldoMensOrd),
                ingresoAcumulable = BigDecimal(separacionForm.ingresoAcumulable),
                ingresoNoAcumulable = BigDecimal(separacionForm.ingresoNoAcumulable)
              )
            )
          )
        ),
        deducciones = nominaCompForm.deducciones.map(deduccionesForm =>
          Deducciones(
            totalOtrasDeducciones = deduccionesForm.totalOtrasDeducciones.map(BigDecimal(_)),
            totalImpuestosRetenidos = deduccionesForm.totalImpuestosRetenidos.map(BigDecimal(_)),
            deduccion = deduccionesForm.deduccion.map(deduccionForm =>
              Deduccion(
                tipoDeduccion = deduccionForm.tipoDeduccion,
                clave = deduccionForm.clave,
                concepto = deduccionForm.concepto,
                importe = BigDecimal(deduccionForm.importe)
              )
            )
          )
        ),
        otrosPagos = nominaCompForm.otrosPagos.map(otrosPagosForm =>
          OtrosPagos(
            otroPago = otrosPagosForm.otroPago.map(otroPagoForm =>
              OtroPago(
                tipoOtroPago = otroPagoForm.tipoOtroPago,
                clave = otroPagoForm.clave,
                concepto = otroPagoForm.concepto,
                importe = BigDecimal(otroPagoForm.importe),
                subsidioAlEmpleo = otroPagoForm.subsidioAlEmpleo.map(subsidioForm =>
                  SubsidioAlEmpleo(
                    subsidioCausado = BigDecimal(subsidioForm.subsidioCausado)
                  )
                ),
                compensacionSaldosAFavor = otroPagoForm.compensacionSaldosAFavor.map(compensacionForm =>
                  CompensacionSaldo(
                    saldoAFavor = BigDecimal(compensacionForm.saldoAFavor),
                    anio = compensacionForm.anio,
                    remanenteSalFav = BigDecimal(compensacionForm.remanenteSalFav)
                  )
                )
              )
            )
          )
        ),
        incapacidades = nominaCompForm.incapacidades.map(incapacidadesForm =>
          Incapacidades(
            incapacidad = incapacidadesForm.incapacidad.map(incapForm =>
              Incapacidad(
                diasIncapacidad = incapForm.diasIncapacidad,
                tipoIncapacidad = incapForm.tipoIncapacidad,
                importeMonetario = incapForm.importeMonetario.map(BigDecimal(_))
              )
            )
          )
        )
      )
    })
  }

  private def toCartaPorte(cf: v40.complementos.ComplementosForm): Option[CartaPorte] = {
    cf.cartaPorte.map(cartaPorteForm => {
      CartaPorte(
        version = cartaPorteForm.version,
        transpInternac = cartaPorteForm.transpInternac,
        entradaSalidaMerc = cartaPorteForm.entradaSalidaMerc,
        paisOrigenDestino = cartaPorteForm.paisOrigenDestino,
        viaEntradaSalida = cartaPorteForm.viaEntradaSalida,
        totalDistRec = cartaPorteForm.totalDistRec.map(BigDecimal(_)),
        ubicaciones = Ubicaciones(
          cartaPorteForm.ubicaciones.ubicacion.map(ubicacionForm =>
            Ubicacion(
              tipoUbicacion = ubicacionForm.tipoUbicacion,
              idUbicacion = ubicacionForm.idUbicacion,
              rfcRemitenteDestinatario = ubicacionForm.rfcRemitenteDestinatario,
              nombreRemitenteDestinatario = ubicacionForm.nombreRemitenteDestinatario,
              numRegIdTrib = ubicacionForm.numRegIdTrib,
              residenciaFiscal = ubicacionForm.residenciaFiscal,
              numEstacion = ubicacionForm.numEstacion,
              nombreEstacion = ubicacionForm.nombreEstacion,
              navegacionTrafico = ubicacionForm.navegacionTrafico,
              fechaHoraSalidaLlegada = ubicacionForm.fechaHoraSalidaLlegada,
              tipoEstacion = ubicacionForm.tipoEstacion,
              distanciaRecorrida = ubicacionForm.distanciaRecorrida.map(BigDecimal(_)),
              domicilio = ubicacionForm.domicilio.map(domicilioForm =>
                Domicilio(
                  calle = domicilioForm.calle,
                  numeroExterior = domicilioForm.numeroExterior,
                  numeroInterior = domicilioForm.numeroInterior,
                  colonia = domicilioForm.colonia,
                  localidad = domicilioForm.localidad,
                  referencia = domicilioForm.referencia,
                  municipio = domicilioForm.municipio,
                  estado = domicilioForm.estado,
                  pais = domicilioForm.pais,
                  codigoPostal = domicilioForm.codigoPostal
                )
              )
            )
          )
        ),
        mercancias = Mercancias(
          pesoBrutoTotal = BigDecimal(cartaPorteForm.mercancias.pesoBrutoTotal),
          unidadPeso = cartaPorteForm.mercancias.unidadPeso,
          pesoNetoTotal = cartaPorteForm.mercancias.pesoNetoTotal.map(BigDecimal(_)),
          numTotalMercancias = cartaPorteForm.mercancias.numTotalMercancias,
          cargoPorTasacion = cartaPorteForm.mercancias.cargoPorTasacion.map(BigDecimal(_)),
          mercancia = cartaPorteForm.mercancias.mercancia.map(mercanciaForm =>
            Mercancia(
              bienesTransp = mercanciaForm.bienesTransp,
              clavesSTCC = mercanciaForm.clavesSTCC,
              descripcion = mercanciaForm.descripcion,
              cantidad = BigDecimal(mercanciaForm.cantidad),
              claveUnidad = mercanciaForm.claveUnidad,
              unidad = mercanciaForm.unidad,
              dimensiones = mercanciaForm.dimensiones,
              materialPeligroso = mercanciaForm.materialPeligroso,
              cveMaterialPeligroso = mercanciaForm.cveMaterialPeligroso,
              embalaje = mercanciaForm.embalaje,
              descripEmbalaje = mercanciaForm.descripEmbalaje,
              pesoEnKg = BigDecimal(mercanciaForm.pesoEnKg),
              valorMercancia = mercanciaForm.valorMercancia.map(BigDecimal(_)),
              moneda = mercanciaForm.moneda,
              fraccionArancelaria = mercanciaForm.fraccionArancelaria,
              uuidComercioExt = mercanciaForm.uuidComercioExt,
              pedimentos = mercanciaForm.pedimentos
                .getOrElse(Nil)
                .map(pedimentoForm =>
                  Pedimento(
                    pedimento = pedimentoForm.pedimento
                  )
                ),
              guiasIdentificacion = mercanciaForm.guiasIdentificacion
                .getOrElse(Nil)
                .map(guiasForm =>
                  GuiasIdentificacion(
                    numeroGuiaIdentificacion = guiasForm.numeroGuiaIdentificacion,
                    descripGuiaIdentificacion = guiasForm.descripGuiaIdentificacion,
                    pesoGuiaIdentificacion = BigDecimal(guiasForm.pesoGuiaIdentificacion)
                  )
                ),
              cantidadTransporta = mercanciaForm.cantidadTransporta
                .getOrElse(Nil)
                .map(cantidadForm =>
                  CantidadTransporta(
                    cantidad = BigDecimal(cantidadForm.cantidad),
                    idOrigen = cantidadForm.idOrigen,
                    idDestino = cantidadForm.idDestino,
                    cvesTransporte = cantidadForm.cvesTransporte
                  )
                ),
              detalleMercancia = mercanciaForm.detalleMercancia
                .getOrElse(Nil)
                .map(detalleForm =>
                  DetalleMercancia(
                    unidadPesoMerc = detalleForm.unidadPesoMerc,
                    pesoBruto = BigDecimal(detalleForm.pesoBruto),
                    pesoNeto = BigDecimal(detalleForm.pesoNeto),
                    pesoTara = BigDecimal(detalleForm.pesoTara),
                    numPiezas = detalleForm.numPiezas
                  )
                )
            )
          ),
          autotransporte = cartaPorteForm.mercancias.autotransporte.map(autoForm =>
            Autotransporte(
              permSCT = autoForm.permSCT,
              numPermisoSCT = autoForm.numPermisoSCT,
              identificacionVehicular = IdentificacionVehicular(
                configVehicular = autoForm.identificacionVehicular.configVehicular,
                placaVM = autoForm.identificacionVehicular.placaVM,
                anioModeloVM = autoForm.identificacionVehicular.anioModeloVM
              ),
              seguros = Seguros(
                aseguraRespCivil = autoForm.seguros.aseguraRespCivil,
                polizaRespCivil = autoForm.seguros.polizaRespCivil,
                aseguraMedAmbiente = autoForm.seguros.aseguraMedAmbiente,
                polizaMedAmbiente = autoForm.seguros.polizaMedAmbiente,
                aseguraCarga = autoForm.seguros.aseguraCarga,
                polizaCarga = autoForm.seguros.polizaCarga,
                primaSeguro = autoForm.seguros.primaSeguro.map(BigDecimal(_))
              ),
              remolques = autoForm.remolques.map(remolForm =>
                Remolques(remolForm.remolque.map(r => {
                  Remolque(
                    subTipoRem = r.subTipoRem,
                    placa = r.placa
                  )
                }))
              )
            )
          ),
          transporteMaritimo = cartaPorteForm.mercancias.transporteMaritimo.map(maritimoForm =>
            TransporteMaritimo(
              permSCT = maritimoForm.permSCT,
              numPermisoSCT = maritimoForm.numPermisoSCT,
              nombreAseg = maritimoForm.nombreAseg,
              numPolizaSeguro = maritimoForm.numPolizaSeguro,
              tipoEmbarcacion = maritimoForm.tipoEmbarcacion,
              matricula = maritimoForm.matricula,
              numeroOMI = maritimoForm.numeroOMI,
              anioEmbarcacion = maritimoForm.anioEmbarcacion,
              nombreEmbarc = maritimoForm.nombreEmbarc,
              nacionalidadEmbarc = maritimoForm.nacionalidadEmbarc,
              unidadesDeArqBruto = BigDecimal(maritimoForm.unidadesDeArqBruto),
              tipoCarga = maritimoForm.tipoCarga,
              numCertITC = maritimoForm.numCertITC,
              eslora = maritimoForm.eslora.map(BigDecimal(_)),
              manga = maritimoForm.manga.map(BigDecimal(_)),
              calado = maritimoForm.calado.map(BigDecimal(_)),
              lineaNaviera = maritimoForm.lineaNaviera,
              nombreAgenteNaviero = maritimoForm.nombreAgenteNaviero,
              numAutorizacionNaviero = maritimoForm.numAutorizacionNaviero,
              numViaje = maritimoForm.numViaje,
              numConocEmbarc = maritimoForm.numConocEmbarc,
              contenedor = maritimoForm.contenedor.map(contenedorForm =>
                Contenedor(
                  matriculaContenedor = contenedorForm.matriculaContenedor,
                  tipoContenedor = contenedorForm.tipoContenedor,
                  numPrecinto = contenedorForm.numPrecinto
                )
              )
            )
          ),
          transporteAereo = cartaPorteForm.mercancias.transporteAereo.map(aereoForm =>
            TransporteAereo(
              permSCT = aereoForm.permSCT,
              numPermisoSCT = aereoForm.numPermisoSCT,
              matriculaAeronave = aereoForm.matriculaAeronave,
              nombreAseg = aereoForm.nombreAseg,
              numPolizaSeguro = aereoForm.numPolizaSeguro,
              numeroGuia = aereoForm.numeroGuia,
              lugarContrato = aereoForm.lugarContrato,
              codigoTransportista = aereoForm.codigoTransportista,
              rfcEmbarcador = aereoForm.rfcEmbarcador,
              numRegIdTribEmbarc = aereoForm.numRegIdTribEmbarc,
              residenciaFiscalEmbarc = aereoForm.residenciaFiscalEmbarc,
              nombreEmbarcador = aereoForm.nombreEmbarcador
            )
          ),
          transporteFerroviario = cartaPorteForm.mercancias.transporteFerroviario.map(ferroviarioForm =>
            TransporteFerroviario(
              tipoServicio = ferroviarioForm.tipoServicio,
              tipoDeTrafico = ferroviarioForm.tipoDeTrafico,
              nombreAseg = ferroviarioForm.nombreAseg,
              numPolizaSeguro = ferroviarioForm.numPolizaSeguro,
              derechosDePaso = ferroviarioForm.derechosDePaso.map(derechosForm =>
                DerechosDePaso(
                  tipoDerechoDePaso = derechosForm.tipoDerechoDePaso,
                  kilometrajePagado = BigDecimal(derechosForm.kilometrajePagado)
                )
              ),
              carro = ferroviarioForm.carro.map(carroForm =>
                Carro(
                  tipoCarro = carroForm.tipoCarro,
                  matriculaCarro = carroForm.matriculaCarro,
                  guiaCarro = carroForm.guiaCarro,
                  toneladasNetasCarro = BigDecimal(carroForm.toneladasNetasCarro),
                  contenedor = carroForm.contenedor.map(contenedorForm =>
                    ContenedorCarro(
                      tipoContenedor = contenedorForm.tipoContenedor,
                      pesoContenedorVacio = BigDecimal(contenedorForm.pesoContenedorVacio),
                      pesoNetoMercancia = BigDecimal(contenedorForm.pesoNetoMercancia)
                    )
                  )
                )
              )
            )
          )
        ),
        figuratransporte = cartaPorteForm.figuratransporte.map(figuraForm =>
          Figuratransporte(
            tiposFigura = figuraForm.tiposFigura.map(tiposForm =>
              TiposFigura(
                tipoFigura = tiposForm.tipoFigura,
                rfcFigura = tiposForm.rfcFigura,
                numLicencia = tiposForm.numLicencia,
                nombreFigura = tiposForm.nombreFigura,
                numRegIdTribFigura = tiposForm.numRegIdTribFigura,
                residenciaFiscalFigura = tiposForm.residenciaFiscalFigura,
                partesTransporte = tiposForm.partesTransporte
                  .getOrElse(Nil)
                  .map(partesForm =>
                    PartesTransporte(
                      parteTransporte = partesForm.parteTransporte
                    )
                  ),
                domicilio = tiposForm.domicilio.map(domicilioForm =>
                  DomicilioFigura(
                    calleFigura = domicilioForm.calleFigura,
                    numeroExteriorFigura = domicilioForm.numeroExteriorFigura,
                    numeroInteriorFigura = domicilioForm.numeroInteriorFigura,
                    coloniaFigura = domicilioForm.coloniaFigura,
                    localidadFigura = domicilioForm.localidadFigura,
                    referenciaFigura = domicilioForm.referenciaFigura,
                    municipioFigura = domicilioForm.municipioFigura,
                    estadoFigura = domicilioForm.estadoFigura,
                    paisFigura = domicilioForm.paisFigura,
                    codigoPostalFigura = domicilioForm.codigoPostalFigura
                  )
                )
              )
            )
          )
        )
      )
    })
  }

  private def toTraslado(trasladoForm: Option[Seq[v40.TrasladoForm]]): Option[Seq[Traslado]] = {
    trasladoForm.map(_.map(tf => {
      Traslado(
        base = BigDecimal(tf.base),
        impuesto = tf.impuesto,
        tipoFactor = tf.tipoFactor,
        tasaOCuota = tf.tasaOCuota.map(BigDecimal.apply),
        importe = tf.importe.map(BigDecimal.apply)
      )
    }))
  }

  private def toRetencion(retencionForm: Option[Seq[v40.RetencionForm]]): Option[Seq[Retencion]] = {
    retencionForm.map(_.map(rf => {
      Retencion(
        impuesto = rf.impuesto,
        importe = BigDecimal(rf.importe)
      )
    }))
  }

  private def toImpuestos(impuestosForm: Option[v40.ImpuestosForm]): Option[Impuestos] = {
    impuestosForm.map(impuestosForm => {
      Impuestos(
        totalImpuestosRetenidos = impuestosForm.totalImpuestosRetenidos.map(BigDecimal(_)),
        totalImpuestosTrasladados = impuestosForm.totalImpuestosTrasladados.map(BigDecimal(_)),
        retenciones = toRetencion(impuestosForm.retenciones),
        traslados = toTraslado(impuestosForm.traslados)
      )
    })
  }

  private def toReceptorComprobante(receptor: Option[ReceptorComprobante40Form]): Option[ReceptorInvoice] = {
    receptor.map(r => {
      ReceptorInvoice(
        rfc = r.rfc,
        nombre = r.nombre,
        domicilioFiscalReceptor = r.domicilioFiscalReceptor,
        residenciaFiscal = r.residenciaFiscal,
        numRegIdTrib = r.numRegIdTrib,
        regimenFiscalReceptor = r.regimenFiscalReceptor,
        usoCfdi = r.usoCfdi
      )
    })
  }

  private def toConceptos(conceptosForm: Seq[Concepto40Form]): Seq[conceptos.Concepto] = {
    conceptosForm.map(cf => {
      conceptos.Concepto(
        claveProductoServicio = cf.claveProductoServicio,
        noIdentificacion = cf.noIdentificacion,
        cantidad = BigDecimal(cf.cantidad),
        claveUnidad = cf.claveUnidad,
        unidad = cf.unidad,
        descripcion = cf.descripcion,
        valorUnitario = BigDecimal(cf.valorUnitario),
        importe = BigDecimal(cf.importe),
        descuento = cf.descuento.map(BigDecimal(_)),
        objetoImp = cf.objetoImp,
        impuestos = toConceptosImpuestos(cf.impuestos),
        aCuentaTerceros = toACuentaTerceros(cf),
        informacionAduanera = cf.informacionAduanera.getOrElse(Nil),
        cuentaPredial = cf.cuentaPredial.getOrElse(Nil),
        parte = toParte(cf)
      )
    })
  }

  private def toParte(cf: Concepto40Form): Seq[Parte] = {
    cf.parte
      .getOrElse(Nil)
      .map(pf => {
        Parte(
          claveProdServ = pf.claveProdServ,
          noIdentificacion = pf.noIdentificacion,
          cantidad = BigDecimal(pf.cantidad),
          unidad = pf.unidad,
          descripcion = pf.descripcion,
          valorUnitario = pf.valorUnitario.map(BigDecimal(_)),
          importe = pf.importe.map(BigDecimal(_)),
          informacionAduanera = toInformacionAduanera(pf)
        )
      })
  }

  private def toInformacionAduanera(pf: ParteForm): Seq[InformacionAduanera] = {
    pf.informacionAduaneraForm.map(iaf => {
      InformacionAduanera(
        numeroPedimento = iaf.numeroPedimento
      )
    })
  }

  private def toACuentaTerceros(cf: Concepto40Form): Option[ACuentaTerceros] = {
    cf.aCuentaTerceros.map(atf => {
      ACuentaTerceros(
        rfcACuentaTerceros = atf.rfcACuentaTerceros,
        nombreACuentaTerceros = atf.nombreACuentaTerceros,
        regimenFiscalACuentaTerceros = atf.regimenFiscalACuentaTerceros,
        domicilioFiscalACuentaTerceros = atf.domicilioFiscalACuentaTerceros
      )
    })
  }

  private def toConceptosImpuestos(conceptosImpuestosForm: Option[v40.ConceptosImpuestos40Form]): Option[ConceptosImpuestos] = {
    conceptosImpuestosForm.map(cif => {
      ConceptosImpuestos(
        traslados = cif.traslados.map(ctf =>
          ConceptoTraslado(
            base = BigDecimal(ctf.base),
            impuesto = ctf.impuesto,
            tipoFactor = ctf.tipoFactor,
            tasaCuota = BigDecimal(ctf.tasaOCuota),
            importe = BigDecimal(ctf.importe)
          )
        ),
        retenciones = cif.retenciones.map(ctf =>
          ConceptoRetencion(
            base = BigDecimal(ctf.base),
            impuesto = ctf.impuesto,
            tipoFactor = ctf.tipoFactor,
            tasaCuota = BigDecimal(ctf.tasaOCuota),
            importe = BigDecimal(ctf.importe)
          )
        )
      )
    })
  }

}
