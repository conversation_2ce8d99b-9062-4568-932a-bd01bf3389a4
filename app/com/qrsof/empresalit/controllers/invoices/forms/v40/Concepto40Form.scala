package com.qrsof.empresalit.controllers.invoices.forms.v40

import com.qrsof.empresalit.controllers.invoices.forms.ACuentaTercerosForm
import com.qrsof.empresalit.controllers.invoices.forms.v40.concepto.ParteForm
import com.qrsof.empresalit.invoicing.domain.conceptos.{CuentaPredial, InformacionAduanera}

case class Concepto40Form(
                           claveProductoServicio: String,
                           noIdentificacion: Option[String] = None,
                           cantidad: String,
                           claveUnidad: String,
                           unidad: Option[String] = None,
                           descripcion: String,
                           valorUnitario: String,
                           importe: String,
                           descuento: Option[String] = None,
                           objetoImp: String,
                           impuestos: Option[ConceptosImpuestos40Form] = None,
                           aCuentaTerceros: Option[ACuentaTercerosForm],
                           informacionAduanera: Option[Seq[InformacionAduanera]],
                           cuentaPredial: Option[Seq[CuentaPredial]],
                           parte: Option[Seq[ParteForm]]
                         )
