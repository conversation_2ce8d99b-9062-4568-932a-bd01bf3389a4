package com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.mercancias

case class TransporteMaritimoForm(
                                   permSCT: Option[String] = None,
                                   numPermisoSCT: Option[String] = None,
                                   nombreAseg: Option[String] = None,
                                   numPolizaSeguro: Option[String] = None,
                                   tipoEmbarcacion: String,
                                   matricula: String,
                                   numeroOMI: String,
                                   anioEmbarcacion: Option[Int] = None,
                                   nombreEmbarc: Option[String] = None,
                                   nacionalidadEmbarc: String,
                                   unidadesDeArqBruto: String,
                                   tipoCarga: String,
                                   numCertITC: String,
                                   eslora: Option[String] = None,
                                   manga: Option[String] = None,
                                   calado: Option[String] = None,
                                   lineaNaviera: Option[String] = None,
                                   nombreAgenteNaviero: String,
                                   numAutorizacionNaviero: String,
                                   numViaje: Option[String] = None,
                                   numConocEmbarc: Option[String] = None,
                                   contenedor: Seq[ContenedorForm] = Nil
                                 )
