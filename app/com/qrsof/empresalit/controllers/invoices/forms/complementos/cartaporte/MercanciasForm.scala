package com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte

import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.mercancias.*

case class MercanciasForm(
                           pesoBrutoTotal: String,
                           unidadPeso: String,
                           pesoNetoTotal: Option[String] = None,
                           numTotalMercancias: Int,
                           cargoPorTasacion: Option[String] = None,
                           mercancia: Seq[MercanciaForm] = Nil,
                           autotransporte: Option[AutotransporteForm] = None,
                           transporteMaritimo: Option[TransporteMaritimoForm] = None,
                           transporteAereo: Option[TransporteAereoForm] = None,
                           transporteFerroviario: Option[TransporteFerroviarioForm] = None
                         )
