package com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.ubicaciones

case class UbicacionForm(
                          tipoUbicacion: String,
                          idUbicacion: Option[String] = None,
                          rfcRemitenteDestinatario: String,
                          nombreRemitenteDestinatario: Option[String] = None,
                          numRegIdTrib: Option[String] = None,
                          residenciaFiscal: Option[String] = None,
                          numEstacion: Option[String] = None,
                          nombreEstacion: Option[String] = None,
                          navegacionTrafico: Option[String] = None,
                          fechaHoraSalidaLlegada: String,
                          tipoEstacion: Option[String] = None,
                          distanciaRecorrida: Option[String] = None,
                          domicilio: Option[DomicilioForm] = None
                        )
