package com.qrsof.empresalit.controllers.invoices.forms.complementos.nomina

case class ReceptorNomForm(
                            curp: String,
                            numSeguridadSocial: Option[String],
                            fechaInicioRelLaboral: Option[String],
                            antiguedad: Option[String],
                            tipoContrato: String,
                            sindicalizado: Option[String],
                            tipoJornada: Option[String],
                            tipoRegimen: String,
                            numEmpleado: String,
                            departamento: Option[String],
                            puesto: Option[String],
                            riesgoPuesto: Option[String],
                            periodicidadPago: String,
                            banco: Option[String],
                            cuentaBancaria: Option[Long],
                            salarioBaseCotApor: Option[String],
                            salarioDiarioIntegrado: Option[String],
                            claveEntFed: String,
                            subContratacion: Seq[SubContratacionForm]
                          )
