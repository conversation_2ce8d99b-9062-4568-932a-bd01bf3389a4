package com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.figuratransporte

case class DomicilioFiguraForm(
                                calleFigura: Option[String] = None,
                                numeroExteriorFigura: Option[String] = None,
                                numeroInteriorFigura: Option[String] = None,
                                coloniaFigura: Option[String] = None,
                                localidadFigura: Option[String] = None,
                                referenciaFigura: Option[String] = None,
                                municipioFigura: Option[String] = None,
                                estadoFigura: String,
                                paisFigura: String,
                                codigoPostalFigura: String
                              )
