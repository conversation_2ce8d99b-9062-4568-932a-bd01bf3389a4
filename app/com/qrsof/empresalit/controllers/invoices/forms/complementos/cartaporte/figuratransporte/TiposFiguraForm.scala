package com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.figuratransporte

case class TiposFiguraForm(
                            tipoFigura: String,
                            rfcFigura: Option[String] = None,
                            numLicencia: Option[String] = None,
                            nombreFigura: Option[String] = None,
                            numRegIdTribFigura: Option[String] = None,
                            residenciaFiscalFigura: Option[String] = None,
                            partesTransporte: Option[Seq[PartesTransporteForm]],
                            domicilio: Option[DomicilioFiguraForm] = None
                          )
