package com.qrsof.empresalit.controllers.invoices.forms.complementos.nomina

case class NominaCompForm(
                           version: String,
                           tipoNomina: String,
                           fechaPago: String,
                           fechaInicialPago: String,
                           fechaFinalPago: String,
                           numDiasPagados: String,
                           totalPercepciones: Option[String] = None,
                           totalDeducciones: Option[String] = None,
                           totalOtrosPagos: Option[String] = None,
                           emisor: Option[EmisorForm] = None,
                           receptorNom: ReceptorNomForm,
                           percepciones: Option[PercepcionesForm] = None,
                           deducciones: Option[DeduccionesForm] = None,
                           otrosPagos: Option[OtrosPagosForm] = None,
                           incapacidades: Option[IncapacidadesForm] = None
                         )
