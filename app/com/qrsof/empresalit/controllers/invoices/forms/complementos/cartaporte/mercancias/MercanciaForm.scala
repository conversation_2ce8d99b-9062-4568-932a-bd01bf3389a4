package com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.mercancias

case class MercanciaForm(
                          bienesTransp: String,
                          clavesSTCC: Option[String] = None,
                          descripcion: String,
                          cantidad: String,
                          claveUnidad: String,
                          unidad: Option[String] = None,
                          dimensiones: Option[String] = None,
                          materialPeligroso: Option[String] = None,
                          cveMaterialPeligroso: Option[String] = None,
                          embalaje: Option[String] = None,
                          descripEmbalaje: Option[String] = None,
                          pesoEnKg: String,
                          valorMercancia: Option[String] = None,
                          moneda: Option[String] = None,
                          fraccionArancelaria: Option[String] = None,
                          uuidComercioExt: Option[String] = None,
                          pedimentos: Option[Seq[PedimentoForm]],
                          guiasIdentificacion: Option[Seq[GuiasIdentificacionForm]],
                          cantidadTransporta: Option[Seq[CantidadTransportaForm]],
                          detalleMercancia: Option[Seq[DetalleMercanciaForm]]
                        )
