package com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.mercancias

case class TransporteAereoForm(
                                permSCT: String,
                                numPermisoSCT: String,
                                matriculaAeronave: Option[String] = None,
                                nombreAseg: Option[String] = None,
                                numPolizaSeguro: Option[String] = None,
                                numeroGuia: String,
                                lugarContrato: Option[String] = None,
                                codigoTransportista: String,
                                rfcEmbarcador: Option[String] = None,
                                numRegIdTribEmbarc: Option[String] = None,
                                residenciaFiscalEmbarc: Option[String] = None,
                                nombreEmbarcador: Option[String] = None
                              )
