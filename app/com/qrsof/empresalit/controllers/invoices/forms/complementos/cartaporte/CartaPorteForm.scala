package com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte

case class CartaPorteForm(
                           version: String = "2.0",
                           transpInternac: String,
                           entradaSalidaMerc: Option[String] = None,
                           paisOrigenDestino: Option[String] = None,
                           viaEntradaSalida: Option[String] = None,
                           totalDistRec: Option[String] = None,
                           ubicaciones: UbicacionesForm,
                           mercancias: MercanciasForm,
                           figuratransporte: Option[FiguratransporteForm] = None,
                         )
