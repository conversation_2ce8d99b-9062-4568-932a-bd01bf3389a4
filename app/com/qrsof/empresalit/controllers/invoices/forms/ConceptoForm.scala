package com.qrsof.empresalit.controllers.invoices.forms

import com.qrsof.empresalit.invoicing.domain.conceptos.{CuentaPredial, InformacionAduanera}

case class ConceptoForm(
                         claveProductoServicio: String,
                         noIdentificacion: Option[String] = None,
                         cantidad: String,
                         claveUnidad: String,
                         unidad: Option[String] = None,
                         descripcion: String,
                         valorUnitario: String,
                         importe: String,
                         descuento: Option[String] = None,
                         impuestos: Option[ConceptosImpuestosForm] = None,
                         informacionAduanera: Option[Seq[InformacionAduanera]],
                         cuentaPredial: Option[CuentaPredial] = None
                       )
