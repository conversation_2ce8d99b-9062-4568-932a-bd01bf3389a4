package com.qrsof.empresalit.controllers.invoices.v40

import com.qrsof.empresalit.controllers.invoices.forms.v40.{FormsMapper, GenerateInvoice40Form}
import com.qrsof.empresalit.invoicing.generateinvoice.v40.GenerateInvoiceAction
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}
import org.apache.pekko.stream.scaladsl.StreamConverters
import java.io.ByteArrayInputStream

@Singleton
class GenerateInvoiceV40Impl @Inject()(
                                        generateInvoiceAction: GenerateInvoiceAction,
                                        formsMapper: FormsMapper
                                      )() extends GenerateInvoiceV40 {

  val logger: Logger = LoggerFactory.getLogger(classOf[GenerateInvoiceV40Impl])

  override def execute(generateInvoiceForm: GenerateInvoice40Form, userKey: String): Array[Byte] = {

    val companyKey = userKey
    val xml: Array[Byte] = generateInvoiceAction.execute(
      formsMapper.toGenerateInvoiceRequest(companyKey, generateInvoiceForm)
    )
    logger.info(s"XML lenght: ${xml.length}")
    StreamConverters.fromInputStream(() => new ByteArrayInputStream(xml))

    xml // TODO: Falta respoder con el XML
  }

}
