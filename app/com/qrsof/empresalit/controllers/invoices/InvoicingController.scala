package com.qrsof.empresalit.controllers.invoices

import com.qrsof.empresalit.controllers.invoices.forms.v40.GenerateInvoice40Form
import com.qrsof.empresalit.controllers.invoices.forms.{GenerateInvoiceForm, InvoiceCancelationForm}
import com.qrsof.empresalit.invoicing.actions.getinvoicestatus.InvoiceStatusResponse
import com.qrsof.empresalit.invoicing.cancelation.InvoiceCancelationResponse
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import play.api.mvc.{Action, AnyContent}

trait InvoicingController {
  @Path("/companies/invoices/{uuid}")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Retrieve Invoice Meta",
    tags = Array("Invoicing"),
    parameters = Array(
      new Parameter(
        name = "uuid",
        in = ParameterIn.PATH,
        required = true,
        description = "UUID Invoice",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      )
    ),
    responses = Array(
      new ApiResponse(responseCode = "200", content = Array(new Content(schema = new Schema(implementation = classOf[InvoiceMetadataView])))),
      new ApiResponse(responseCode = "404", content = Array(new Content(schema = new Schema(implementation = classOf[String]))))
    )
  )
  def retrieveInvoiceMetadata(@Parameter(hidden = true) uuid: String): Action[AnyContent]

  @Path("/companies/invoices/{uuid}")
  @DELETE
  @Consumes(Array(MediaType.APPLICATION_FORM_URLENCODED))
  @Produces(Array(MediaType.APPLICATION_OCTET_STREAM))
  @Operation(
    summary = "Cancel Invoice Stamp",
    tags = Array("Invoicing"),
    parameters = Array(
      new Parameter(
        name = "uuid",
        in = ParameterIn.PATH,
        required = true,
        description = "UUID Invoice",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      )
    ),
    requestBody = new RequestBody(
      required = true,
      description = "The motivoCancelacion values are 01, 02, 03 and 04",
      content = Array(new Content(schema = new Schema(implementation = classOf[InvoiceCancelationForm])))
    ),
    responses = Array(
      new ApiResponse(responseCode = "200", content = Array(new Content(schema = new Schema(implementation = classOf[InvoiceCancelationResponse])))),
      new ApiResponse(responseCode = "404", content = Array(new Content(schema = new Schema(implementation = classOf[String]))))
    )
  )
  def cancelInvoice(@Parameter(hidden = true) uuid: String): Action[AnyContent]

  @Path("/companies/invoices")
  @POST
  @Consumes(Array(MediaType.APPLICATION_JSON, MediaType.APPLICATION_FORM_URLENCODED))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Generate Invoice",
    tags = Array("Invoicing"),
    parameters = Array(
      new Parameter(
        name = "Accept",
        in = ParameterIn.HEADER,
        required = true,
        description = "Set stamp version 4.0 (application/com.qrsof.empresalit.invoice.v40+json) o 3.3 (application/com.qrsof.empresalit.invoice.v33+json)",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      )
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(new Content(schema = new Schema(implementation = classOf[GenerateInvoiceForm])))
    ),
    responses = Array(
      new ApiResponse(responseCode = "200", content = Array(new Content(schema = new Schema(implementation = classOf[Array[Byte]])))),
      new ApiResponse(responseCode = "404", content = Array(new Content(schema = new Schema(implementation = classOf[String]))))
    )
  )
  def generateInvoice: Action[AnyContent]

  @Path("/companies/invoices")
  @POST
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Generate stamp invoice",
    tags = Array("Invoicing"),
    parameters = Array(
      new Parameter(
        name = "Accept",
        in = ParameterIn.HEADER,
        required = true,
        description = "Set stamp version 4.0 (application/com.qrsof.empresalit.invoice.v40+json) o 3.3 (application/com.qrsof.empresalit.invoice.v33+json)",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      )
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(new Content(schema = new Schema(implementation = classOf[GenerateInvoice40Form])))
    ),
    responses = Array(
      new ApiResponse(responseCode = "200", content = Array(new Content(schema = new Schema(implementation = classOf[Array[Byte]]))))
    )
  )
  def generateInvoice40: Action[AnyContent]

  @Path("/companies/invoices/qrcode")
  @GET
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get QrCode Invoice",
    tags = Array("Invoicing"),
    parameters = Array(
      new Parameter(
        name = "id",
        in = ParameterIn.QUERY,
        required = true,
        description = "UUID Invoice",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      ),
      new Parameter(
        name = "re",
        in = ParameterIn.QUERY,
        required = true,
        description = "Rfc of Transmitter",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      ),
      new Parameter(
        name = "rr",
        in = ParameterIn.QUERY,
        required = false,
        description = "Rfc of Receiver",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      ),
      new Parameter(
        name = "nr",
        in = ParameterIn.QUERY,
        required = false,
        description = "Client Key",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      ),
      new Parameter(
        name = "tt",
        in = ParameterIn.QUERY,
        required = true,
        description = "Total Amount of Invoice",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      ),
      new Parameter(
        name = "fe",
        in = ParameterIn.QUERY,
        required = true,
        description = "Sello of Invoice",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      )
    ),
    responses = Array(
      new ApiResponse(responseCode = "200", content = Array(new Content(schema = new Schema(implementation = classOf[Array[Byte]])))),
      new ApiResponse(responseCode = "404", content = Array(new Content(schema = new Schema(implementation = classOf[String]))))
    )
  )
  def generateQrCodeByParameters(
      @Parameter(hidden = true) id: String,
      @Parameter(hidden = true) re: String,
      @Parameter(hidden = true) rr: Option[String],
      @Parameter(hidden = true) nr: Option[String],
      @Parameter(hidden = true) tt: String,
      @Parameter(hidden = true) fe: String
  ): Action[AnyContent]

  @Path("/companies/invoices/{uuid}/status")
  @GET
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get Invoice Status By UUID",
    tags = Array("Invoicing"),
    parameters = Array(
      new Parameter(
        name = "uuid",
        in = ParameterIn.PATH,
        required = true,
        description = "UUID Invoice",
        content = Array(new Content(schema = new Schema(implementation = classOf[String])))
      )
    ),
    responses = Array(
      new ApiResponse(responseCode = "200", content = Array(new Content(schema = new Schema(implementation = classOf[InvoiceStatusResponse])))),
      new ApiResponse(responseCode = "404", content = Array(new Content(schema = new Schema(implementation = classOf[String]))))
    )
  )
  def getInvoiceStatus(
      @Parameter(hidden = true) uuid: String,
      @Parameter(hidden = true) rfcReceptor: String,
      @Parameter(hidden = true) total: String,
      @Parameter(hidden = true) sello: String
  ): Action[AnyContent]
}
