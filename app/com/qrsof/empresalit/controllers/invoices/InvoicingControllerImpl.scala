package com.qrsof.empresalit.controllers.invoices

import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.controllers.invoices.forms.v40.GenerateInvoice40Form
import com.qrsof.empresalit.controllers.invoices.forms.GenerateInvoiceForm
import com.qrsof.empresalit.controllers.invoices.v33.GenerateInvoiceV33
import com.qrsof.empresalit.controllers.invoices.v40.GenerateInvoiceV40
import jakarta.inject.*
import org.apache.pekko.http.scaladsl.model.MediaTypes
import org.slf4j.{Logger, LoggerFactory}
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

@Singleton
class InvoicingControllerImpl @Inject() (
    generateInvoiceV40: GenerateInvoiceV40,
    generateInvoiceV30: GenerateInvoiceV33,
    authenticatedAction: AuthenticatedAction,
    cc: ControllerComponents
) extends AbstractController(cc)
    with InvoicingController
    with BasePlayController {

  val logger: Logger = LoggerFactory.getLogger(classOf[InvoicingControllerImpl])

  override def generateInvoice: Action[AnyContent] = authenticatedAction { implicit request =>
    val generateInvoiceForm: GenerateInvoiceForm = bindFromRequest[GenerateInvoiceForm]().get
    val userKey = request.principal
    val result: Array[Byte] = generateInvoiceV30.execute(generateInvoiceForm, userKey)

    Ok(result)
  }

  override def generateInvoice40: Action[AnyContent] = authenticatedAction { implicit request =>
    val generateInvoiceForm: GenerateInvoice40Form = bindFromRequest[GenerateInvoice40Form]().get
    val userKey = request.principal
    val result: Array[Byte] = generateInvoiceV40.execute(generateInvoiceForm, userKey)

    Ok(result).as(MediaTypes.`application/octet-stream`.value)
  }

  override def generateQrCodeByParameters(id: String, re: String, rr: Option[String] = None, nr: Option[String] = None, tt: String, fe: String): Action[AnyContent] = ???
  override def cancelInvoice(uuidToCancel: String): Action[AnyContent] = ???

  override def retrieveInvoiceMetadata(uuid: String): Action[AnyContent] = ???

  def getInvoiceStatus(uuid: String, rfcReceptor: String, total: String, sello: String): Action[AnyContent] = ???

}
