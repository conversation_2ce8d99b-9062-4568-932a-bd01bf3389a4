package com.qrsof.empresalit.controllers.invoices

/*package com.qrsof.empresalit.controllers.invoices

import com.qrsof.empresalit.controllers.invoices.forms.InvoiceCancelationForm
import com.qrsof.empresalit.controllers.invoices.forms.getinvoicestatus.GetInvoiceStatusForm


trait InvoicingControllerForms {
	val generateCancelationForm: Form[InvoiceCancelationForm] = Form(
		mapping(
			"motivoCancelacion" -> optional(text),
			"uuidSustitucion" -> optional(text)
		)(InvoiceCancelationForm.apply)(InvoiceCancelationForm.unapply))

	val getInvoiceStatusForm: Form[GetInvoiceStatusForm] = Form(
		mapping(
			"rr" -> nonEmptyText,
			"tt" -> nonEmptyText,
			"fe" -> nonEmptyText,
		)(GetInvoiceStatusForm.apply)(GetInvoiceStatusForm.unapply)
	);

}*/
