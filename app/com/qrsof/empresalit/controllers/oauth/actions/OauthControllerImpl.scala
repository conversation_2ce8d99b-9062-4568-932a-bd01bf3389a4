package com.qrsof.empresalit.controllers.oauth.actions

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.controllers.oauth.*
import com.qrsof.empresalit.controllers.oauth.OauthExceptionObj.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}
import play.api.libs.json.{Json, Reads}
import play.api.mvc.*
@Singleton
class OauthControllerImpl @Inject() (oauthActions: OauthActions, authenticatedAction: AuthenticatedAction, cc: ControllerComponents)
    extends AbstractController(cc)
    with OauthController
    with BasePlayController {

  val logger: Logger = LoggerFactory.getLogger(classOf[OauthActionsImpl])

  // JSON formatters
  implicit val registerFormReads: Reads[RegisterForm] = Json.reads[RegisterForm]
  implicit val loginFormReads: Reads[LoginForm] = Json.reads[LoginForm]

  override def register(): Action[AnyContent] = Action { implicit request =>
    val registerForm = request.body.asJson.get.as[RegisterForm]
    oauthActions.register(registerForm.transformInto[RegisterActionRequest]) match {
      case Left(oauthException) => managementException(oauthException)
      case Right(jwtToken)      => Ok(jwtToken.toJsValue())
    }
  }

  override def login(): Action[AnyContent] = Action { implicit request =>
    val loginForm = request.body.asJson.get.as[LoginForm]
    oauthActions.login(loginForm.transformInto[LoginActionRequest]) match {
      case Left(oauthException) => managementException(oauthException)
      case Right(jwtToken)      => Ok(jwtToken.toJsValue())
    }
  }

  override def getUserData(): Action[AnyContent] = authenticatedAction { implicit request =>
    val key = request.principal
  oauthActions.getUserDataAction(key) match {
    case Left(oauthException) =>
      managementException(oauthException)
    case Right(account) => Ok(account.toJsValue())
  }
  }

  private def managementException(oauthException: OauthException): Result = {
    oauthException match {
      case e: OauthUserNotFoundException => Results.NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
      case e: UserCredentialsException   => Results.Unauthorized(ApiErrorResponse(Seq(e.appError)).toJsValue())
      case e: UserAlreadyExistsException => Results.Conflict(ApiErrorResponse(Seq(e.appError)).toJsValue())
      case e: UnknownException           => Results.InternalServerError(ApiErrorResponse(Seq(e.appError)).toJsValue())
      case e: MalformedGoogleTokenException =>
        Results.BadRequest(ApiErrorResponse(Seq(e.appError)).toJsValue())
    }
  }
}
