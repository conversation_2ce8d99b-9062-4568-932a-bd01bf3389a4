package com.qrsof.empresalit.controllers.oauth.actions

import com.qrsof.core.api.ApiErrorResponse
import io.swagger.v3.oas.annotations.*
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import play.api.mvc.{Action, AnyContent}

trait OauthController {
  @Path("/empresalit/oauth/register")
  @POST
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Register user",
    tags = Array("Oauth"),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[RegisterForm])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[RegisteredApiResponse])
          )
        )
      ),
      new ApiResponse(
        responseCode = "401",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    ),
  )
  def register(): Action[AnyContent]

  @Path("/empresalit/oauth/login")
  @POST
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Login user",
    tags = Array("Oauth"),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[LoginForm])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[LoggedInApiResponse]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "401",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    )
  )
  def login(): Action[AnyContent]

  @Path("/empresalit/oauth/account")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get user info",
    description = "Get a specific user in workspace by workspace key in path, that belongs to an user when the view render, protected by JWT token user",
    tags = Array("Oauth"),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[UserForm]
            )
          )
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    )
  )
  def getUserData(): Action[AnyContent]
}
