package com.qrsof.empresalit.controllers.resources

import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.core.MediaType
import io.swagger.v3.oas.annotations.enums.ParameterIn
import jakarta.ws.rs.*
import play.api.mvc.{Action, AnyContent}

trait ResourcesController {
	@Path("company/{companyKey}/resources/{resourceKey}")
	@GET
	@Produces(Array(MediaType.APPLICATION_OCTET_STREAM))
	@Operation(
		summary = "Get resource by key",
		description = "Get resource",
		tags = Array("Resource"),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[Byte]
						)
					)
				)
			)
		),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "company key",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),new Parameter(
				name = "resourceKey",
				in = ParameterIn.PATH,
				required = true,
				description = "resource key",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		)
	)
	def getResourceByCompanyKeyAndResourceKey(companyKey: String, resourceKey: String): Action[AnyContent]
}
