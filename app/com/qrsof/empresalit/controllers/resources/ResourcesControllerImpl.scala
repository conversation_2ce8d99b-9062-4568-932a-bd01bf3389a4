package com.qrsof.empresalit.controllers.resources

import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.empresalit.domain.resources.ResourcesService
import jakarta.inject.{Inject, Singleton}
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}
import org.slf4j.{Logger, LoggerFactory}

@Singleton
class ResourcesControllerImpl @Inject() (cc: ControllerComponents, resourcesService: ResourcesService) extends AbstractController(cc) with ResourcesController with BasePlayController {

  val logger: Logger = LoggerFactory.getLogger(classOf[ResourcesControllerImpl])
  override def getResourceByCompanyKeyAndResourceKey(companyKey: String, resourceKey: String): Action[AnyContent] = Action { implicit request =>
    try {
      val resource = resourcesService.getResourceByCompanyKeyAndResourceKey(companyKey, resourceKey)
      Ok(resource.file).withHeaders("Content-Disposition" -> "attachment;filename=".concat(resource.name))
    } catch {
      case cause: Exception => {
        BadRequest(cause.toJsValue())
      }

    }
  }
}
