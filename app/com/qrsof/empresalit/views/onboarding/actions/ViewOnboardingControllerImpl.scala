package com.qrsof.empresalit.views.onboarding.actions

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.apache.pekko.http.scaladsl.common.StrictForm
import org.apache.pekko.http.scaladsl.model.{ContentTypes, HttpEntity}
import org.apache.pekko.util.ByteString
import org.slf4j.{Logger, LoggerFactory}
import play.api.libs.Files
import play.api.libs.Files.TemporaryFile
import play.api.libs.json.{Json, Reads}
import play.api.mvc.*

@Singleton
class ViewOnboardingControllerImpl @Inject() (
    cc: ControllerComponents,
    authenticatedAction: AuthenticatedAction,
    viewCompanyActions: ViewCompanyActions
) extends AbstractController(cc)
    with ViewOnboardingController
    with BasePlayController {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewOnboardingControllerImpl])

  implicit val dataCompanyFormViewReads: Reads[DataCompanyFormView] = Json.reads[DataCompanyFormView]
  implicit val companyAddressFormViewReads: Reads[CompanyAddressFormView] = Json.reads[CompanyAddressFormView]
  implicit val brandCompanyFormViewReads: Reads[BrandCompanyFormView] = Json.reads[BrandCompanyFormView]
  implicit val newCompanyFormViewReads: Reads[NewCompanyFormView] = Json.reads[NewCompanyFormView]

  override def createNewCompany: Action[MultipartFormData[TemporaryFile]] = authenticatedAction(parse.multipartFormData) { implicit request =>
    val newCompanyRequest = request.body.asFormUrlEncoded
    request.body
    val userKey = request.principal
    val private_key: Option[StrictForm.FileData] = request.body.file("private_key").map { file =>
      val byteArray = java.nio.file.Files.readAllBytes(file.ref.path)
      val byteString = ByteString(byteArray)
      this.buildFileData(file.filename, byteString)
    }
    val public_key: Option[StrictForm.FileData] = request.body.file("public_key").map { file =>
      val byteArray = java.nio.file.Files.readAllBytes(file.ref.path)
      val byteString = ByteString(byteArray)
      this.buildFileData(file.filename, byteString)
    }
    val password = newCompanyRequest.get("password").flatMap(_.headOption).getOrElse("")
    val newCompanyForm: NewCompanyFormView = request.body
      .file("companyForm")
      .map { file =>
        Json.parse(java.nio.file.Files.readAllBytes(file.ref.path)).toModel[NewCompanyFormView]()
      }
      .get

    val certificates =
      CompanyCertificatesFormView(private_key.getOrElse(this.buildFileData("", ByteString("".getBytes))), public_key.getOrElse(this.buildFileData("", ByteString("".getBytes))), password)
    val logoCompany: Option[StrictForm.FileData] = request.body.file("logo_company").map { logoFile =>
      val byteArray = java.nio.file.Files.readAllBytes(logoFile.ref.path)
      val byteString = ByteString(byteArray)
      this.buildFileData(logoFile.filename, byteString)
    }

    viewCompanyActions.addNewCompany(
      newCompanyForm
        .into[NewCompanyActionRequestView]
        .withFieldConst(_.logoCompany, logoCompany)
        .withFieldConst(_.certificates, certificates.transformInto[CompanyCertificatesActionRequestView])
        .withFieldConst(_.userKey, userKey)
        .transform
    ) match {
      case Left(newCompanyException)     => BadRequest(ApiErrorResponse(Seq(newCompanyException.appError)).toJsValue())
      case Right(createdCompanyResponse) => Ok(CreatedCompanyApiResponseView(createdCompanyResponse.key).toJsValue())
    }
  }

  private def buildFileData(filename: String, byteString: ByteString): StrictForm.FileData = {
    StrictForm.FileData(Some(filename), HttpEntity.Strict(ContentTypes.`application/octet-stream`, byteString))
  }
}
