package com.qrsof.empresalit.views.onboarding.actions

import com.qrsof.core.api.ApiErrorResponse
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.Operation
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.*
import play.api.libs.Files.TemporaryFile
import play.api.mvc.{Action, MultipartFormData}


trait ViewOnboardingController {
  @Path("views/onboarding")
  @POST
  @Consumes(Array(MediaType.MULTIPART_FORM_DATA))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Create a new company",
    tags = Array("Views"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[CompanyFormDataView])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[CreatedCompanyApiResponseView])
          )
        )
      ),
      new ApiResponse(
        responseCode = "500",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    ),
  )
  def createNewCompany: Action[MultipartFormData[TemporaryFile]]

}
