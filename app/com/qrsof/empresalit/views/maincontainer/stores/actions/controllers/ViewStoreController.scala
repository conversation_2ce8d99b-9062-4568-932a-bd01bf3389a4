package com.qrsof.empresalit.views.maincontainer.stores.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.empresalit.views.maincontainer.stores.actions.{NewStoreResponse, Store}
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.*
import play.api.mvc.{Action, AnyContent}

trait ViewStoreController {
  @Path("/views/main-container/{companyKey}/store")
  @POST
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Created a new store",
    tags = Array("Stores"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[StoreDataForm])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[NewStoreResponse])
          )
        )
      ),
      new ApiResponse(
        responseCode = "500",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    )
  )
  def addStore(@Parameter(hidden = true) companyKey: String): Action[AnyContent]

  @Path("/views/main-container/{company_key}/store")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get store by key",
    description = "Get store, protected by JWT token user",
    tags = Array("Stores"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "page",
        in = ParameterIn.QUERY,
        required = false,
        description = "Number of Page, position",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "pageSize",
        in = ParameterIn.QUERY,
        required = false,
        description = "Page size enables you to specify the max number of rows to be returned in response",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "searchBar",
        in = ParameterIn.QUERY,
        required = false,
        description = "searchBar, name or rfc for any search",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[Store]
            )
          )
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    )
  )
  def getStore(@Parameter(hidden = true) company_key: String, @Parameter(hidden = true) page: Int, @Parameter(hidden = true) pageSize: Int, 
               @Parameter(hidden = true) searchBar: Option[String]): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/store/{store_key}")
  @PUT
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Update a store",
    tags = Array("Stores"),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "store_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of store",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = false,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[String])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      )
    )
  )
  def updateStore(@Parameter(hidden = true) company_key: String, @Parameter(hidden = true) store_key: String): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/store/{store_key}")
  @DELETE
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Delete a store",
    description = "Delete store",
    tags = Array("Stores"),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "store_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of store",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[NewStoreResponse]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      )
    )
  )
  def deleteStore(@Parameter(hidden = true) company_key: String, @Parameter(hidden = true) store_key: String): Action[AnyContent]
}
