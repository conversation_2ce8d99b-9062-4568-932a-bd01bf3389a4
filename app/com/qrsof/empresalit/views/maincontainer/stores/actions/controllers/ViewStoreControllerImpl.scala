package com.qrsof.empresalit.views.maincontainer.stores.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.views.maincontainer.actions.{UserCompaniesView, ViewMainContainerActions}
import com.qrsof.empresalit.views.maincontainer.stores.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

@Singleton
class ViewStoreControllerImpl @Inject() (viewStoreActions: ViewStoreActions, viewMainContainerActions: ViewMainContainerActions, authenticatedAction: AuthenticatedAction, cc: ControllerComponents)
    extends AbstractController(cc)
    with ViewStoreController
    with BasePlayController {

  override def addStore(companyKey: String): Action[AnyContent] = authenticatedAction { implicit request =>
    val storeData = bindFromRequest[StoreDataForm]().get
    val userKey = request.principal

    val companies: Seq[UserCompaniesView] = viewMainContainerActions.getUserCompanies(userKey)
    viewStoreActions.addStore(
      storeData
        .into[NewStoreAction]
        .withFieldConst(_.company_key, companyKey)
        .withFieldConst(_.user_key, userKey)
        .withFieldConst(_.company_key, companies.head.key)
        .transform
    ) match {
      case Left(newStoreException) =>
        newStoreException match {
          case e: UnknownErrorView => InternalServerError(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(store) =>
        val newStoreResponse = NewStoreResponse(store.key)
        Ok(newStoreResponse.toJsValue())
    }
  }

  override def getStore(company_key: String, page: Int, pageSize: Int, searchBar: Option[String]): Action[AnyContent] = Action { implicit request =>
    val filters = FiltersStoreActionRequest(
      company_key,
      page,
      pageSize,
      searchBar
    )
    val response: Stores = viewStoreActions
      .getStore(
        filters
      )
      .into[Stores]
      .transform
    Ok(response.toJsValue())
  }

  override def updateStore(company_key: String, store_key: String): Action[AnyContent] = Action { implicit request =>
    val generalDataInStoreView = bindFromRequest[GeneralDataInStoreView]().get
    val response = viewStoreActions.updateStore(
      generalDataInStoreView
        .into[GeneralDtoInStoreActionRequest]
        .withFieldConst(_.store_key, store_key)
        .withFieldConst(_.company_key, company_key)
        .transform
    )
    response match {
      case Left(error) =>
        error match {
          case e: StoreNotFoundExceptionUpdate => InternalServerError(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  override def deleteStore(company_key: String, store_key: String): Action[AnyContent] = Action { implicit request =>
    viewStoreActions.deleteStore(store_key) match {
      case Left(value) =>
        value match {
          case e: StoreNotFoundException => NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }
}
