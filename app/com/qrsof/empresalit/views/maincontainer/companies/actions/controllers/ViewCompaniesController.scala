package com.qrsof.empresalit.views.maincontainer.companies.actions.controllers

import com.qrsof.empresalit.views.maincontainer.companies.action.CompaniesResponse
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import play.api.mvc.{Action, AnyContent}

trait ViewCompaniesController {

  @Path("/views/main-container/{company_key}/companies")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get companies by key",
    description = "Get company, protected by JWT token user",
    tags = Array("Views"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[CompaniesResponse]
            )
          )
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    )
  )
  def getCompanies(@Parameter(hidden = true) company_key: String): Action[AnyContent]
}
