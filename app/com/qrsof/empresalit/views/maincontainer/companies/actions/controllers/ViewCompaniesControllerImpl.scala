package com.qrsof.empresalit.views.maincontainer.companies.actions.controllers

import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.empresalit.views.maincontainer.companies.action.{CompanyWithTotal, ViewCompaniesAction}
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

@Singleton
class ViewCompaniesControllerImpl @Inject() (viewCompaniesAction: ViewCompaniesAction, cc: ControllerComponents) extends AbstractController(cc) with ViewCompaniesController with BasePlayController {
  override def getCompanies(company_key: String): Action[AnyContent] = Action { implicit request =>
    val response: CompanyWithTotal = viewCompaniesAction.getCompanies(company_key).into[CompanyWithTotal].transform
    Ok(response.toJsValue())
  }
}
