package com.qrsof.empresalit.views.maincontainer.quote.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.views.maincontainer.actions.ViewMainContainerActions
import com.qrsof.empresalit.views.maincontainer.clients.actions.ViewClientsActions
import com.qrsof.empresalit.views.maincontainer.quote.actions.*
import com.qrsof.empresalit.views.maincontainer.stores.actions.ViewStoreActions
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.LoggerFactory
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

@Singleton
class ViewQuoteControllerImpl @Inject() (
    viewQuoteActions: ViewQuoteActions,
    viewMainContainerActions: ViewMainContainerActions,
    viewStoreActions: ViewStoreActions,
    viewClientsActions: ViewClientsActions,
    authenticatedAction: AuthenticatedAction,
    cc: ControllerComponents
) extends AbstractController(cc)
    with ViewQuoteController
    with BasePlayController {
  private val log = LoggerFactory.getLogger(classOf[ViewQuoteControllerImpl])

  override def addQuote(company_key: String): Action[AnyContent] = authenticatedAction { implicit request =>
    val quoteForm = bindFromRequest[QuoteDataForm]().get
    val userKey = request.principal

    viewQuoteActions.addQuote(
      quoteForm
        .into[NewQuoteAction]
        .withFieldConst(_.store_key, quoteForm.store_key)
        .withFieldConst(_.client_key, quoteForm.client_key)
        .withFieldConst(_.user_key, userKey)
        .withFieldComputed(_.status, newFactureAction => QuoteStatus.withName(newFactureAction.status))
        .transform
    ) match {
      case Left(newQuoteException) =>
        log.error(newQuoteException.appError.error.getOrElse("InternalServerError"))
        log.error(newQuoteException.appError.detail.getOrElse("InternalServerError"))
        newQuoteException match {
          case e: UnknownErrorView =>
            InternalServerError(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(quote) =>
        val newQuoteResponse = NewQuoteResponse(quote.key)
        Ok(newQuoteResponse.toJsValue())
    }
  }

  override def getQuote(
      company_key: String,
      page: Int,
      pageSize: Int,
      searchBar: Option[String],
      dateSearch: Option[String],
      store_key: Option[String],
      client_key: Option[String]
  ): Action[AnyContent] = Action { implicit request =>
    val filters = FilterDefaultQuote(
      store_key,
      client_key,
      company_key,
      page,
      pageSize,
      searchBar,
      dateSearch
    )
    val response: Quotations = viewQuoteActions
      .getQuote(
        filters
      )
      .into[Quotations]
      .transform
    Ok(response.toJsValue())
  }

  override def getAllQuote(company_key: String): Action[AnyContent] = Action { implicit request =>
    val response: Quotations = viewQuoteActions
      .getAllQuote(Option(company_key))
      .into[Quotations]
      .transform
    Ok(response.toJsValue())
  }

  override def updateQuote(quote_key: String, company_key: String): Action[AnyContent] = Action { implicit request =>
    val generalDataInQuoteView = bindFromRequest[GeneralDataInQuoteView]().get

    val response = viewQuoteActions.updateQuote(
      generalDataInQuoteView
        .into[GeneralDtoInQuoteActionRequest]
        .withFieldConst(_.quote_key, quote_key)
        .withFieldConst(_.store_key, generalDataInQuoteView.store_key)
        .withFieldConst(_.client_key, generalDataInQuoteView.client_key)
        .withFieldComputed(_.status, newFactureAction => QuoteStatus.withName(newFactureAction.status))
        .transform
    )
    response match {
      case Left(error) =>
        error match {
          case e: QuoteNotFoundExceptionUpdate =>
            InternalServerError(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  override def deleteQuote(quote_key: String, company_key: String): Action[AnyContent] = Action { implicit request =>
    viewQuoteActions.deleteQuote(quote_key) match {
      case Left(value) =>
        value match {
          case e: QuoteDeleteException => NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  override def markAsFactureQuote(quote_key: String, company_key: String): Action[AnyContent] = Action { implicit request =>
    viewQuoteActions.markAsFacture(quote_key) match {
      case Left(value) =>
        value match {
          case e: QuoteNotFoundExceptionUpdate =>
            NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  override def markAsRejectQuote(quote_key: String, company_key: String): Action[AnyContent] = Action { implicit request =>
    viewQuoteActions.markAsReject(quote_key) match {
      case Left(value) =>
        value match {
          case e: QuoteNotFoundExceptionUpdate =>
            NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  override def markAsPendingQuote(quote_key: String, company_key: String): Action[AnyContent] = Action { implicit request =>
    viewQuoteActions.markAsPending(quote_key) match {
      case Left(value) =>
        value match {
          case e: QuoteNotFoundExceptionUpdate =>
            NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }
}
