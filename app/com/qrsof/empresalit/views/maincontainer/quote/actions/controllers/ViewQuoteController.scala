package com.qrsof.empresalit.views.maincontainer.quote.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.empresalit.views.maincontainer.quote.actions.NewQuoteResponse
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import play.api.mvc.{Action, AnyContent}

trait ViewQuoteController {
  @Path("/views/main-container/{company_key}/quote")
  @POST
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Created a new quote",
    tags = Array("Quote"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[QuoteDataForm])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[NewQuoteResponse])
          )
        )
      ),
      new ApiResponse(
        responseCode = "500",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    )
  )
  def addQuote(@Parameter(hidden = true) company_key: String): Action[AnyContent]

  @Path("/views/main-container/{company_key}/quote?")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get quote by key",
    description = "Get quote, protected by JWT token user",
    tags = Array("Quote"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "page",
        in = ParameterIn.QUERY,
        required = false,
        description = "Number of Page, position",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "pageSize",
        in = ParameterIn.QUERY,
        required = false,
        description = "Page size enables you to specify the max number of rows to be returned in response",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "searchBar",
        in = ParameterIn.QUERY,
        required = false,
        description = "searchBar, name for any search",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "store_key",
        in = ParameterIn.QUERY,
        required = false,
        description = "store_key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "client_key",
        in = ParameterIn.QUERY,
        required = false,
        description = "client_key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[QuoteApiResponse]
            )
          )
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    )
  )
  def getQuote(@Parameter(hidden = true) company_key: String, @Parameter(hidden = true) page: Int, @Parameter(hidden = true) pageSize: Int,
               @Parameter(hidden = true) searchBar: Option[String], @Parameter(hidden = true) dateSearch: Option[String],
               @Parameter(hidden = true) store_key: Option[String], @Parameter(hidden = true) client_key: Option[String]): Action[AnyContent]

  @Path("/views/main-container/{company_key}/allQuote")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get quote by key",
    description = "Get quote, protected by JWT token user",
    tags = Array("Quote"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[QuoteApiResponse]
            )
          )
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    )
  )
  def getAllQuote(@Parameter(hidden = true) company_key: String): Action[AnyContent]

  @Path("/views/main-container/{company_key}/quote/{quote_key}")
  @PUT
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Update a quote",
    tags = Array("Quote"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "quote_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of quote",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = false,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[String])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      )
    )
  )
  def updateQuote(@Parameter(hidden = true) quote_key: String, @Parameter(hidden = true) company_key: String): Action[AnyContent]

  @Path("/views/main-container/{company_key}/quote/{quote_key}")
  @DELETE
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Delete a quote",
    description = "Delete quote",
    tags = Array("Quote"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "quote_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of quote",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[NewQuoteResponse]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      )
    )
  )
  def deleteQuote(@Parameter(hidden = true) quote_key: String, @Parameter(hidden = true) company_key: String): Action[AnyContent]

  @Path("/views/main-container/{company_key}/markAsFacture/{quote_key}")
  @PUT
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Update status a quote",
    tags = Array("Quote"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "quote_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of quote",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = false,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[String])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      ),
    ),
  )
  def markAsFactureQuote(@Parameter(hidden = true) quote_key: String, @Parameter(hidden = true) company_key: String): Action[AnyContent]

  @Path("/views/main-container/{company_key}/markAsReject/{quote_key}")
  @PUT
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Update status a quote",
    tags = Array("Quote"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "quote_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of quote",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = false,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[String])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      ),
    ),
  )
  def markAsRejectQuote(@Parameter(hidden = true) quote_key: String, @Parameter(hidden = true) company_key: String): Action[AnyContent]

  @Path("/views/main-container/{company_key}/markAsPending/{quote_key}")
  @PUT
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Update status a quote",
    tags = Array("Quote"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "quote_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of quote",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = false,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[String])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      ),
    ),
  )
  def markAsPendingQuote(@Parameter(hidden = true) quote_key: String, @Parameter(hidden = true) company_key: String): Action[AnyContent]
}
