package com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import play.api.mvc.{Action, AnyContent}

trait ViewDebtsReceivablesController {
  @Path("/views/main-container/{companyKey}/debts-receivable")
  @POST
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Create a new debt",
    tags = Array("Views"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[DebtReceivableForm])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[NewDebtReceivableApiResponse])
          )
        )
      ),
      new ApiResponse(
        responseCode = "500",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    ),
  )
  def newDebtReceivable(@Parameter(hidden = true) companyKey: String): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/debts-receivable")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get all debts to pay",
    description = "Get all debts to pay in company when the view render, protected by JWT token user",
    tags = Array("Views"),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "page",
        in = ParameterIn.QUERY,
        required = false,
        description = "Number of Page, position",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "pageSize",
        in = ParameterIn.QUERY,
        required = false,
        description = "Page size enables you to specify the max number of rows to be returned in response",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "supplierKey",
        in = ParameterIn.QUERY,
        required = false,
        description = "supplier key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "paymentStatus",
        in = ParameterIn.QUERY,
        required = false,
        description = "paymen status",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "startDate",
        in = ParameterIn.QUERY,
        required = false,
        description = "startDate",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "endDate",
        in = ParameterIn.QUERY,
        required = false,
        description = "End Date",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "orderBy",
        in = ParameterIn.QUERY,
        required = false,
        description = "order by xx, yy, zz",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "searchBar",
        in = ParameterIn.QUERY,
        required = false,
        description = "searchBar, name or rfc for any search",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[DebtsWithClientsResponce]
            )
          )
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    )
  )
  def getDebtsReceivable(@Parameter(hidden = true) companyKey: String, @Parameter(hidden = true) page: Int, 
                         @Parameter(hidden = true) pageSize: Int, @Parameter(hidden = true) clientKey: Option[String],
                         @Parameter(hidden = true) paymentStatus: Option[String], @Parameter(hidden = true) startDate: Option[String], 
                         @Parameter(hidden = true) endDate: Option[String], @Parameter(hidden = true) orderBy: Option[String],
                         @Parameter(hidden = true) searchBar: Option[String]): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/debts-receivable/{debtToPayKey}")
  @DELETE
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Delete a debt receivable",
    tags = Array("Views"),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "debtToPayKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of debt",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = false,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[String])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      ),
    ),
  )
  def deleteDebtReceivable(@Parameter(hidden = true) company_key: String, @Parameter(hidden = true) debt_key: String): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/debts-receivable/{debtToPayKey}")
  @PUT
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Delete a debt receivable",
    tags = Array("Views"),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "debtToPayKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of debt",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = false,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[String])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      ),
    ),
  )
  def markAsPaid(@Parameter(hidden = true) company_key: String, @Parameter(hidden = true) debt_key: String): Action[AnyContent]
}
