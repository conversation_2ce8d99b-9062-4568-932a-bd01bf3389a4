package com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

@Singleton
class ViewDebtsReceivablesControllerImpl @Inject() (viewsDebtReceivable: ViewDebtsReceivableActions, cc: ControllerComponents)
    extends AbstractController(cc)
    with ViewDebtsReceivablesController
    with BasePlayController {
  override def newDebtReceivable(companyKey: String): Action[AnyContent] = Action { implicit request =>
    val debtReceivableForm: DebtReceivableForm = bindFromRequest[DebtReceivableForm]().get

    val response = viewsDebtReceivable.newDebtReceivable(
      debtReceivableForm
        .into[DebtReceivableActionRequest]
        .withFieldConst(_.company_key, companyKey)
        .withFieldConst(_.client_key, debtReceivableForm.key)
        .withFieldComputed(_.paymentStatus, newDebtReceivableAction => PaymentStatus.withName(newDebtReceivableAction.debt_paid_status))
        .transform
    )
    Ok(response.toJsValue())
  }

  override def getDebtsReceivable(
      companyKey: String,
      page: Int,
      pageSize: Int,
      clientKey: Option[String],
      paymentStatus: Option[String],
      startDate: Option[String],
      endDate: Option[String],
      orderBy: Option[String],
      searchBar: Option[String]
  ): Action[AnyContent] = Action { implicit request =>
    val filters = FiltersActionRequest(
      companyKey,
      page,
      pageSize,
      clientKey,
      paymentStatus.map { status =>
        PaymentStatus.withNameOption(status).get
      },
      startDate,
      endDate,
      orderBy,
      searchBar
    )
    val response: DebtsReceivableWithTotalApiResponse = viewsDebtReceivable.getDebtsReceivable(filters).into[DebtsReceivableWithTotalApiResponse].transform
    Ok(response.toJsValue())
  }

  override def deleteDebtReceivable(company_key: String, debt_key: String): Action[AnyContent] = Action { implicit request =>
    viewsDebtReceivable.deleteDebtReceivable(debt_key) match {
      case Left(value) =>
        value match {
          case e: DebtNotFoundException => NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  override def markAsPaid(company_key: String, debt_key: String): Action[AnyContent] = Action { implicit request =>
    viewsDebtReceivable.markAsPaid(debt_key) match {
      case Left(value) =>
        value match {
          case e: DebtNotFoundException => NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }
}
