package com.qrsof.empresalit.views.maincontainer.suppliers.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.AddressDataActionRequest
import com.qrsof.empresalit.views.maincontainer.suppliers.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

@Singleton
class ViewSuppliersControllerImpl @Inject() (viewSuppliersActions: ViewSuppliersActions, cc: ControllerComponents) extends AbstractController(cc) with ViewSuppliersController with BasePlayController {

  override def getSuppliers(companyKey: String, page: Int, pageSize: Int, supplierKey: Option[String], searchBar: Option[String]): Action[AnyContent] = Action { implicit request =>
    val filters = FiltersActionRequest(
      companyKey,
      page,
      pageSize,
      supplierKey,
      searchBar
    )

    val response = viewSuppliersActions.getSuppliers(filters)

    val suppApiResponse: Seq[SupplierApiResponse] = response.suppliers map { supp =>
      {
        supp.into[SupplierApiResponse].transform
      }
    }

    val suppliers: SuppliersWithFiltersApiResponse = response
      .into[SuppliersWithFiltersApiResponse]
      .withFieldConst(_.suppliers, suppApiResponse)
      .transform

    Ok(suppliers.toJsValue())
  }

  override def getAllSuppliers(company_key: String): Action[AnyContent] = Action { implicit request =>
    val response = viewSuppliersActions.getAllSuppliers(Option(company_key))

    Ok(response.toJsValue())
  }

  def addNewSupplier(companyKey: String): Action[AnyContent] = Action { implicit request =>
    val newSupplierFormData: NewSupplierForm = bindFromRequest[NewSupplierForm]().get

    viewSuppliersActions.addNewSupplier(
      newSupplierFormData
        .into[NewSupplierActionRequest]
        .withFieldConst(_.generalData, newSupplierFormData.generalData.into[SupplierGeneralDataActionRequest].transform)
        .withFieldConst(_.addressData, newSupplierFormData.addressData.into[AddressDataActionRequest].transform)
        .withFieldConst(_.companyKey, companyKey)
        .transform
    ) match {
      case Left(newSupplierException) =>
        newSupplierException match {
          case e: UnknowErrorView => InternalServerError(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(newSupplier) =>
        val newSupp = NewSupplierResponseData(newSupplier.supplier_key)
        Ok(newSupp.toJsValue())
    }
  }
}
