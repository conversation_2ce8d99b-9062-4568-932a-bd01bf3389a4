package com.qrsof.empresalit.views.maincontainer.suppliers.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.empresalit.views.maincontainer.suppliers.actions.Supplier
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import play.api.mvc.{Action, AnyContent}

trait ViewSuppliersController {
  @Path("/views/main-container/{companyKey}/suppliers")
  @GET
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get all suppliers",
    description = "Get all suppliers in company when the view render, protected by JWT token user",
    tags = Array("Suppliers"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[Supplier]
            )
          )
        )
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "page",
        in = ParameterIn.QUERY,
        required = false,
        description = "Number of Page, position",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "pageSize",
        in = ParameterIn.QUERY,
        required = false,
        description = "Page size enables you to specify the max number of rows to be returned in response",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "supplierKey",
        in = ParameterIn.QUERY,
        required = false,
        description = "supplier key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "searchBar",
        in = ParameterIn.QUERY,
        required = false,
        description = "searchBar, name or rfc for any search",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
    )
  )
  def getSuppliers(@Parameter(hidden = true) companyKey: String, @Parameter(hidden = true) page: Int, 
                   @Parameter(hidden = true) pageSize: Int, @Parameter(hidden = true) supplierKey: Option[String], 
                   @Parameter(hidden = true) searchBar: Option[String]): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/allSuppliers")
  @GET
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get all suppliers",
    description = "Get all suppliers in company when the view render, protected by JWT token user",
    tags = Array("Suppliers"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[Supplier]
            )
          )
        )
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    )
  )
  def getAllSuppliers(@Parameter(hidden = true) companyKey: String): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/suppliers")
  @POST
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Create a new supplier",
    tags = Array("Suppliers"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[NewSupplierForm])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[NewSupplierResponseData])
          )
        )
      ),
      new ApiResponse(
        responseCode = "500",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    ),
  )
  def addNewSupplier(@Parameter(hidden = true) companyKey: String): Action[AnyContent]

}
