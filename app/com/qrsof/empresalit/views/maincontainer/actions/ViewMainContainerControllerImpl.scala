package com.qrsof.empresalit.views.maincontainer.actions

import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import jakarta.inject.{Inject, Singleton}
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

@Singleton
class ViewMainContainerControllerImpl @Inject() (
    viewDashboardActions: ViewMainContainerActions,
    authenticatedAction: AuthenticatedAction,
    cc: ControllerComponents
) extends AbstractController(cc)
    with ViewMainContainerController
    with BasePlayController {

  override def getUserCompanies: Action[AnyContent] = authenticatedAction { implicit request =>
    val userKey = request.principal
    val companies: Seq[UserCompaniesView] = viewDashboardActions.getUserCompanies(userKey)
    Ok(companies.toJsValue())
  }
}
