package com.qrsof.empresalit.views.maincontainer.actions


import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.Operation
import jakarta.ws.rs.*
import play.api.mvc.{Action, AnyContent}

trait ViewMainContainerController {

  @Path("/views/main-layout/getCompanies")
  @GET
  @Operation(
    summary = "Get Companies",
    description = "Get all companies in user when the view render, protected by JWT token user",
    tags = Array("Views"),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[UserCompaniesView]
            )
          )
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    )
  )
  def getUserCompanies: Action[AnyContent]

}
