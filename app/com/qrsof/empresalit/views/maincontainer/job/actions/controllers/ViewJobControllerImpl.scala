package com.qrsof.empresalit.views.maincontainer.job.actions.controllers


import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.views.maincontainer.actions.{UserCompaniesView, ViewMainContainerActions}
import com.qrsof.empresalit.views.maincontainer.jobs.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

@Singleton
class ViewJobControllerImpl @Inject()(viewJobsActions: ViewJobsActions,
                                      viewDashboardActions: ViewMainContainerActions,
                                      authenticatedAction: AuthenticatedAction,
                                      cc: ControllerComponents)
  extends AbstractController(cc) with ViewJobController with BasePlayController {
  override def addJob(company_key: String): Action[AnyContent] = authenticatedAction { implicit request =>
    val jobsDataForm: JobsDataForm = bindFromRequest[JobsDataForm]().get
    val userKey = request.principal
    val companies: Seq[UserCompaniesView] = viewDashboardActions.getUserCompanies(userKey)
    viewJobsActions.addJobs(
      jobsDataForm
        .into[NewJobsAction]
        .withFieldConst(_.userKey, userKey)
        .withFieldConst(_.company_key, companies.head.key)
        .transform
    ) match {
      case Left(newJobException) => newJobException match {
        case e: UnknowErrorView => InternalServerError(ApiErrorResponse(Seq(e.appError)).toJsValue())
      }
      case Right(job) =>
        val newJobResponse = JobResponse(job.job_key)
        Ok(newJobResponse.toJsValue())
    }
  }

  override def getJobs(company_key: String): Action[AnyContent] = Action { implicit request =>
    val jobs: JobsWithTotal = viewJobsActions.getJobs(company_key).into[JobsWithTotal].transform
    Ok(jobs.toJsValue())
  }

  override def updateJob(company_key: String, job_key: String): Action[AnyContent] = Action { implicit request =>
    viewJobsActions.updateJobs(job_key) match {
      case Left(value) => value match {
        case e: JobsNotFountException => NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
      }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  override def deleteJob(company_key: String, job_key: String): Action[AnyContent] = Action { implicit request =>
    viewJobsActions.deleteJobs(job_key) match {
      case Left(value) => value match {
        case e: JobsNotFountException => NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
      }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }
}
