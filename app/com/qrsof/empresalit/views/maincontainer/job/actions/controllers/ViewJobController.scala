package com.qrsof.empresalit.views.maincontainer.job.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.empresalit.views.maincontainer.jobs.actions.{JobResponse, Jobs}
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import play.api.mvc.{Action, AnyContent}

trait ViewJobController {

  @Path("/views/main-container/{company_key}/jobs")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get job by key",
    description = "Get job, protected by JWT token user",
    tags = Array("Job"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[Jobs]
            )
          )
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    )
  )
  def getJobs(@Parameter(hidden = true) company_key: String): Action[AnyContent]

  @Path("/views/main-container/{company_key}/jobs")
  @POST
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Created a new job",
    tags = Array("Job"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[JobsDataForm])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[JobResponse])
          )
        )
      ),
      new ApiResponse(
        responseCode = "500",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    )
  )
  def addJob(@Parameter(hidden = true) company_key: String): Action[AnyContent]

  @Path("/views/main-container/{company_key}/jobs/{job_key}")
  @DELETE
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Delete a job data",
    description = "Delete a job",
    tags = Array("Job"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "job_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of job",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[JobsApiResponse]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      ),
    )
  )
  def deleteJob(@Parameter(hidden = true) company_key: String, @Parameter(hidden = true) job_key: String): Action[AnyContent]

  @Path("/views/main-container/{company_key}/jobs/{job_key}")
  @PUT
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Update a job by key",
    tags = Array("Job"),
    parameters = Array(
      new Parameter(
        name = "company_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "job_key",
        in = ParameterIn.PATH,
        required = true,
        description = "key of job",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = false,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[String])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ApiErrorResponse]
            )
          )
        )
      )
    )
  )
  def updateJob(@Parameter(hidden = true) company_key: String, @Parameter(hidden = true) job_key: String): Action[AnyContent]
}
