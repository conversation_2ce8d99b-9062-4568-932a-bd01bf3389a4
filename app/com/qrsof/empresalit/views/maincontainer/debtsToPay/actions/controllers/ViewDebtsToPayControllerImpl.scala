package com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.apache.pekko.http.scaladsl.common.StrictForm.FileData
import org.slf4j.LoggerFactory
import play.api.libs.json.Json
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

import java.io.File
import scala.io.Source

@Singleton
class ViewDebtsToPayControllerImpl @Inject() (viewDebtsToPayActions: ViewDebtToPayActions, cc: ControllerComponents, authenticatedAction: AuthenticatedAction)
    extends AbstractController(cc)
    with ViewDebtsToPayController
    with BasePlayController {
  private val log = LoggerFactory.getLogger(classOf[ViewDebtsToPayControllerImpl])

  override def addDebtToPay(company_key: String): Action[AnyContent] = authenticatedAction { implicit request =>
    val newFacturaData: DebtToPayForm = bindFromRequest[DebtToPayForm]().get
    val userKey = request.principal

    viewDebtsToPayActions.addDebtToPay(
      newFacturaData
        .into[NewDebtToPayAction]
        .withFieldConst(_.userKey, userKey)
        //			 .withFieldConst(_.file_pdf, fileDataToString(newFacturaData.file_pdf))
        .withFieldComputed(_.debt_paid_status, newFacturaAction => DebtToPayStatus.withName(newFacturaAction.debt_paid_status))
        .transform
    ) match {
      case Left(newFacturaException) =>
        log.error(newFacturaException.appError.error.getOrElse("InternalServerError"))
        log.error(newFacturaException.appError.detail.getOrElse("InternalServerError"))
        newFacturaException match {
          case e: UnknowErrorViewA => InternalServerError(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(debtToPay) =>
        val newDebtToPayResponse = NewDebtToPayApiResponse(debtToPay.key)
        Ok(newDebtToPayResponse.toJsValue())
    }
  }

  override def getDebtsToPay(
      companyKey: String,
      page: Int,
      pageSize: Int,
      supplierKey: Option[String],
      paymentStatus: Option[String],
      startDate: Option[String],
      endDate: Option[String],
      orderBy: Option[String],
      searchBar: Option[String]
  ): Action[AnyContent] = Action { implicit request =>
    val filters = ListDebtToPayActionRequest(
      companyKey,
      page,
      pageSize,
      supplierKey,
      paymentStatus.map { pystatus =>
        DebtToPayStatus.withNameOption(pystatus).get
      },
      startDate,
      endDate,
      orderBy,
      searchBar
    )
    val debtsToPay: DebtsToPayApiResponse = viewDebtsToPayActions.getDebtsToPay(filters).into[DebtsToPayApiResponse].transform
    Ok(debtsToPay.toJsValue())
  }

  override def deleteDebtToPay(companyKey: String, debtToPayKey: String): Action[AnyContent] = Action { implicit request =>
    viewDebtsToPayActions.deleteDebtToPay(debtToPayKey) match {
      case Left(value) =>
        value match {
          case e: DebtToPayNotFoundExeption => NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  override def markAsPaidDebtToPay(companyKey: String, debtToPayKey: String): Action[AnyContent] = Action { implicit request =>
    viewDebtsToPayActions.markAsPaid(debtToPayKey) match {
      case Left(value) =>
        value match {
          case e: DebtToPayNotFoundExeption => NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  override def pruebaruta: Action[AnyContent] = Action { implicit request =>
    print("apoco si pa")
    Ok(Json.obj("status" -> "OK", "message" -> "apoco si pa"))
  }

  def fileDataToString(fileData: FileData): String = {
    val source = Source.fromFile(new File("Archivo_prueba"))
    try {
      source.mkString
    } finally {
      source.close()
    }
  }
}
