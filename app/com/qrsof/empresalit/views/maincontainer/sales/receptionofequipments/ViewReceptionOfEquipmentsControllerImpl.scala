package com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments

import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.pojos.*
import jakarta.inject.{Inject, Singleton}
import org.apache.pekko.http.scaladsl.common.StrictForm
import org.apache.pekko.http.scaladsl.model.{ContentTypes, HttpEntity}
import org.apache.pekko.util.ByteString
import org.slf4j.{Logger, LoggerFactory}
import play.api.libs.Files.TemporaryFile
import play.api.mvc.*

@Singleton
class ViewReceptionOfEquipmentsControllerImpl @Inject() (viewReceptionOfEquipmentActions: ViewReceptionOfEquipmentActions, authenticatedAction: AuthenticatedAction, cc: ControllerComponents)
    extends Abstract<PERSON>ontroller(cc)
    with ViewReceptionOfEquipmentsController
    with BasePlayController {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewReceptionOfEquipmentsControllerImpl])

  override def getClientsOfCompany(companyKey: String): Action[AnyContent] = authenticatedAction { implicit request =>
    val clients: Seq[ClientByReceptionOfEquipment] = viewReceptionOfEquipmentActions.getClientsByCompany(companyKey)
    Ok(clients.toJsValue())
  }

  override def getCarriersOfCompany(companyKey: String): Action[AnyContent] = authenticatedAction { implicit request =>
    val clients: Seq[ClientByReceptionOfEquipment] = Seq.empty
    Ok(clients.toJsValue())
  }

  override def newReceptionOfEquipments(companyKey: String): Action[MultipartFormData[TemporaryFile]] = authenticatedAction(parse.multipartFormData) { implicit request =>
    val userKey = request.principal
    val bodyRequest = request.body

    val requestData = bodyRequest.dataParts.get("requestData").flatMap(_.headOption)

    if (requestData == null) {
      throw new Exception()
    }
    val dataReceptionOfEquipmentsForm: Option[ReceptionOfEquipmentsForm] = requestData.map(body => {
      body.toModel[ReceptionOfEquipmentsForm]()
    })

    val data = dataReceptionOfEquipmentsForm.get

    val equipments: Seq[EquipmentRequest] = data.equipments.map(equipment => EquipmentRequest(equipment.name, equipment.quantity, equipment.observations, Seq.empty));
    val equipmentsWithEvidences: Seq[EquipmentRequest] = equipments.zipWithIndex.map { (equipment, index) =>
      val evidences = request.body.files.filter(_.key == "evidences-".concat(index.toString)).map { filePart =>
        logger.info("filePart::filePart: {}", filePart)
        val fileBytes = java.nio.file.Files.readAllBytes(filePart.ref.path)
        StrictForm.FileData(Some(filePart.filename), HttpEntity.Strict(ContentTypes.`application/octet-stream`, ByteString(fileBytes)))
      }
      equipment.copy(resources = evidences)
    }
    val create = viewReceptionOfEquipmentActions.newReceptionOfEquipments(companyKey, ReceptionOfEquipmentRequest(data.client, data.carrier, userKey, equipmentsWithEvidences, data.signature))
    Ok(create.serializeToJson())
  }

  override def getReceptionOfEquipments(companyKey: String, pageNumber: Int, pageSize: Int): Action[AnyContent] = authenticatedAction { implicit request =>
    val text: Option[String] = request.getQueryString("text")
    val result = viewReceptionOfEquipmentActions.getReceptionOfEquipment(companyKey, ReceptionOfEquipmentFilter(text, None, None, None), pageNumber, pageSize)
    Ok(result.toJsValue())
  }
}
