package com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.pojos.{ClientByReceptionOfEquipment, ReceptionOfEquipmentsForm}
import jakarta.ws.rs.*
import jakarta.ws.rs.Path
import jakarta.ws.rs.core.MediaType
import play.api.libs.Files.TemporaryFile
import play.api.mvc.{Action, AnyContent, MultipartFormData}

trait ViewReceptionOfEquipmentsController {

  @Path("/views/main-layout/{companyKey}/sales/reception-of-equipments/clients")
  @GET
  @Operation(
    summary = "Get clients",
    description = "Get clients of company by reception of equipments",
    tags = Array("Reception-Equipment"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ClientByReceptionOfEquipment]
            )
          )
        )
      )
    )
  )
  def getClientsOfCompany(companyKey: String): Action[AnyContent]

  @Path("/views/main-layout/{companyKey}/sales/reception-of-equipments/carriers")
  @GET
  @Operation(
    summary = "Get carriers",
    description = "Get Carriers of company by reception of equipments",
    tags = Array("Reception-Equipment"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[ClientByReceptionOfEquipment]
            )
          )
        )
      )
    )
  )
  def getCarriersOfCompany(companyKey: String): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/sales/reception-of-equipments/new")
  @Consumes(Array(MediaType.MULTIPART_FORM_DATA, MediaType.APPLICATION_JSON))
  @POST
  @Operation(
    summary = "New Reception of equipments",
    description = "New Reception of equipments",
    tags = Array("Reception-Equipment-New"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[ReceptionOfEquipmentsForm])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      )
    )
  )
  def newReceptionOfEquipments(companyKey: String): Action[MultipartFormData[TemporaryFile]]

  @Path("/views/main-layout/{companyKey}/sales/reception-of-equipments")
  @Consumes(Array(MediaType.MULTIPART_FORM_DATA, MediaType.APPLICATION_JSON))
  @GET
  @Operation(
    summary = "New Reception of equipments",
    description = "New Reception of equipments",
    tags = Array("Reception-Equipment-New"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[ReceptionOfEquipmentsForm])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      )
    )
  )
  def getReceptionOfEquipments(companyKey: String, pageNumber: Int, pageSize: Int): Action[AnyContent]

}
