package com.qrsof.empresalit.views.maincontainer.employee.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.views.maincontainer.actions.{UserCompaniesView, ViewMainContainerActions}
import com.qrsof.empresalit.views.maincontainer.employee.actions.*
import com.qrsof.empresalit.views.maincontainer.employee.actions.pojos.EmployeeDataUpdateRequest
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.apache.pekko.http.scaladsl.common.StrictForm
import org.apache.pekko.http.scaladsl.model.{ContentTypes, HttpEntity}
import org.apache.pekko.util.ByteString
import org.slf4j.{Logger, LoggerFactory}
import play.api.libs.Files.TemporaryFile
import play.api.mvc.*
import play.api.mvc.MultipartFormData.FilePart

@Singleton
class ViewEmployeeControllerImpl @Inject() (
    viewEmployeeActions: ViewEmployeeActions,
    viewDashboardActions: ViewMainContainerActions,
    authenticatedAction: AuthenticatedAction,
    cc: ControllerComponents
) extends AbstractController(cc)
    with ViewEmployeeController
    with BasePlayController {

  val logger: Logger = LoggerFactory.getLogger(classOf[ViewEmployeeControllerImpl])
  val text = "Example"

  override def addEmployee(companyKey: String): Action[MultipartFormData[TemporaryFile]] = authenticatedAction(parse.multipartFormData) { implicit request =>

    val userKey = request.principal
    val bodyRequest = request.body
    val requestData = bodyRequest.dataParts.get("employeeRequest").flatMap(_.headOption)

    val newEmployeeData: EmployeeDataForm = requestData
      .map(newEmployeeData => {
        newEmployeeData.toModel[EmployeeDataForm]()
      })
      .get

    logger.info(s"Request Header -> {}", request.principal)

    val files = getFilesOfRequest(request.body.files.filter(_.key == "employeeFiles"))

    val companies: Seq[UserCompaniesView] = viewDashboardActions.getUserCompanies(userKey)

    var employeeRequest = newEmployeeData
      .into[NewEmployeeAction]
      .withFieldConst(_.userKey, userKey)
      .withFieldConst(_.companyKey, companies.head.key)
      .withFieldConst(_.files, Seq.empty)
      .transform

    employeeRequest = employeeRequest.copy(files = files)

    viewEmployeeActions.addEmployee(
      employeeRequest
    ) match {
      case Left(newEmployeeException) =>
        newEmployeeException match {
          case e: UnknowErrorViewA => Conflict(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(employee) =>
        val newEmployeeResponse = NewEmployeeResponse(employee.key)
        Ok(newEmployeeResponse.toJsValue())
    }
  }

  override def getEmployee(companyKey: String, job_key: Option[String], page: Int, pageSize: Int, searchBar: Option[String]): Action[AnyContent] = Action { implicit request =>
    val filters = FiltersEmployeeActionRequest(
      companyKey,
      page,
      pageSize,
      searchBar
    )
    val response: Employees = viewEmployeeActions
      .getEmployee(filters)
      .into[Employees]
      .transform
    Ok(response.toJsValue())
  }

  override def deleteEmployee(company_key: String, employee_key: String): Action[AnyContent] = Action { implicit request =>
    viewEmployeeActions.deleteEmployee(employee_key) match {
      case Left(value) =>
        value match {
          case e: EmployeeNotFoundException => NotFound(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  override def updateEmployee(company_key: String, employeeKey: String): Action[MultipartFormData[TemporaryFile]] = authenticatedAction(parse.multipartFormData) { implicit request =>
    request.principal
    val bodyRequest = request.body
    val requestData = bodyRequest.dataParts.get("employeeRequest").flatMap(_.headOption)
    val employeeDataUpdateForm: EmployeeDataUpdateForm = requestData
      .map(employeeUpdate => {
        employeeUpdate.toModel[EmployeeDataUpdateForm]()
      })
      .get

    val files = getFilesOfRequest(request.body.files.filter(_.key == "employeeFiles"))

    val response = viewEmployeeActions.updateEmployee(
      employeeDataUpdateForm
        .into[EmployeeDataUpdateRequest]
        .withFieldConst(_.employeeKey, employeeKey)
        .withFieldConst(_.files, files)
        .transform
    )
    response match {
      case Left(errorP) =>
        errorP match {
          case e: EmployeeNotFoundException => InternalServerError(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }

  def getFilesOfRequest(files: Seq[FilePart[TemporaryFile]]): Seq[StrictForm.FileData] = {
    files.map { filePart =>
      val fileBytes = java.nio.file.Files.readAllBytes(filePart.ref.path)
      StrictForm.FileData(Some(filePart.filename), HttpEntity.Strict(ContentTypes.`application/octet-stream`, ByteString(fileBytes)))
    }
  }

  override def getEmployeeResources(companyKey: String, employeeKey: String): Action[AnyContent] = authenticatedAction { implicit request =>
    val resources = viewEmployeeActions.getEmployeeResources(companyKey, employeeKey)
    Ok(resources.toJsValue())
  }

  override def getEmployeeData(companyKey: String, employeeKey: String): Action[AnyContent] = authenticatedAction { implicit request =>
    val employeeData = viewEmployeeActions.getEmployeeData(companyKey, employeeKey)
    Ok(employeeData.toJsValue())
  }
}
