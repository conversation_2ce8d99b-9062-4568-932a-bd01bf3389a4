package com.qrsof.empresalit.views.maincontainer.employee.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.empresalit.domain.employee_resources.pojos.EmployeeResourceData
import com.qrsof.empresalit.views.maincontainer.employee.actions.pojos.EmployeeData
import com.qrsof.empresalit.views.maincontainer.employee.actions.{Employee, NewEmployeeResponse}
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import play.api.libs.Files.TemporaryFile
import play.api.mvc.{Action, AnyContent, MultipartFormData}

trait ViewEmployeeController {
	@Path("/views/main-container/{companyKey}/employee")
	@POST
	@Consumes(Array(MediaType.MULTIPART_FORM_DATA))
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Created a new employee",
		tags = Array("Employee"),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		requestBody = new RequestBody(
			required = true,
			content = Array(
				new Content(
					schema = new Schema(implementation = classOf[EmployeeDataForm])
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(
						schema = new Schema(implementation = classOf[NewEmployeeResponse])
					)
				)
			),
			new ApiResponse(
				responseCode = "500",
				content = Array(
					new Content(
						schema = new Schema(implementation = classOf[ApiErrorResponse])
					)
				)
			)
		)
	)
	def addEmployee(@Parameter(hidden = true) companyKey: String): Action[MultipartFormData[TemporaryFile]]

	@Path("/views/main-container/{company_key}/employee")
	@GET
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Get employee by key",
		description = "Get employee, protected by JWT token user",
		tags = Array("Employee"),
		parameters = Array(
			new Parameter(
				name = "company_key",
				in = ParameterIn.PATH,
				required = true,
				description = "company key",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "page",
				in = ParameterIn.QUERY,
				required = false,
				description = "Number page",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "pageSize",
				in = ParameterIn.QUERY,
				required = false,
				description = "Page size enables you to specify the max number of rows to be returned in response",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "searchBar",
				in = ParameterIn.QUERY,
				required = false,
				description = "searchBar, name or rfc for any search",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "job_key",
				in = ParameterIn.QUERY,
				required = false,
				description = "job_key",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[Employee]
						)
					)
				)
			)
		),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		)
	)
	def getEmployee(@Parameter(hidden = true) company_key: String, @Parameter(hidden = true) job_key: Option[String],
									@Parameter(hidden = true) page: Int, @Parameter(hidden = true) pageSize: Int,
									@Parameter(hidden = true) searchBar: Option[String]): Action[AnyContent]

	@Path("/views/main-container/{companyKey}/employee/{employee_key}")
	@DELETE
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Delete a employee",
		description = "Delete a employee",
		tags = Array("Employee"),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "employee_key",
				in = ParameterIn.PATH,
				required = true,
				description = "key of employee",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[NewEmployeeResponse]
						)
					)
				)
			),
			new ApiResponse(
				responseCode = "404",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[ApiErrorResponse]
						)
					)
				)
			),
		),
	)
	def deleteEmployee(@Parameter(hidden = true) company_key: String, @Parameter(hidden = true) employee_key: String): Action[AnyContent]

	@Path("/views/main-container/{companyKey}/employee/{employeeKey}")
	@PUT
	@Consumes(Array(MediaType.MULTIPART_FORM_DATA))
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Update a employee",
		tags = Array("Employee"),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "employeeKey",
				in = ParameterIn.PATH,
				required = true,
				description = "key of employee key",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		requestBody = new RequestBody(
			required = false,
			content = Array(
				new Content(
					schema = new Schema(implementation = classOf[EmployeeDataUpdateForm])
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[String]
						)
					)
				)
			),
			new ApiResponse(
				responseCode = "404",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[ApiErrorResponse]
						)
					)
				)
			)
		)
	)
	def updateEmployee(@Parameter(hidden = true) companyKey: String, @Parameter(hidden = true) employeeKey: String): Action[MultipartFormData[TemporaryFile]]

	@Path("/views/main-container/{companyKey}/employee/{employeeKey}/resources")
	@PUT
	@Consumes(Array(MediaType.APPLICATION_JSON))
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "get employee resources",
		tags = Array("Employee"),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "employeeKey",
				in = ParameterIn.PATH,
				required = true,
				description = "key of employee key",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[Seq[EmployeeResourceData]]
						)
					)
				)
			),
			new ApiResponse(
				responseCode = "404",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[ApiErrorResponse]
						)
					)
				)
			)
		)
	)
	def getEmployeeResources(@Parameter(hidden = true) companyKey: String, @Parameter(hidden = true) employeeKey: String): Action[AnyContent]

	@Path("/views/main-container/{companyKey}/employee/{employeeKey}/data")
	@PUT
	@Consumes(Array(MediaType.APPLICATION_JSON))
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "get employee data",
		tags = Array("Employee"),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "employeeKey",
				in = ParameterIn.PATH,
				required = true,
				description = "key of employee key",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[EmployeeData]
						)
					)
				)
			),
			new ApiResponse(
				responseCode = "404",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[ApiErrorResponse]
						)
					)
				)
			)
		)
	)
	def getEmployeeData(@Parameter(hidden = true) companyKey: String, @Parameter(hidden = true) employeeKey: String): Action[AnyContent]
}
