package com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.forms

import com.qrsof.empresalit.views.maincontainer.presenteeism.pojos.UpdateEmployeePresenteeismRequest
import play.api.data.Form
import play.api.data.Forms.*

import java.util.Date

object UpdatePresenteeismForm {

  private object UpdatePresenteeismData {
    def unapply(data: UpdateEmployeePresenteeismRequest): Option[(String, String, Int, Date, Date)] = Some((data.presenteeismKey, data.presenteeismType, data.days, data.startDate, data.endDate))
  }

  val updatePresenteeismForm: Form[UpdateEmployeePresenteeismRequest] = Form(
    mapping(
      "presenteeismKey" -> text,
      "presenteeismType" -> text,
      "days" -> number,
      "startDate" -> date,
      "endDate" -> date
    )(UpdateEmployeePresenteeismRequest.apply)(UpdatePresenteeismData.unapply)
  )

}
