package com.qrsof.empresalit.views.maincontainer.clients.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import play.api.mvc.{Action, AnyContent}

trait ViewsClientsController {
  @Path("/views/main-container/{companyKey}/clients")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get all clients with pagination",
    description = "Get all clients in company when the view render, protected by JWT token user",
    tags = Array("Clients"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "Key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "Page",
        in = ParameterIn.QUERY,
        required = false,
        description = "Page 0 - *",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[Int]))
        )
      ),
      new Parameter(
        name = "Page size",
        in = ParameterIn.QUERY,
        required = false,
        description = "Page size enables you to specify the max number of rows to be returned in response",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[Int]))
        )
      ),
      new Parameter(
        name = "clientKey",
        in = ParameterIn.QUERY,
        required = false,
        description = "client",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "searchBar",
        in = ParameterIn.QUERY,
        required = false,
        description = "name / rfc",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      )
    )
  )
  def getClients(@Parameter(hidden = true) companyKey: String, @Parameter(hidden = true) page: Int, @Parameter(hidden = true) pageSize: Int, 
                 @Parameter(hidden = true) clientKey: Option[String], @Parameter(hidden = true) searchBar: Option[String]): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/allClients")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get all clients",
    description = "Get all clients in company when the view render, protected by JWT token user",
    tags = Array("Clients"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "key of company",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[String]
            )
          )
        )
      )
    )
  )
  def getClientsAll(@Parameter(hidden = true) companyKey: String): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/clients")
  @POST
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Create a new client",
    tags = Array("Clients"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[ClientForm])
        )
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[NewClientApiResponse])
          )
        )
      ),
      new ApiResponse(
        responseCode = "500",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    )
  )
  def newClient(@Parameter(hidden = true) companyKey: String): Action[AnyContent]

  @Path("/views/main-container/{companyKey}/clients/{clientKey}")
  @DELETE
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Delete a client",
    tags = Array("Clients"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    parameters = Array(
      new Parameter(
        name = "companyKey",
        in = ParameterIn.PATH,
        required = true,
        description = "company key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
      new Parameter(
        name = "clientKey",
        in = ParameterIn.PATH,
        required = true,
        description = "client key",
        content = Array(
          new Content(schema = new Schema(implementation = classOf[String]))
        )
      ),
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[NewClientApiResponse])
          )
        )
      ),
      new ApiResponse(
        responseCode = "500",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    )
  )
  def deleteClient(@Parameter(hidden = true) companyKey: String, @Parameter(hidden = true) clientKey: String): Action[AnyContent]

}
