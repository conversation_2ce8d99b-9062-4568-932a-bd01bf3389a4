package com.qrsof.empresalit.views.maincontainer.clients.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.domain.addressess.actions.AddressDataActionRequest
import com.qrsof.empresalit.views.maincontainer.clients.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

@Singleton
class ViewsClientsControllerImpl @Inject() (viewClientsActions: ViewClientsActions, cc: ControllerComponents, authenticatedAction: AuthenticatedAction)
    extends AbstractController(cc)
    with ViewsClientsController
    with BasePlayController {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewsClientsControllerImpl])

  override def getClients(companyKey: String, page: Int, pageSize: Int, clientKey: Option[String], searchBar: Option[String]): Action[AnyContent] = Action { implicit request =>
    val filters = FiltersClientsActionRequest(companyKey, page, pageSize, clientKey, searchBar)

    val response = viewClientsActions
      .getClients(filters)
      .into[ClientsApiResponse]
      .withFieldComputed(_.clients, _.clients.map(_.into[ClientApiResponse].transform))
      .transform

    Ok(response.toJsValue())
  }

  override def getClientsAll(company_key: String): Action[AnyContent] = Action { implicit request =>
    val response = viewClientsActions.getClientsAll(Option(company_key))
    Ok(response.toJsValue())
  }

  override def newClient(companyKey: String): Action[AnyContent] = authenticatedAction { implicit request =>
    val clientForm = bindFromRequest[ClientForm]().get
  viewClientsActions.newClient(
    clientForm
      .into[ClientActionRequest]
      .withFieldComputed(_.clientData, client => client.clientData.into[ClientDataActionRequest].transform)
      .withFieldConst(_.addressData, clientForm.addressData.into[AddressDataActionRequest].transform)
      .withFieldConst(_.company_key, companyKey)
      .transform
  ) match {
    case Left(value) =>
      value match {
        case e: ClientAlreadyExist => Conflict(ApiErrorResponse(Seq(e.appError)).toJsValue())
      }
    case Right(value) =>
      Ok(value.toJsValue())
  }
  }

  override def deleteClient(companyKey: String, clientKey: String): Action[AnyContent] = Action { implicit request =>
    viewClientsActions.deleteClient(clientKey) match {
      case Left(value) =>
        value match {
          case e: ClientAlreadyExist => Conflict(ApiErrorResponse(Seq(e.appError)).toJsValue())
        }
      case Right(value) =>
        Ok(value.toJsValue())
    }
  }
}
