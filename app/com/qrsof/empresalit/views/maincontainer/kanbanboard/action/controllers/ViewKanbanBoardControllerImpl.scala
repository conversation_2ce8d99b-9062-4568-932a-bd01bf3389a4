package com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.controllers.oauth.OauthActions
import com.qrsof.empresalit.domain.status.StatusService
import com.qrsof.empresalit.domain.status.pojos.StatusOrderRequest
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.*
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.{ViewKanbanBoardActions, ViewTaskConfirmationActions}
import com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.pojos.{FormTask, TaskForConfirmationForm}
import io.scalaland.chimney.dsl.*
import jakarta.inject.Inject
import org.apache.pekko.http.scaladsl.common.StrictForm
import org.apache.pekko.http.scaladsl.model.{ContentTypes, HttpEntity}
import org.apache.pekko.util.ByteString
import org.slf4j.{Logger, LoggerFactory}
import play.api.libs.Files
import play.api.libs.Files.TemporaryFile
import play.api.mvc.*

class ViewKanbanBoardControllerImpl @Inject() (
    statusService: StatusService,
    viewKanbanBoardActions: ViewKanbanBoardActions,
    viewTaskConfirmationActions: ViewTaskConfirmationActions,
    cc: ControllerComponents,
    authentication: AuthenticatedAction,
    oauthActions: OauthActions
) extends AbstractController(cc)
    with ViewKanbanBoardController
    with BasePlayController {

  val logger: Logger = LoggerFactory.getLogger(classOf[ViewKanbanBoardControllerImpl])

  override def getAllTasksAndStatus(companyKey: String): Action[AnyContent] = authentication { implicit request =>

    val response = viewKanbanBoardActions.getKanbanTasksAndStatus(companyKey)
    Ok(response.toJsValue())

  }

  override def getTasksByFilter(companyKey: String, term: Option[String]): Action[AnyContent] = Action { implicit request =>
    val filters = FiltersKanbanActionRequest(companyKey = companyKey, term = term, None, None, None)
    val response = viewKanbanBoardActions.getKanbanFilterTasks(filters)
    Ok(response.toJsValue())
  }

  override def newLogbook(companyKey: String): Action[MultipartFormData[TemporaryFile]] = authentication(parse.multipartFormData) { implicit request =>
    val userKey = request.principal
    val bodyRequest = request.body
    val requestData = bodyRequest.dataParts.get("logbookRequestData").flatMap(_.headOption)
    if (requestData == null) {
      throw new Exception()
    }

    val dataLogbook: Option[NewLogbookForm] = requestData
      .map(body => {
        body.toModel[NewLogbookForm]()
      })
    val data: NewLogbookForm = dataLogbook.get

    val resources = request.body.files.map { filePart =>
      val fileBytes = java.nio.file.Files.readAllBytes(filePart.ref.path)
      StrictForm.FileData(Some(filePart.filename), HttpEntity.Strict(ContentTypes.`application/octet-stream`, ByteString(fileBytes)))
    }

    val logbookRequest = NewLogbookRequest(
      author = data.author,
      taskKey = data.taskKey,
      logbookType = data.logbookType,
      payload = data.payload,
      attachments = resources
    )

    viewKanbanBoardActions.newLogbook(companyKey = companyKey, newLogbookRequest = logbookRequest, userKey = userKey) match {
      case Left(newLogbookException)     => BadRequest(ApiErrorResponse(Seq(newLogbookException.appError)).toJsValue())
      case Right(createdLogbookResponse) => Created
    }
  }

  override def getTaskByConfirmation(companyKey: String, taskKey: String): Action[AnyContent] = authentication { implicit request =>
    val response = viewTaskConfirmationActions.getTaskByConfirmation(companyKey, taskKey)
    Ok(response.toJsValue())
  }

  override def taskConfirmation(companyKey: String, taskKey: String): Action[AnyContent] = authentication { implicit request =>
    val taskForConfirmationForm = bindFromRequest[TaskForConfirmationForm]().get
    viewTaskConfirmationActions.saveTaskConfirmation(companyKey, TaskForConfirmationRequest(taskKey, taskForConfirmationForm.statusKey, taskForConfirmationForm.observations))
    Ok(taskKey.serializeToJson())
  }

  override def changeStatusOrder(companyKey: String, statusKey: String): Action[AnyContent] = authentication { implicit request =>
    val currentIndex = (request.body.toJsValue() \ "json" \ "currentIndex").as[Int]
    statusService.changeStatusOrder(companyKey, StatusOrderRequest(statusKey, currentIndex))
    Ok
  }

  override def getLogbookAndResources(companyKey: String, taskKey: String): Action[AnyContent] = Action { implicit request =>
    val message = viewKanbanBoardActions.getLogbookAndAttachmentsAction(companyKey, taskKey)
    Ok(message.toJsValue())
  }

  override def assignTask(companyKey: String, taskKey: String): Action[AnyContent] = authentication { implicit request =>
    val userKey = request.principal
    val taskForm = bindFromRequest[FormTask]().get
    val response = viewKanbanBoardActions.assignTaskAction(companyKey = companyKey, updatedTask = taskForm.into[TaskActionRequest].transform, userKey = userKey)

    response match {
      case Left(error) =>
        error match
          case e: TaskExceptionError => BadRequest(ApiErrorResponse(Seq(error.appError)).toJsValue())
      case Right(()) =>
        Accepted

    }
  }

  override def getAllTasksAndStatusAssigned(companyKey: String): Action[AnyContent] = authentication { implicit request =>
    val userKey = request.principal
    val filters = FiltersKanbanActionRequest(
      companyKey = companyKey,
      folio = request.getQueryString("folio"),
      employee = request.getQueryString("employee"),
      userKey = Some(userKey),
      term = request.getQueryString("term")
    )
    val response = viewKanbanBoardActions.getKanbanTasksAndStatusByFiltersKanbanActionRequest(filters)
    Ok(response.toJsValue())
  }

}
