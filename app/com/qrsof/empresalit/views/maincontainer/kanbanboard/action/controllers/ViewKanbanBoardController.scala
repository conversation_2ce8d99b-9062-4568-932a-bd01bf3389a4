package com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.{KanbanTasksResponse, NewLogbookForm, TaskDetailForConfirmation}
import com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.pojos.NewLogbookApiResponse
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import play.api.libs.Files.TemporaryFile
import play.api.mvc.{Action, AnyContent, MultipartFormData}

trait ViewKanbanBoardController {
	@Path("/views/main-container/{companyKey}/kanbanboard")
	@GET
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Get tasks and status",
		description = "Get all tasks and status by company",
		tags = Array("Kanban Tasks & status"),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[KanbanTasksResponse]
						)
					)
				)
			)
		)
	)
	def getAllTasksAndStatus(@Parameter(hidden = true) companyKey: String): Action[AnyContent]

	@Path("/views/main-container/{companyKey}/kanbanboardfilter")
	@GET
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Get filtered tasks",
		description = "Get tasks by client or folio ",
		tags = Array("Kanban Tasks & status"),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "term",
				in = ParameterIn.QUERY,
				required = false,
				description = "Name / Folio",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new ApiResponse(
				responseCode = "404",
				description = "Not found"
			)
		)
	)
	def getTasksByFilter(@Parameter(hidden = true) companyKey: String, @Parameter(hidden = true) term: Option[String]): Action[AnyContent]

	@Path("/views/main-container/{companyKey}/new-logbook")
	@POST
	@Consumes(Array(MediaType.MULTIPART_FORM_DATA, MediaType.APPLICATION_JSON))
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "New register of logbook for a task",
		description = "New register in the logbook",
		tags = Array("KanbanBoard"),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		requestBody = new RequestBody(
			required = true,
			content = Array(
				new Content(
					schema = new Schema(implementation = classOf[NewLogbookForm])
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "201",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[NewLogbookApiResponse]
						)
					)
				)
			),
			new ApiResponse(
				responseCode = "400",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[ApiErrorResponse]
						)
					)
				)
			),
			new ApiResponse(
				responseCode = "500",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[String]
						)
					)
				)
			)
		)
	)
	def newLogbook(companyKey: String): Action[MultipartFormData[TemporaryFile]]

	@Path("/views/main-container/{companyKey}/kanbanboard/task/{taskKey}")
	@GET
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Get Task Detail With Resources",
		description = "Get Task Detail With Resources ",
		tags = Array("Task"),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "taskKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of task",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[TaskDetailForConfirmation]))
				)
			),
			new ApiResponse(
				responseCode = "404",
				description = "Not found"
			)
		)
	)
	def getTaskByConfirmation(companyKey: String, taskKey: String): Action[AnyContent]

	@Path("/views/main-container/{companyKey}/kanbanboard/task/{taskKey}/confirmation")
	@POST
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Get filtered tasks",
		description = "Get tasks by client or folio ",
		tags = Array("Kanban Tasks & status"),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "taskKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of task",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new ApiResponse(
				responseCode = "404",
				description = "Not found"
			)
		)
	)
	def taskConfirmation(companyKey: String, taskKey: String): Action[AnyContent]

	@Path("/views/main-container/{companyKey}/kanbanboard/tasks/{taskKey}/logbook")
	@GET
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Get all registers of logbook and attachments",
		description = "Get logbook and attachments",
		tags = Array("KanbanBoard"),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "taskKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of task",
				content = Array(
					new Content(
						schema = new Schema(implementation = classOf[String])
					)
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				description = "Get logbook and attachments by key task for one task",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new ApiResponse(
				responseCode = "404",
				description = "Not found",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		)
	)
	def getLogbookAndResources(companyKey: String, taskKey: String): Action[AnyContent]

	@Path("/views/main-container/{companyKey}/kanbanboard/status/{statusKey}/order")
	@PATCH
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Change status order",
		description = "Change status order ",
		tags = Array("Status"),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "statusKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of status",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new ApiResponse(
				responseCode = "404",
				description = "Not found"
			)
		)
	)
	def changeStatusOrder(companyKey: String, statusKey: String): Action[AnyContent]

	@Path("/views/main-container/{companyKey}/kanbanboard/tasks/{taskKey}")
	@PATCH
	@Consumes(Array(MediaType.APPLICATION_JSON))
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Assign task",
		description = "Assign task to employee, this also create a register in logbook of the task",
		tags = Array("Kanban Tasks & status"),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			),
			new Parameter(
				name = "taskKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of task",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		requestBody = new RequestBody(
			required = true,
			content = Array(
				new Content(
					schema = new Schema(implementation = classOf[String])
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = " 200",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[String]
						)
					)
				)
			),
			new ApiResponse(
				responseCode = "204",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[String]
						)
					)
				)
			),
			new ApiResponse(
				responseCode = "304",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[String]
						)
					)
				)
			),
			new ApiResponse(
				responseCode = "400",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[String]
						)
					)
				)
			)
		)
	)
	def assignTask(companyKey: String, taskKey: String): Action[AnyContent]


	@Path("/views/main-container/{companyKey}/kanbanboard/assigned")
	@GET
	@Produces(Array(MediaType.APPLICATION_JSON))
	@Operation(
		summary = "Get tasks and status",
		description = "Get all tasks and status by company assigned",
		tags = Array("Kanban Tasks & status"),
		security = Array(
			new SecurityRequirement(
				name = "Bearer"
			)
		),
		parameters = Array(
			new Parameter(
				name = "companyKey",
				in = ParameterIn.PATH,
				required = true,
				description = "Key of company",
				content = Array(
					new Content(schema = new Schema(implementation = classOf[String]))
				)
			)
		),
		responses = Array(
			new ApiResponse(
				responseCode = "200",
				content = Array(
					new Content(
						schema = new Schema(
							implementation = classOf[KanbanTasksResponse]
						)
					)
				)
			)
		)
	)
	def getAllTasksAndStatusAssigned(@Parameter(hidden = true) companyKey: String): Action[AnyContent]
}
