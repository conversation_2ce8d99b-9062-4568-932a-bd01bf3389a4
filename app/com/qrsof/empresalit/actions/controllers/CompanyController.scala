package com.qrsof.empresalit.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.empresalit.actions.controllers.forms.UpdateCompanyFormData
import com.qrsof.empresalit.domain.company.actions.models.DataCompanyResponse
import io.swagger.v3.oas.annotations.*
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.{Consumes, PUT, Path, Produces}
import play.api.libs.Files.TemporaryFile
import play.api.mvc.{Action, AnyContent, MultipartFormData}

import javax.ws.rs.GET

trait CompanyController {

  @Path("/company/{companyKey}")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get company data",
    tags = Array("Company"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    responses = Array(
      new ApiResponse(responseCode = "200", content = Array(new Content(schema = new Schema(implementation = classOf[DataCompanyResponse])))),
      new ApiResponse(responseCode = "400", content = Array(new Content(schema = new Schema(implementation = classOf[ApiErrorResponse])))),
      new ApiResponse(responseCode = "404", content = Array(new Content(schema = new Schema(implementation = classOf[ApiErrorResponse])))),
      new ApiResponse(responseCode = "500", content = Array(new Content(schema = new Schema(implementation = classOf[ApiErrorResponse]))))
    )
  )
  def getCompanyData(companyKey: String): Action[AnyContent]

  @Path("/company/{companyKey}")
  @PUT
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Consumes(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Update company data",
    tags = Array("Company"),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    requestBody = new RequestBody(
      required = true,
      content = Array(
        new Content(
          schema = new Schema(implementation = classOf[UpdateCompanyFormData])
        )
      )
    ),
    responses = Array(
      new ApiResponse(responseCode = "200", content = Array(new Content(schema = new Schema(implementation = classOf[DataCompanyResponse])))),
      new ApiResponse(responseCode = "400", content = Array(new Content(schema = new Schema(implementation = classOf[ApiErrorResponse])))),
      new ApiResponse(responseCode = "404", content = Array(new Content(schema = new Schema(implementation = classOf[ApiErrorResponse])))),
      new ApiResponse(responseCode = "500", content = Array(new Content(schema = new Schema(implementation = classOf[ApiErrorResponse]))))
    )
  )
  def updateCompanyData(companyKey: String): Action[MultipartFormData[TemporaryFile]]
}
