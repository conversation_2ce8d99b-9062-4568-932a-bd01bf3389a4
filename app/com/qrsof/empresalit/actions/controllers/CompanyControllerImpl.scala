package com.qrsof.empresalit.actions.controllers

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.core.app.api.play.BasePlayController
import com.qrsof.core.app.api.play.security.AuthenticatedAction
import com.qrsof.empresalit.actions.controllers.forms.{BrandCompanyForm, CompanyAddressForm, CompanyForm, DataCompanyForm}
import com.qrsof.empresalit.domain.company.actions.CompanyActions
import com.qrsof.empresalit.domain.company.actions.models.{CompanyCertificatesRequestAction, DataCompanyRequestAction}
import com.qrsof.empresalit.views.onboarding.actions.CompanyCertificatesFormView
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.apache.pekko.http.scaladsl.common.StrictForm
import org.apache.pekko.http.scaladsl.model.{ContentTypes, HttpEntity}
import org.apache.pekko.util.ByteString
import org.slf4j.{Logger, LoggerFactory}
import play.api.libs.Files.TemporaryFile
import play.api.libs.json.{Json, Reads}
import play.api.mvc.*
@Singleton
class CompanyControllerImpl @Inject() (cc: ControllerComponents, authenticatedAction: AuthenticatedAction, companyActions: CompanyActions)
    extends AbstractController(cc)
    with BasePlayController
    with CompanyController {
  val logger: Logger = LoggerFactory.getLogger(classOf[CompanyControllerImpl])

  // JSON formatters
  implicit val dataCompanyFormReads: Reads[DataCompanyForm] = Json.reads[DataCompanyForm]
  implicit val companyAddressFormReads: Reads[CompanyAddressForm] = Json.reads[CompanyAddressForm]
  implicit val brandCompanyFormReads: Reads[BrandCompanyForm] = Json.reads[BrandCompanyForm]
  implicit val companyFormReads: Reads[CompanyForm] = Json.reads[CompanyForm]
  override def getCompanyData(companyKey: String): Action[AnyContent] = authenticatedAction { implicit request =>
    request.principal
    logger.info(s"Request Header -> {}", request.principal)
    val companyData = companyActions.getCompanyDataByCompanyKey(companyKey)
    Ok(companyData.toJsValue())
  }

  override def updateCompanyData(companyKey: String): Action[MultipartFormData[TemporaryFile]] = Action(parse.multipartFormData) { implicit request =>
    val updateCompanyRequest = request.body.asFormUrlEncoded
    val body = request.body

    val private_key: Option[StrictForm.FileData] = request.body.file("private_key").map { file =>
      val byteArray = java.nio.file.Files.readAllBytes(file.ref.path)
      val byteString = ByteString(byteArray)
      this.buildFileData(file.filename, byteString)
    }
    val public_key: Option[StrictForm.FileData] = request.body.file("public_key").map { file =>
      val byteArray = java.nio.file.Files.readAllBytes(file.ref.path)
      val byteString = ByteString(byteArray)
      this.buildFileData(file.filename, byteString)
    }
    val password = updateCompanyRequest.get("password").flatMap(_.headOption).getOrElse("")

    val certificates =
      CompanyCertificatesFormView(private_key.getOrElse(this.buildFileData("", ByteString("".getBytes))), public_key.getOrElse(this.buildFileData("", ByteString("".getBytes))), password)

    val logoCompany: Option[StrictForm.FileData] = request.body.file("logo_company").map { logoFile =>
      val byteArray = java.nio.file.Files.readAllBytes(logoFile.ref.path)
      val byteString = ByteString(byteArray)
      this.buildFileData(logoFile.filename, byteString)
    }

    val updateCompanyForm = body
      .file("updatedCompanyForm")
      .map { file =>
        Json.parse(java.nio.file.Files.readAllBytes(file.ref.path)).as[CompanyForm]
      }
      .get
  logger.info(s"Update Company Form:->{}", updateCompanyForm)
  val updateCompanyData = companyActions.updateCompanyDataAction(
    updateCompanyForm
      .into[DataCompanyRequestAction]
      .withFieldConst(_.dataCompany.companyKey, companyKey)
      .withFieldConst(_.certificatesCompany, certificates.transformInto[CompanyCertificatesRequestAction])
      .withFieldConst(_.brandCompany.logoFile, logoCompany)
      .withFieldConst(_.logoCompany, logoCompany)
      .transform
  )
  updateCompanyData match {
    case Left(value)  => InternalServerError(ApiErrorResponse(Seq(value.appError)).toJsValue())
    case Right(value) => Accepted
  }
  }

  def buildFileData(filename: String, byteString: ByteString): StrictForm.FileData = {
    StrictForm.FileData(Some(filename), HttpEntity.Strict(ContentTypes.`application/octet-stream`, byteString))
  }
}
