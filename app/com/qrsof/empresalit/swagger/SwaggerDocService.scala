package com.qrsof.empresalit.swagger

import com.qrsof.core.swagger.*
import com.qrsof.empresalit.actions.controllers.CompanyController
import com.qrsof.empresalit.controllers.companies.CompaniesController
import com.qrsof.empresalit.controllers.invoices.InvoicingController
import com.qrsof.empresalit.controllers.oauth.actions.OauthController
import com.qrsof.empresalit.controllers.resources.ResourcesController
import com.qrsof.empresalit.views.maincontainer.actions.ViewMainContainerController
import com.qrsof.empresalit.views.maincontainer.clients.actions.controllers.ViewsClientsController
import com.qrsof.empresalit.views.maincontainer.companies.actions.controllers.ViewCompaniesController
import com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.controllers.ViewDebtsReceivablesController
import com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.controllers.ViewDebtsToPayController
import com.qrsof.empresalit.views.maincontainer.employee.actions.controllers.ViewEmployeeController
import com.qrsof.empresalit.views.maincontainer.job.actions.controllers.ViewJobController
import com.qrsof.empresalit.views.maincontainer.kanbanboard.action.controllers.ViewKanbanBoardController
import com.qrsof.empresalit.views.maincontainer.presenteeism.actions.controllers.ViewPresenteeismController
import com.qrsof.empresalit.views.maincontainer.quote.actions.controllers.ViewQuoteController
import com.qrsof.empresalit.views.maincontainer.stores.actions.controllers.ViewStoreController
import com.qrsof.empresalit.views.maincontainer.suppliers.actions.controllers.ViewSuppliersController
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.ViewReceptionOfEquipmentsController
import com.qrsof.empresalit.views.onboarding.actions.ViewOnboardingController
import io.swagger.v3.oas.models.security.{SecurityRequirement, SecurityScheme}

import javax.inject.{Inject, Singleton}

@Singleton
class SwaggerDocService @Inject() () extends SwaggerSupport {

  override val apiClasses: Set[Class[?]] = Set(
    classOf[OauthController],
    classOf[ViewMainContainerController],
    classOf[ViewOnboardingController],
    classOf[ViewQuoteController],
    classOf[ViewStoreController],
    classOf[ViewsClientsController],
    classOf[ViewDebtsReceivablesController],
    classOf[ViewDebtsToPayController],
    classOf[ViewSuppliersController],
    classOf[ViewEmployeeController],
    classOf[ViewJobController],
    classOf[InvoicingController],
    classOf[CompaniesController],
    classOf[ViewCompaniesController],
    classOf[ViewReceptionOfEquipmentsController],
    classOf[ViewKanbanBoardController],
    classOf[ViewPresenteeismController],
    classOf[CompanyController],
    classOf[ResourcesController]

//    classOf[CompanyController]
    //    classOf[UsersController]
  )
  override val swaggerHost = s"localhost:8080"
  override val swaggerInfo: Info = Info(
    version = "1.0",
    title = "Empresalit API",
    termsOfService = "termsOfService",
    contact = Some(
      Contact(
        name = "Develop",
        url = "url",
        email = "<EMAIL>"
      )
    ),
    license = Some(
      License(
        name = "name",
        url = "url"
      )
    ),
    vendorExtensions = Map(("key", "value"))
  )

  private final val bearer = new SecurityScheme().name("Bearer Security").description("Bearer Token based")
  bearer.setType(SecurityScheme.Type.HTTP)
  bearer.setScheme("Bearer")
  bearer.setBearerFormat("JWT")
  override val swaggerSecuritySchemes: Map[String, SecurityScheme] = Map("Bearer" -> bearer)
  override val swaggerSecurity: List[SecurityRequirement] = List(new SecurityRequirement().addList("Bearer"))
  override val swaggerUnwantedDefinitions: Seq[String] = Seq("Function1", "Function1RequestContextFutureRouteResult")

  override def swaggerSchemes: List[String] = List("http", "https")
}
