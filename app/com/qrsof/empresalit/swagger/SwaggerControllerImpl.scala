package com.qrsof.empresalit.swagger

import com.qrsof.empresalit.AppConfigurations
import jakarta.inject.{Inject, Singleton}
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

@Singleton
class SwaggerControllerImpl @Inject()(appConfigurations: AppConfigurations, swaggerHttpServiceImpl: SwaggerDocService,
                                      cc: ControllerComponents
                                   ) extends AbstractController(cc) with SwaggerController {

  def swaggerSpec(): Action[AnyContent] = Action {
    Ok(swaggerHttpServiceImpl.getJsonSwagger()).as(JSON)
  }

  def swaggerUi: Action[AnyContent] = Action {
    Redirect("https://petstore.swagger.io/?url=" + appConfigurations.appHost + "/api-docs/swagger.json")
  }
}
