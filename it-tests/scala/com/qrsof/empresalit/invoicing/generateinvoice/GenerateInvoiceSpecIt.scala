package com.qrsof.empresalit.invoicing.generateinvoice

import com.google.inject.{AbstractModule, <PERSON><PERSON><PERSON>, Provides}
import com.qrsof.core.certificates.{CertificateUtils, CertificateUtilsImpl}
import com.qrsof.empresalit.invoicing.domain.complementos.Complementos
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.figura.transporte.TiposFigura
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.figura.transporte.DomicilioFigura
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.mercancias.*
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.ubicaciones.{Domicilio, Ubicacion}
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.{CartaPorte, Figuratransporte, Mercancias, Ubicaciones}
import com.qrsof.empresalit.invoicing.domain.complementos.nomina.*
import com.qrsof.empresalit.invoicing.domain.complementos.pagos.{DoctoRelacionado, Pago, RecepcionPago}
import com.qrsof.empresalit.invoicing.domain.conceptos.impuestos.ConceptoTraslado
import com.qrsof.empresalit.invoicing.domain.conceptos.{Concepto, ConceptosImpuestos}
import com.qrsof.empresalit.invoicing.domain.impuestos.{Impuestos, Traslado}
import com.qrsof.empresalit.invoicing.domain.invoicing.IssuerInvoicingData
import com.qrsof.empresalit.invoicing.domain.invoicing.v30.GenerateInvoiceRequest
import com.qrsof.empresalit.invoicing.domain.invoicing.xml.*
import com.qrsof.empresalit.invoicing.domain.{EmpresalitUtils, EmpresalitUtilsImpl}
import com.qrsof.empresalit.invoicing.geteways.sifei.SifeiConfigurations
import com.qrsof.empresalit.invoicing.geteways.{PacGatewayFactory, PacGatewayFactoryImpl}
import net.codingwell.scalaguice.ScalaModule
import org.apache.commons.compress.archivers.zip.ZipFile
import org.apache.commons.compress.utils.SeekableInMemoryByteChannel
import org.apache.commons.io.IOUtils
import org.mockito.Mockito
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import java.security.PrivateKey
import jakarta.inject.Singleton

class GenerateInvoiceSpecIt extends PathAnyFreeSpec with MockitoSugar with Matchers {
	private val generateInvoiceProxy: GenerateInvoiceProxy = mock[GenerateInvoiceProxy]

	"Generate invoce" - {
		val injector = Guice.createInjector(
			new GenerateInvoiceActionTestModule(),
			new InvoiceXmlGeneratorModule(),
		)

		val emisorKey = "emisorKey"

		val privateKeyFactoryImpl = new PrivateKeyFactoryImpl
		val urlResource = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/CSD_XOCHILT_CASAS_CHAVEZ_CACX7605101P8_20190528_173544.key")
		val privateKeyContent = urlResource.openStream().readAllBytes()
		val privateKey: PrivateKey = privateKeyFactoryImpl.getInstance(privateKeyContent, "12345678a")

		val urlResourcePk = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/30001000000400002335.cer")
		val publicKeyContent = urlResourcePk.openStream().readAllBytes()

		val generateInvoiceAction: GenerateInvoiceAction = injector.getInstance(classOf[GenerateInvoiceAction])
		val issuerInvoicingData = IssuerInvoicingData(
			certificateNumber = "30001000000400002335",
			privateKey = privateKey,
			publicKey = publicKeyContent,
			rfc = "CACX7605101P8",
			name = "XOCHILT CASAS CHAVEZ",
			regimenFiscalClave = "621"
		)
		val generateInvoiceRequest = GenerateInvoiceRequest(
			version = "3.3",
			companyKey = emisorKey,
			serie = Some("A"),
			folio = Some("1"),
			lugarExpedicion = "42083",
			formaPago = Some("02"),
			metodoPago = Some("PUE"),
			moneda = "MXN",
			tipoComprobante = "I",
			subTotal = BigDecimal(10),
			total = BigDecimal(11.60),
			receptor = Some(ReceptorInvoice(rfc = "XAXX010101000", usoCfdi = "G01")),
			conceptos = Seq(
				Concepto(
					claveProductoServicio = "81111500",
					cantidad = BigDecimal(1),
					claveUnidad = "E48",
					valorUnitario = BigDecimal(10),
					descripcion = "descripcion",
					importe = BigDecimal(10),
					impuestos = Some(ConceptosImpuestos(
						traslados = Seq(ConceptoTraslado(BigDecimal(10), "002", "Tasa", BigDecimal("0.160000"), BigDecimal(1.6))),
					))
				)
			),
			impuestos = Some(Impuestos(
				totalImpuestosTrasladados = Some(BigDecimal(1.6)),
				traslados = Some(Seq(
					Traslado("002", "Tasa", BigDecimal("0.160000"), BigDecimal(1.6))
				))
			))
		)

		Mockito.when(generateInvoiceProxy.getIssuerInvoicingDataByCompanyKey(emisorKey)) thenReturn issuerInvoicingData

		"WHEN: generate invoice" in {
			val stampedXml: Array[Byte] = generateInvoiceAction.execute(generateInvoiceRequest)
			stampedXml shouldNot (be(null))
			val seekableInMemoryByteChannel = new SeekableInMemoryByteChannel(stampedXml)
			val zipFile = new ZipFile(seekableInMemoryByteChannel)
			val entries = zipFile.getEntries
			val archiveEntry = entries.nextElement()
			val inputStream = zipFile.getInputStream(archiveEntry)
			val bytes = IOUtils.toByteArray(inputStream)
			println("Stamped CFDI: " + new String(bytes))
		}
	}

	"Generate invoce with Complemento Pagos" - {
		val injector = Guice.createInjector(
			new GenerateInvoiceActionTestModule(),
			new InvoiceXmlGeneratorModule(),
		)

		val emisorKey = "emisorKey"

		val privateKeyFactoryImpl = new PrivateKeyFactoryImpl
		val urlResource = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/CSD_XOCHILT_CASAS_CHAVEZ_CACX7605101P8_20190528_173544.key")
		val privateKeyContent = urlResource.openStream().readAllBytes()
		val privateKey: PrivateKey = privateKeyFactoryImpl.getInstance(privateKeyContent, "12345678a")

		val urlResourcePk = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/30001000000400002335.cer")
		val publicKeyContent = urlResourcePk.openStream().readAllBytes()

		val generateInvoiceAction: GenerateInvoiceAction = injector.getInstance(classOf[GenerateInvoiceAction])
		val issuerInvoicingData = IssuerInvoicingData(
			certificateNumber = "30001000000400002335",
			privateKey = privateKey,
			publicKey = publicKeyContent,
			rfc = "CACX7605101P8",
			name = "XOCHILT CASAS CHAVEZ",
			regimenFiscalClave = "621"
		)
		val generateInvoiceRequest = GenerateInvoiceRequest(
			version = "3.3",
			companyKey = emisorKey,
			serie = Some("A"),
			folio = Some("1"),
			lugarExpedicion = "42083",
			moneda = "XXX",
			tipoComprobante = "P",
			subTotal = BigDecimal(0),
			total = BigDecimal(0),
			receptor = Some(ReceptorInvoice(rfc = "XAXX010101000", usoCfdi = "P01")),
			conceptos = Seq(
				Concepto(
					claveProductoServicio = "84111506",
					cantidad = BigDecimal(1),
					claveUnidad = "ACT",
					valorUnitario = BigDecimal(0),
					descripcion = "Pago",
					importe = BigDecimal(0)
				)
			),
			complementos = Some(
				Complementos(
					recepcionPagos = Some(
						RecepcionPago(
							"1.0",
							pagos = Seq(
								Pago(
									fechaPago = "2021-04-09T23:25:42",
									formaDePagoP = "01",
									monedaP = "MXN",
									monto = BigDecimal("12768.00"),
									doctosRelacionesdos = Seq(
										DoctoRelacionado(
											idDocumento = "43EA82E4-FCC5-421F-879E-0D0C1A5807E5",
											monedaDR = "MXN",
											metodoDePagoDR = "PUE"
										)
									)
								)
							)
						)
					)
				)
			)
		)

		Mockito.when(generateInvoiceProxy.getIssuerInvoicingDataByCompanyKey(emisorKey)) thenReturn issuerInvoicingData

		"WHEN: generate invoice" in {
			val stampedXml: Array[Byte] = generateInvoiceAction.execute(generateInvoiceRequest)
			stampedXml shouldNot (be(null))
			val seekableInMemoryByteChannel = new SeekableInMemoryByteChannel(stampedXml)
			val zipFile = new ZipFile(seekableInMemoryByteChannel)
			val entries = zipFile.getEntries
			val archiveEntry = entries.nextElement()
			val inputStream = zipFile.getInputStream(archiveEntry)
			val bytes = IOUtils.toByteArray(inputStream)
			println("Stamped CFDI: " + new String(bytes))
		}
	}

	"Generate invoce with Carta Porte" - {
		val injector = Guice.createInjector(
			new GenerateInvoiceActionTestModule(),
			new InvoiceXmlGeneratorModule(),
		)

		val emisorKey = "emisorKey"

		val privateKeyFactoryImpl = new PrivateKeyFactoryImpl
		val urlResource = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/CSD_XOCHILT_CASAS_CHAVEZ_CACX7605101P8_20190528_173544.key")
		val privateKeyContent = urlResource.openStream().readAllBytes()
		val privateKey: PrivateKey = privateKeyFactoryImpl.getInstance(privateKeyContent, "12345678a")

		val urlResourcePk = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/30001000000400002335.cer")
		val publicKeyContent = urlResourcePk.openStream().readAllBytes()

		val generateInvoiceAction: GenerateInvoiceAction = injector.getInstance(classOf[GenerateInvoiceAction])
		val issuerInvoicingData = IssuerInvoicingData(
			certificateNumber = "30001000000400002335",
			privateKey = privateKey,
			publicKey = publicKeyContent,
			rfc = "CACX7605101P8",
			name = "XOCHILT CASAS CHAVEZ",
			regimenFiscalClave = "621"
		)
		val generateInvoiceRequestCarta = GenerateInvoiceRequest(
			version = "3.3",
			companyKey = emisorKey,
			serie = Some("A"),
			folio = Some("1"),
			lugarExpedicion = "42083",
			moneda = "XXX",
			tipoComprobante = "T",
			subTotal = BigDecimal(0),
			total = BigDecimal(0),
			receptor = Some(ReceptorInvoice(rfc = "CACX7605101P8", usoCfdi = "P01")),
			conceptos = Seq(
				Concepto(
					claveProductoServicio = "60121514",
					cantidad = BigDecimal(1),
					claveUnidad = "X44",
					valorUnitario = BigDecimal(0),
					descripcion = "Pago",
					importe = BigDecimal(0)
				)
			),
			complementos = Some(
				Complementos(
					cartaPorte20 = Some(
						CartaPorte(
							transpInternac = "No",
							//							entradaSalidaMerc = Some("Salida"),
							//							paisOrigenDestino = Some("MEX"),
							//							viaEntradaSalida = Some("01"),
							totalDistRec = Some(BigDecimal("21")),
							ubicaciones = Ubicaciones(
								ubicacion = Seq(Ubicacion(
									tipoUbicacion = "Origen",
									idUbicacion = Some("OR000001"),
									rfcRemitenteDestinatario = "MCO081110B28",
									nombreRemitenteDestinatario = Some("nombreRemitenteDestinatario"),
									//										numRegIdTrib = Some("NumRegIdTrib"),
									//										residenciaFiscal = Some("ARG"),
									//										numEstacion = Some("numEstacion"),
									//										nombreEstacion = Some("nombreEstacion"),
									//										navegacionTrafico = Some("Altura"),
									fechaHoraSalidaLlegada = "2021-04-09T23:25:42",
									//										tipoEstacion = Some("01"),
									//										distanciaRecorrida = Some(BigDecimal("21.23")),
									domicilio = Some(
										Domicilio(
											calle = Some("calle"),
											numeroExterior = Some("numeroExterior"),
											numeroInterior = Some("numeroInterior"),
											colonia = Some("0150"),
											localidad = None,
											referencia = Some("referencia"),
											municipio = Some("048"),
											estado = "HID",
											pais = "MEX",
											codigoPostal = "42083"))
								),
									Ubicacion(
										tipoUbicacion = "Destino",
										idUbicacion = Some("DE000000"),
										rfcRemitenteDestinatario = "MCO081110B28",
										nombreRemitenteDestinatario = Some("nombreRemitenteDestinatario"),
										//										numRegIdTrib = Some("NumRegIdTrib"),
										//										residenciaFiscal = Some("ARG"),
										//										numEstacion = Some("numEstacion"),
										//										nombreEstacion = Some("nombreEstacion"),
										//										navegacionTrafico = Some("Altura"),
										fechaHoraSalidaLlegada = "2021-04-09T23:25:42",
										//										tipoEstacion = Some("01"),
										distanciaRecorrida = Some(BigDecimal("21")),
										domicilio = Some(
											Domicilio(
												calle = Some("calle"),
												numeroExterior = Some("numeroExterior"),
												numeroInterior = Some("numeroInterior"),
												colonia = Some("0150"),
												localidad = None,
												referencia = Some("referencia"),
												municipio = Some("048"),
												estado = "HID",
												pais = "MEX",
												codigoPostal = "42083")
										)
									)
								)),
							mercancias = Mercancias(
								pesoBrutoTotal = BigDecimal("24"),
								unidadPeso = "Tu",
								pesoNetoTotal = Some(BigDecimal("24")),
								numTotalMercancias = 1,
								cargoPorTasacion = Some(BigDecimal("32.31")),
								mercancia = Seq(
									Mercancia(
										bienesTransp = "60121514",
										clavesSTCC = Some("000000"),
										descripcion = "descripcion",
										cantidad = BigDecimal("32.43"),
										claveUnidad = "X44",
										unidad = Some("unidad"),
										dimensiones = Some("59/40/36cm"),
										//											materialPeligroso = Some("No"),
										//											cveMaterialPeligroso = Some("M0001"),
										//											embalaje = Some("1A1"),
										//											descripEmbalaje = Some("descripEmbalaje"),
										pesoEnKg = BigDecimal("24"),
										valorMercancia = Some(BigDecimal("23.23")),
										moneda = Some("MXN"),
										//											fraccionArancelaria = Some("8418699999"),
										uuidComercioExt = Some("550e8400-e29b-41d4-a716-************"),
										//											pedimentos = Some(
										//												Pedimento(
										//													pedimento = "10  47  3807  8003832"
										//												)),
										//											guiasIdentificacion = Some(
										//												GuiasIdentificacion(
										//													numeroGuiaIdentificacion = "numeroGuiaIdentificacion",
										//													descripGuiaIdentificacion = "descripGuiaIdentificacion",
										//													pesoGuiaIdentificacion = BigDecimal("12.11")
										//												)),
										cantidadTransporta = Seq(
											CantidadTransporta(
												cantidad = BigDecimal("23.32"),
												idOrigen = "OR000001",
												idDestino = "DE000000",
												//													cvesTransporte = Some("01")
											)),
										//											detalleMercancia = Some(DetalleMercancia(
										//												unidadPesoMerc = "Tu",
										//												pesoBruto = BigDecimal("32.24"),
										//												pesoNeto = BigDecimal("34.23"),
										//												pesoTara = BigDecimal("43.34"),
										//												numPiezas = Some(23)
										//											))
									)),
								autotransporte = Some(
									Autotransporte(
										permSCT = "TPAF_01",
										numPermisoSCT = "23",
										identificacionVehicular = IdentificacionVehicular(
											configVehicular = "VL",
											placaVM = "placaVM",
											anioModeloVM = 2020
										),
										seguros = Seguros(
											aseguraRespCivil = "aseguraRespCivil",
											polizaRespCivil = "polizaRespCivil",
											//													aseguraMedAmbiente = Some("aseguraMedAmbiente"),
											//													polizaMedAmbiente = Some("polizaMedioAmbiente"),
											aseguraCarga = Some("aseguraCarga"),
											polizaCarga = Some("polizaCarga"),
											primaSeguro = Some(BigDecimal("23.23"))
										),
										remolques = Some(
											Remolques(
												remolque = Seq(
													Remolque(
														subTipoRem = "CTR_004",
														placa = "FM43535"
													))
											))
									)),
								//									transporteMaritimo = Some(
								//										TransporteMaritimo(
								//											permSCT = Some("TPAF02"),
								//											numPermisoSCT = Some("234"),
								//											nombreAseg = Some("nombreAseg"),
								//											numPolizaSeguro = Some("numPolizaSeguro"),
								//											tipoEmbarcacion = "B_03",
								//											matricula = "FM43535",
								//											numeroOMI = "IMO0000000",
								//											anioEmbarcacion = Some(2021),
								//											nombreEmbarc = Some("nombreEmbarc"),
								//											nacionalidadEmbarc = "MEX",
								//											unidadesDeArqBruto = BigDecimal("23.12"),
								//											tipoCarga = "GMN",
								//											numCertITC = "numCertITC",
								//											eslora = Some(BigDecimal("50.43")),
								//											manga = Some(BigDecimal("34.23")),
								//											calado = Some(BigDecimal("43.24")),
								//											lineaNaviera = Some("lineaNaviera"),
								//											nombreAgenteNaviero = "nombreAgenteNaviero",
								//											numAutorizacionNaviero = "SCT418/020/2016",
								//											numViaje = Some("3456"),
								//											numConocEmbarc = Some("45436"),
								//											contenedor = Seq(
								//												Contenedor(
								//													matriculaContenedor = "ME453EWR111",
								//													tipoContenedor = "CM_001",
								//													numPrecinto = Some("numPrecinto")
								//												))
								//										)),
								//									transporteAereo = Some(
								//										TransporteAereo(
								//											permSCT = "TPAF_05",
								//											numPermisoSCT = "24325",
								//											matriculaAeronave = Some("KSAJFEN453"),
								//											nombreAseg = Some("nombreAseg"),
								//											numPolizaSeguro = Some("42354"),
								//											numeroGuia = "232351111111",
								//											lugarContrato = Some("lugarContrato"),
								//											codigoTransportista = "CA_002",
								//											rfcEmbarcador = Some("CACX7605101P8"),
								//											numRegIdTribEmbarc = Some("3348357"),
								//											residenciaFiscalEmbarc = Some("ARG"),
								//											nombreEmbarcador = Some("nombreEmbarcador")
								//										)),
								//									transporteFerroviario = Some(
								//										TransporteFerroviario(
								//											tipoServicio = "TS_01",
								//											tipoDeTrafico = "TT_01",
								//											nombreAseg = Some("nombreAseg"),
								//											numPolizaSeguro = Some("30923843"),
								//											derechosDePaso = Some(
								//												DerechosDePaso(
								//													tipoDerechoDePaso = "CDP_001",
								//													kilometrajePagado = BigDecimal("324.34")
								//												)), carro = Seq(
								//												Carro(
								//													tipoCarro = "TC_01",
								//													matriculaCarro = "ETD23213",
								//													guiaCarro = "guiaCarro",
								//													toneladasNetasCarro = BigDecimal("23.24"),
								//													contenedor = Some(ContenedorCarro(
								//														tipoContenedor = "TC_01",
								//														pesoContenedorVacio = BigDecimal("23.23"),
								//														pesoNetoMercancia = BigDecimal("85.33")
								//													))
								//												))
								//										))
							),
							figuratransporte = Some(
								Figuratransporte(
									tiposFigura = Seq(
										TiposFigura(
											tipoFigura = "01",
											rfcFigura = Some("MCO081110B28"),
											numLicencia = Some("342751"),
											nombreFigura = Some("nombreFigura"),
											//											numRegIdTribFigura = Some("354511"),
											//											residenciaFiscalFigura = Some("ARG"),
											//											partesTransporte = Option(
											//												PartesTransporte(
											//													parteTransporte = "PT01"
											//												)),
											domicilio = Some(
												DomicilioFigura(
													calleFigura = Some("calleFigura"),
													numeroExteriorFigura = Some("24"),
													numeroInteriorFigura = Some("34"),
													coloniaFigura = Some("0150"),
													localidadFigura = None,
													referenciaFigura = Some("referenciaFigura"),
													municipioFigura = Some("048"),
													estadoFigura = "HID",
													paisFigura = "MEX",
													codigoPostalFigura = "42083"
												))
										))
								))
						)
					)
				)
			)
		)

		Mockito.when(generateInvoiceProxy.getIssuerInvoicingDataByCompanyKey(emisorKey)) thenReturn issuerInvoicingData

		"WHEN: generate invoice with carta porte" in {
			val stampedXml: Array[Byte] = generateInvoiceAction.execute(generateInvoiceRequestCarta)
			stampedXml shouldNot (be(null))
			val seekableInMemoryByteChannel = new SeekableInMemoryByteChannel(stampedXml)
			val zipFile = new ZipFile(seekableInMemoryByteChannel)
			val entries = zipFile.getEntries
			val archiveEntry = entries.nextElement()
			val inputStream = zipFile.getInputStream(archiveEntry)
			val bytes = IOUtils.toByteArray(inputStream)
			println("Stamped CFDI: " + new String(bytes))
		}
	}

	"Generate invoce with Nomina" - {
		val injector = Guice.createInjector(
			new GenerateInvoiceActionTestModule(),
			new InvoiceXmlGeneratorModule(),
		)

		val emisorKey = "emisorKey"

		val privateKeyFactoryImpl = new PrivateKeyFactoryImpl
		val urlResource = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/CSD_XOCHILT_CASAS_CHAVEZ_CACX7605101P8_20190528_173544.key")
		val privateKeyContent = urlResource.openStream().readAllBytes()
		val privateKey: PrivateKey = privateKeyFactoryImpl.getInstance(privateKeyContent, "12345678a")

		val urlResourcePk = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/30001000000400002335.cer")
		val publicKeyContent = urlResourcePk.openStream().readAllBytes()

		val generateInvoiceAction: GenerateInvoiceAction = injector.getInstance(classOf[GenerateInvoiceAction])
		val issuerInvoicingData = IssuerInvoicingData(
			certificateNumber = "30001000000400002335",
			privateKey = privateKey,
			publicKey = publicKeyContent,
			rfc = "CACX7605101P8",
			name = "XOCHILT CASAS CHAVEZ",
			regimenFiscalClave = "621"
		)
		val generateInvoiceRequestNomina = GenerateInvoiceRequest(
			version = "3.3",
			companyKey = emisorKey,
			serie = Some("A"),
			folio = Some("1"),
			lugarExpedicion = "42083",
			formaPago = Some("99"),
			moneda = "MXN",
			tipoComprobante = "N",
			subTotal = BigDecimal("150"),
			total = BigDecimal("1135"),
			descuento = Some(BigDecimal("0")),
			receptor = Some(ReceptorInvoice(rfc = "XAXX010101000", usoCfdi = "P01")),
			conceptos = Seq(
				Concepto(
					claveProductoServicio = "84111505",
					cantidad = BigDecimal("1"),
					claveUnidad = "ACT",
					valorUnitario = BigDecimal("300"),
					descripcion = "Pago de nómina",
					importe = BigDecimal("300"),
					descuento = Some(BigDecimal("0"))
				)
			),
			complementos = Some(
				Complementos(
					nomina12 = Some(
						NominaComp(
							version = "1.2",
							tipoNomina = "O",
							fechaPago = "2022-01-19",
							fechaInicialPago = "2022-01-01",
							fechaFinalPago = "2022-01-19",
							numDiasPagados = BigDecimal("5.500"),
							totalPercepciones = Some(BigDecimal("300")),
							totalDeducciones = Some(BigDecimal("0")),
							totalOtrosPagos = Some(BigDecimal("0")),
							emisor = Some(
								Emisor(
									curp = Some("XEXX010101MNEXXXA8"),
									registroPatronal = Some("RegistroPatronal"),
									rfcPatronOrigen = Some("CACX7605101P8"),
									//									entidadSNCF = Some(
									//										EntidadSNCF(
									//											origenRecurso = "IP",
									//											montoRecursoPropio = Some(BigDecimal("21.24"))
									//										)
									//									)
								)
							),
							receptor = ReceptorNom(
								curp = "XEXX010101MNEXXXA8",
								numSeguridadSocial = Some("123456789876543"),
								fechaInicioRelLaboral = Some("2022-01-01"),
								antiguedad = Some("P19D"),
								tipoContrato = "01",
								sindicalizado = Some("No"),
								tipoJornada = Some("01"),
								tipoRegimen = "02",
								numEmpleado = "numEmpleado",
								departamento = Some("departamento"),
								puesto = Some("puesto"),
								riesgoPuesto = Some("5"),
								periodicidadPago = "05",
								banco = Some("019"),
								cuentaBancaria = Some("87968098743".toLong),
								salarioBaseCotApor = Some(BigDecimal("50")),
								salarioDiarioIntegrado = Some(BigDecimal("32.35")),
								claveEntFed = "AGU",
								subContratacion = Seq(
									SubContratacion(
										rfcLabora = "CACX7605101P8",
										porcentajeTiempo = BigDecimal("100")
									)
								)
							),
							percepciones = Some(
								Percepciones(
									totalSueldos = Some(BigDecimal("100")),
									totalSeparacionIndemnizacion = Some(BigDecimal("100")),
									totalJubilacionPensionRetiro = Some(BigDecimal("100")),
									totalGravado = BigDecimal("300"),
									totalExento = BigDecimal("0"),
									percepcion = Seq(
										Percepcion(
											tipoPercepcion = "022",
											clave = "clave",
											concepto = "concepto",
											importeGravado = BigDecimal("100"),
											importeExento = BigDecimal("0"),
											accionesOTitulos = Some(
												AccionesOTitulos(
													valorMercado = BigDecimal("23.21"),
													precioAlOtorgarse = BigDecimal("32.65")
												)
											),
											horasExtra = Seq(
												HorasExtra(
													dias = 23,
													tipoHoras = "01",
													horasExtra = 43,
													importePagado = BigDecimal("43")
												)
											)
										)
									),
									jubilacionPensionRetiro = Some(
										JubilacionPensionRetiro(
											totalUnaExhibicion = Some(BigDecimal("34.87")),
											totalParcialidad = Some(BigDecimal("34.87")),
											montoDiario = Some(BigDecimal("34.87")),
											ingresoAcumulable = BigDecimal("34.87"),
											ingresoNoAcumulable = BigDecimal("34.87")
										)
									),
									separacionIndemnizacion = Some(
										SeparacionIndemnizacion(
											totalPagado = BigDecimal("34.87"),
											numAniosServicio = 4,
											ultimoSueldoMensOrd = BigDecimal("34.87"),
											ingresoAcumulable = BigDecimal("34.87"),
											ingresoNoAcumulable = BigDecimal("34.87")
										)
									)
								)
							),
							deducciones = Some(
								Deducciones(
									totalOtrasDeducciones = Some(BigDecimal("0")),
									totalImpuestosRetenidos = Some(BigDecimal("0")),
									deduccion = Seq(
										Deduccion(
											tipoDeduccion = "001",
											clave = "clave",
											concepto = "concepto",
											importe = BigDecimal("50")
										)
									)
								)
							),
							otrosPagos = None,
							incapacidades = Some(
								Incapacidades(
									incapacidad = Seq(
										Incapacidad(
											diasIncapacidad = 4,
											tipoIncapacidad = "03",
											importeMonetario = Some(BigDecimal("345"))
										)
									)
								)
							)
						)
					)
				)
			)
		)

		Mockito.when(generateInvoiceProxy.getIssuerInvoicingDataByCompanyKey(emisorKey)) thenReturn issuerInvoicingData

		"WHEN: generate invoice with Nomina" in {
			val stampedXml: Array[Byte] = generateInvoiceAction.execute(generateInvoiceRequestNomina)
			stampedXml shouldNot be(null)
			val seekableInMemoryByteChannel = new SeekableInMemoryByteChannel(stampedXml)
			val zipFile = new ZipFile(seekableInMemoryByteChannel)
			val entries = zipFile.getEntries
			val archiveEntry = entries.nextElement()
			val inputStream = zipFile.getInputStream(archiveEntry)
			val bytes = IOUtils.toByteArray(inputStream)
			println("Stamped CFDI: " + new String(bytes))
		}
	}


	class GenerateInvoiceActionTestModule extends AbstractModule with ScalaModule {

		override def configure(): Unit = {
//			bind[EmpresalitUtils].to[EmpresalitUtilsImpl].in[Singleton]
//			bind[GenerateInvoiceAction].to[GenerateInvoiceActionImpl].in[Singleton]
//			bind[GenerateInvoiceProxy].toInstance(generateInvoiceProxy)
//			bind[PacGatewayFactory].to[PacGatewayFactoryImpl].in[Singleton]
//			bind[CertificateUtils].to[CertificateUtilsImpl].in[Singleton]
		}

		@Provides()
		def sifeiConfigurations(): SifeiConfigurations = {
			new SifeiConfigurations {
				override val idEquipo: String = "YzBiMGM1NWEtOTc0ZC1kYjllLWQ3N2EtNWNmYmRlZDIzYzQw"

				override val password: String = "16aad203"

				override val user: String = "HEPC880430CJ1"

				override val wsdlUrl: String = "http://devcfdi.sifei.com.mx:8080/SIFEI33/SIFEI?wsdl"

				override val wsdlCancellationUrl: String = "http://devcfdi.sifei.com.mx:8888/CancelacionSIFEI/Cancelacion?wsdl"
			}
		}

	}

}
