package com.qrsof.empresalit.invoicing.domain.company.dao

import com.qrsof.core.database.qrslick.{DatabaseContext, QrPostgresProfile, QueryExecutor}
import com.qrsof.empresalit.companies.*
import com.qrsof.empresalit.domain.storage.StorageGateway
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.ExecutionContext.Implicits.global

class CompanyGatewayImplTest(implicit queryExecutor: QueryExecutor, val storage: StorageGateway) extends DatabaseContext with MockitoSugar {
  override val tables: Option[QrPostgresProfile.DDL] = Some(
    CompanyRepository.table ++
      CompanyCertificateRepository.table
  )

  "CompanyDaoImpl" - withDatabase { implicit t =>
    new CompanyCertificateMemoryStorage
    val companyDaoImpl = new CompanyGatewayImpl(queryExecutor, storage)

    "Save company" in {
      val company = Company(
        key = "key",
        name = "name",
        rfc = "rfc",
        regimenFiscalClave = "regimen"
      )
      companyDaoImpl.save(company)
      val someCompany = companyDaoImpl.getCompanyByKey(company.key)
      someCompany shouldEqual (Some(company))
    }

    "Get company certificate" in {
      val company = Company(
        key = "key",
        name = "name",
        rfc = "rfc",
        regimenFiscalClave = "regimen"
      )
      companyDaoImpl.save(company)

      val certificate = CompanyCertificate(
        key = "cetificatekey",
        companyKey = company.key,
        noCertificado = "nocertificado",
        privateKey = "privatekey".getBytes,
        password = "password",
        publicKey = "publicKey".getBytes
      )

      companyDaoImpl.saveCompanyCertificate(certificate)

      val someCertificate = companyDaoImpl.getCompanyCertificateByCompanyKey(company.key)
      someCertificate shouldEqual (Some(certificate))
    }

  }

}
