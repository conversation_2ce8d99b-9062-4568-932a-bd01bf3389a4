package com.qrsof.empresalit.repositories.addresses

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}
import org.slf4j.LoggerFactory

import scala.concurrent.ExecutionContext

@Singleton
class AddressesRepositoryImpl @Inject() ()(implicit
    queryExecutor: QueryExecutor,
    executionContext: ExecutionContext
) extends AddressesRepository {
  override def update(addressesEntity: AddressesEntity): AddressesEntity = {
    LoggerFactory.getLogger(classOf[AddressesRepositoryImpl])
    queryExecutor.syncExecuteQuery {
      AddressesTable.tableQuery
        .filter(_.address_key === addressesEntity.address_key)
        .forUpdate
        .result
        .head
        .flatMap(address => {
          AddressesTable.saveOrUpdate(
            address.copy(
              calle = addressesEntity.calle,
              estado = addressesEntity.estado,
              municipio = addressesEntity.municipio,
              codigo_postal = addressesEntity.codigo_postal,
              numero_exterior = addressesEntity.numero_exterior,
              modifiedAt = addressesEntity.modifiedAt
            )
          )
        })
    }
  }

  override def getAddress(address_key: String): AddressesEntity = {
    queryExecutor.syncExecuteQuery(
      AddressesTable.findById(address_key)
    )
  }

  override def delete(address_key: String): Unit = {
    queryExecutor.syncExecuteQuery {
      AddressesTable.deleteByProperty(_.address_key === address_key)
    }
  }

  override def save(addressesEntity: AddressesEntity): AddressesEntity = {
    queryExecutor.syncExecuteQuery {
      AddressesTable.save(addressesEntity)
    }
  }

  override def getCompanyAddress(): Seq[AddressesEntity] = {
    queryExecutor.syncExecuteQuery {
      AddressesTable.list
    }
  }
}
