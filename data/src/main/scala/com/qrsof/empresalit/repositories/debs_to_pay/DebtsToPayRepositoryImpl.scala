package com.qrsof.empresalit.repositories.debs_to_pay

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class DebtsToPayRepositoryImpl @Inject()()(
  implicit queryExecutor: QueryExecutor,
  executionContext: ExecutionContext
) extends DebtsToPayRepository {
  override def save(debtsToPayEntity: DebtsToPayEntity): DebtsToPayEntity = {
    queryExecutor.syncExecuteQuery {
      DebtsToPayTable.save(debtsToPayEntity)
    }
  }

  override def getDebts(): Seq[DebtsToPayEntity] = {
    queryExecutor.syncExecuteQuery {
      DebtsToPayTable.list
    }
  }

  override def findDebtToPay(debtToPayKey: String): Option[DebtsToPayEntity] = {
    queryExecutor.syncExecuteQuery {
      DebtsToPayTable.findOptionByProperty(_.debt_to_pay_key === debtToPayKey)
    }
  }

  override def deleteDebtToPay(debtToPayKey: String): Unit = {
    queryExecutor.syncExecuteQuery {
      DebtsToPayTable.deleteByProperty(_.debt_to_pay_key === debtToPayKey)
    }
  }

  override def markAsPaid(debtToPay: DebtsToPayEntity): Unit = {
    queryExecutor.syncExecuteQuery {
      DebtsToPayTable.saveOrUpdate(debtToPay)
    }
  }
}

