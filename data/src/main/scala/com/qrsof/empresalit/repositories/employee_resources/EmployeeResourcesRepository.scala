package com.qrsof.empresalit.repositories.employee_resources

import com.qrsof.empresalit.domain.employee_resources.pojos.EmployeeResourceData
import com.qrsof.empresalit.repositories.resources.ResourceEntity

trait EmployeeResourcesRepository {

  def getResourcesByEmployeeKey(employeeKey: String): Seq[ResourceEntity]
  def getResourcesDataByEmployeeKey(employeeKey: String): Seq[EmployeeResourceData]
  def saveResources(resources: Seq[EmployeeResourceEntity]): Unit
}
