package com.qrsof.empresalit.repositories.status

import jakarta.inject.{Inject, Singleton}
import com.qrsof.empresalit.domain.status.StatusGateway
import com.qrsof.empresalit.domain.status.pojos.{ChangeStatusOrderRequest, StatusDto}
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.StatusListResponse

@Singleton
class StatusGatewayImpl @Inject() (statusRepository: StatusRepository) extends StatusGateway{

	override def getStatusListByCompanyKey(companyKey: String): Seq[StatusListResponse] = {
		statusRepository.getStatusByCompanyKey(companyKey).map(status => StatusListResponse(status.status_key, status.status, status.order_process))
	}

	override def getStatusByStatusKey(statusKey: String): Option[StatusDto] = {
		statusRepository.getStatusByStatusKey(statusKey).map(status => {
			StatusDto(status.status_key, status.status, status.order_process, status.company_key)
		})
	}

	override def changeStatusOrder(changeStatusOrderRequest: ChangeStatusOrderRequest): Unit = {
		statusRepository.changeStatusOrder(changeStatusOrderRequest)
	}
}
