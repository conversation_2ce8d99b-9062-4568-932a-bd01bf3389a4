package com.qrsof.empresalit.repositories.seniority_benefits

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class SeniorityBenefitsTable(tag: Tag) extends Table[SeniorityBenefitsEntity](tag, Some("empresalit"), "seniority_benefits") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def yearId: Rep[Int] = column[Int]("years_id", O.<PERSON>ey)

  def vacationDays: Rep[Int] = column[Int]("vacation_days")

  def startDate = column[Date]("start_date")

  def createdAt = column[Date]("created_at")

  def updatedAt = column[Date]("updated_at")

  def * : ProvenShape[SeniorityBenefitsEntity] = (yearId, vacationDays, startDate, createdAt, updatedAt).<>(SeniorityBenefitsEntity.apply, SeniorityBenefitsEntity.unapply)
}

object SeniorityBenefitsTable extends GenericDao {
  override type Id = Int

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = SeniorityBenefitsEntity
  override type EntityTable = SeniorityBenefitsTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[SeniorityBenefitsTable] = TableQuery[SeniorityBenefitsTable]

  override def $id(table: EntityTable): Rep[Id] = table.yearId

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.yearId
    } { (entity, id) =>
      entity.copy(yearId = id)
    }
  }
}
