package com.qrsof.empresalit.repositories.inabilities_and_vacations

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import com.qrsof.empresalit.repositories.employee.EmployeeTable
import com.qrsof.empresalit.repositories.jobs.JobsTable
import com.qrsof.empresalit.repositories.seniority_benefits.{SeniorityBenefitsEntity, SeniorityBenefitsTable}
import com.qrsof.empresalit.views.maincontainer.presenteeism.pojos.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}
import slick.jdbc.JdbcType

import java.sql.Timestamp
import java.util.Date
import scala.collection.mutable
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext}

@Singleton
class InabilityAndVacationRepositoryImpl @Inject() (implicit queryExecutor: QueryExecutor, executionContext: ExecutionContext, empresalitUtils: EmpresalitUtils)
    extends InabilityAndVacationRepository {

  val logger: Logger = LoggerFactory.getLogger(classOf[InabilityAndVacationRepositoryImpl])

  override def saveInabilityAndVacation(inabilityAndVacationEntity: InabilityAndVacationEntity): Unit = queryExecutor.syncExecuteQuery {
    InabilityAndVacationsTable.save(inabilityAndVacationEntity)
  }

  override def getInabilityAndVacationsByEmployeeKey(employeeKey: String, presenteeismFilter: PresenteeismFilter): Seq[InabilityAndVacationEntity] = queryExecutor.syncExecuteQuery {
    InabilityAndVacationsTable.findListByProperty(_.employeeKey === employeeKey)
  }

  override def updateInabilityAndVacation(inabilityAndVacationEntity: InabilityAndVacationEntity): Unit = queryExecutor.syncExecuteQuery {
    InabilityAndVacationsTable.tableQuery
      .filter(_.inabilityAndVacationKey === inabilityAndVacationEntity.inabilityAndVacationKey)
      .forUpdate
      .result
      .head
      .flatMap(inabilityAndVacation => {
        InabilityAndVacationsTable.saveOrUpdate(
          inabilityAndVacation.copy(
            startDate = inabilityAndVacationEntity.startDate,
            endDate = inabilityAndVacationEntity.endDate,
            requestType = inabilityAndVacationEntity.requestType,
            days = inabilityAndVacationEntity.days,
            updatedAt = inabilityAndVacationEntity.updatedAt
          )
        )
      })
  }

  override def getInabilityAndVacationByKey(key: String): Option[InabilityAndVacationEntity] = queryExecutor.syncExecuteQuery {
    InabilityAndVacationsTable.findOptionByProperty(_.inabilityAndVacationKey === key)
  }

  override def getInabilityAndVacationsByCompanyKey(companyKey: String, presenteeismFilter: PresenteeismFilter, pageNumber: Int, pageSize: Int): Seq[InabilityAndVacationEntity] =
    queryExecutor.syncExecuteQuery {
      (for {
        inabilityAndVacation <- InabilityAndVacationsTable.tableQuery
        employee <- EmployeeTable.tableQuery.filter(_.companyKey === companyKey)
        if employee.employeeKey === inabilityAndVacation.employeeKey
      } yield inabilityAndVacation)
        // .filterIf(presenteeismFilter.startDate.isDefined)(data => (data.startDate >= presenteeismFilter.startDate.get))
        // .filterIf(presenteeismFilter.endDate.isDefined)(data => (data.startDate <= presenteeismFilter.endDate.get))
        .filterIf(presenteeismFilter.employeeKey.isDefined)(data => data.employeeKey === presenteeismFilter.employeeKey.get)
        // .sortBy(_.createdAt)
        .drop(pageSize * pageNumber)
        .take(pageSize)
        .result
    }

  override def getTotalInabilityAndVacationsByCompanyKey(companyKey: String, presenteeismFilter: PresenteeismFilter): Int = queryExecutor.syncExecuteQuery {
    (for {
      employee <- EmployeeTable.tableQuery.filter(_.companyKey === companyKey)
      inabilityAndVacation <- InabilityAndVacationsTable.tableQuery
      if employee.employeeKey === inabilityAndVacation.employeeKey
    } yield (employee)).length.result
  }

  override def saveInabilityAndVacations(inabilityAndVacationEntitys: Seq[InabilityAndVacationEntity]): Unit = {
    var transactions = mutable.ListBuffer.empty[DBIO[Any]]
    inabilityAndVacationEntitys.foreach(value => transactions.+=(InabilityAndVacationsTable.save(value)))
    val querySeq = transactions.toSeq
    Await.result(
      queryExecutor.database.run(
        DBIO
          .seq(querySeq: _*)
          .transactionally
      ),
      Duration.Inf
    )
  }

  override def getEmployeesWithPresenteeismByCompanyKey(companyKey: String, presenteeismFilter: PresenteeismFilter, pageNumber: Int, pageSize: Int): Seq[EmployeeWithPresenteeismDTO] = {
    implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
      d => new Timestamp(d.getTime),
      ts => new Date(ts.getTime)
    )
    queryExecutor.syncExecuteQuery {
      (for {
        employee <- EmployeeTable.tableQuery.filter(_.companyKey === companyKey)
        jobTable <- JobsTable.tableQuery.filter(_.job_key === employee.jobKey)
        (employee2, inabilityAndVacationsTable) <- EmployeeTable.tableQuery joinLeft InabilityAndVacationsTable.tableQuery on (_.employeeKey === _.employeeKey)
        if employee.employeeKey === employee2.employeeKey
      } yield (employee, jobTable))
        // .filterIf(presenteeismFilter.startDate.isDefined)(data => (data.startDate >= presenteeismFilter.startDate.get))
        // .filterIf(presenteeismFilter.endDate.isDefined)(data => (data.startDate <= presenteeismFilter.endDate.get))
        .filterIf(presenteeismFilter.employeeKey.isDefined)(data => data._1.employeeKey === presenteeismFilter.employeeKey.get)
        .filterIf(presenteeismFilter.name.isDefined)(data => data._1.fullname.toLowerCase like "%".concat(presenteeismFilter.name.get).concat("%").toLowerCase)
        .distinctOn(_._1.employeeKey)
        .drop(pageSize * pageNumber)
        .take(pageSize)
        .sortBy(_._1.fullname)
        .result
        .map(result => {
          result.map(value => {
            val year = empresalitUtils.getYears(value._1.entryDate, new Date())
            val seniorityBenefitsTable: Option[SeniorityBenefitsEntity] = queryExecutor.syncExecuteQuery {
              SeniorityBenefitsTable.tableQuery.filter(_.yearId === year).result.headOption
            }
            var days = 0
            if (seniorityBenefitsTable.isDefined) {
              days = seniorityBenefitsTable.get.vacationDays
              val startDate: Date = empresalitUtils.getDateAddYears(value._1.entryDate, year)
              val endDate: Date = empresalitUtils.getDateAddYears(value._1.entryDate, year + 1)
              val daysByInabilityAndVacations: Option[Int] = queryExecutor.syncExecuteQuery {
                InabilityAndVacationsTable.tableQuery
                  .filter(data => data.employeeKey === value._1.employeeKey && data.requestType === "VACATIONS" && data.startDate >= startDate && data.endDate <= endDate)
                  .map(_.days)
                  .sum
                  .result
              }

              if (daysByInabilityAndVacations.isDefined) {
                days = days - daysByInabilityAndVacations.get

              }
            }
            val dateNow = new Date()
            val inabilityAndVacationEntity: Option[InabilityAndVacationEntity] = (queryExecutor.syncExecuteQuery {
              InabilityAndVacationsTable.tableQuery.filter(data => data.employeeKey === value._1.employeeKey && data.startDate <= dateNow && data.endDate >= dateNow).result.headOption
            })
            var status = "ACTIVE"
            if (inabilityAndVacationEntity.isDefined) {
              status = inabilityAndVacationEntity.get.requestType
            }

            EmployeeWithPresenteeismDTO(employeeKey = value._1.employeeKey, name = value._1.fullname, jobName = value._2.name, lastDate = new Date(), status = status, days = days)
          })
        })
    }
  }

  override def getTotalEmployeesWithPresenteeismByCompanyKey(companyKey: String, presenteeismFilter: PresenteeismFilter): Int = queryExecutor.syncExecuteQuery {
    (for {
      employee <- EmployeeTable.tableQuery.filter(_.companyKey === companyKey)
      jobTable <- JobsTable.tableQuery.filter(_.job_key === employee.jobKey)
    } yield (employee))
      // .filterIf(presenteeismFilter.startDate.isDefined)(data => (data.startDate >= presenteeismFilter.startDate.get))
      // .filterIf(presenteeismFilter.endDate.isDefined)(data => (data.startDate <= presenteeismFilter.endDate.get))
      .filterIf(presenteeismFilter.employeeKey.isDefined)(data => data.employeeKey === presenteeismFilter.employeeKey.get)
      .filterIf(presenteeismFilter.name.isDefined)(data => data.fullname.toLowerCase like "%".concat(presenteeismFilter.name.get).concat("%").toLowerCase)
      .sortBy(_.fullname)
      .length
      .result
  }

  override def getInabilityAndVacationByCompanyKey(companyKey: String, presenteeismFilter: EmployeeAndPresenteeismFilters): Seq[EventDTO] = queryExecutor.syncExecuteQuery {
    val query = for {
      (inabilityAndVacation, employee) <- InabilityAndVacationsTable.tableQuery joinLeft EmployeeTable.tableQuery.filter(
        _.companyKey === companyKey
      ) on (_.employeeKey === _.employeeKey)
    } yield (inabilityAndVacation, employee)

    query
      .distinctOn(_._1.inabilityAndVacationKey)
      .filterIf(
        presenteeismFilter.leavesFilter.isDefined || presenteeismFilter.vacationsFilter.isDefined || presenteeismFilter.absenteeismFilter.isDefined || presenteeismFilter.presenteeismFilter.isDefined
      )(data =>
        data._1.requestType.toUpperCase === presenteeismFilter.leavesFilter.orNull.toUpperCase ||
          data._1.requestType.toUpperCase === presenteeismFilter.vacationsFilter.orNull.toUpperCase ||
          data._1.requestType.toUpperCase === presenteeismFilter.absenteeismFilter.orNull.toUpperCase ||
          data._1.requestType.toUpperCase === presenteeismFilter.presenteeismFilter.orNull.toUpperCase
      )
      .result
      .map(resultQuery => {
        resultQuery.collect { case (inabilityAndVacation, Some(employee)) =>
          EventDTO(
            title = employee.fullname,
            id = inabilityAndVacation.inabilityAndVacationKey,
            date = inabilityAndVacation.startDate,
            start = inabilityAndVacation.startDate,
            end = inabilityAndVacation.endDate,
            className = inabilityAndVacation.requestType,
            extendedProps = EmployeeAndPresenteeismData(
              employeeKey = employee.employeeKey,
              name = employee.fullname,
              presenteeismKey = inabilityAndVacation.inabilityAndVacationKey,
              presenteeismType = inabilityAndVacation.requestType,
              totalDays = inabilityAndVacation.days,
              startDate = inabilityAndVacation.startDate.toString,
              endDate = inabilityAndVacation.endDate.toString
            )
          )
        }
      })
  }

  override def deleteInabilityAndVacation(inabilityAndVacationKey: String): Unit = queryExecutor.syncExecuteQuery {
    InabilityAndVacationsTable.deleteByProperty(_.inabilityAndVacationKey === inabilityAndVacationKey)
  }
}
