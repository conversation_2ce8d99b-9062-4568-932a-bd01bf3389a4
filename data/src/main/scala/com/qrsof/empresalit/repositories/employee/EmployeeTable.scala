package com.qrsof.empresalit.repositories.employee

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.ProvenShape

import java.sql.Timestamp
import java.util.Date

class EmployeeTable(tag: Tag) extends Table[EmployeeEntity](tag, Some("empresalit"), "employee") {
  implicit val dateColumType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def employeeKey: Rep[String] = column[String]("employee_key", O.PrimaryKey);

  def companyKey: Rep[String] = column[String]("company_key");

  def employeeNumber: Rep[String] = column[String]("employee_number")

  def name: Rep[String] = column[String]("name");
  def lastname: Rep[String] = column[String]("lastname");
  def motherLastname: Rep[String] = column[String]("mother_lastname");
  def email: Rep[String] = column[String]("email");
  def fullname: Rep[String] = column[String]("fullname");
	

  def entryDate = column[Date]("entry_date");
  def birthDate = column[Date]("birth_date");

  def areaKey: Rep[String] = column[String]("area_key");
	
  def jobKey: Rep[String] = column[String]("job_key");

  def amount: Rep[BigDecimal] = column[BigDecimal]("amount", O.SqlType("decimal(10, 2"));
  def accountNumber: Rep[String] = column[String]("account_number");

  def bonus: Rep[BigDecimal] = column[BigDecimal]("bonus", O.SqlType("decimal(10, 2"));

  def insertedAt = column[Date]("inserted_at");

  def modifiedAt = column[Date]("modified_at");
  def accessUserKey = column[String]("access_user_key");
	
  override def * : ProvenShape[EmployeeEntity] =
    (employeeKey, companyKey, employeeNumber, fullname, name, lastname, motherLastname, email.?, entryDate, birthDate,areaKey.?,  jobKey, accountNumber.?, amount, bonus, insertedAt, modifiedAt, accessUserKey.?)
      .<>(EmployeeEntity.apply, EmployeeEntity.unapply)
}

object EmployeeTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = EmployeeEntity
  override type EntityTable = EmployeeTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.employeeKey

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.employeeKey
    } { (entity, id) =>
      entity.copy(employeeKey = id)
    }
  }
}
