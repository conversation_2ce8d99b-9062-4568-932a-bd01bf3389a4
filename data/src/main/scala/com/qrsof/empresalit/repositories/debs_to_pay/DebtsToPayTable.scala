package com.qrsof.empresalit.repositories.debs_to_pay

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.DebtToPayStatus
import slick.jdbc.JdbcType
import slick.lifted.ProvenShape

import java.sql.Timestamp
import java.util.Date

class DebtsToPayTable(tag: Tag) extends Table[DebtsToPayEntity](tag, Some("empresalit"), "debts_to_pay") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  implicit val debtToPayStatusType: JdbcType[DebtToPayStatus] = MappedColumnType.base[DebtToPayStatus, String](
    en => en.entryName, s => DebtToPayStatus.withName(s))

  def debt_to_pay_key: Rep[String] = column[String]("debt_to_pay_key", O.PrimaryKey);

  def amount: Rep[BigDecimal] = column[BigDecimal]("amount", O.SqlType("decimal(10, 4)"));

  def company_key: Rep[String] = column[String]("company_key");

  def supplier_key: Rep[String] = column[String]("supplier_key");

  def modifiedAt = column[Date]("modified_at");

  def insertedAt = column[Date]("inserted_at");

  def debt_paid_status: Rep[DebtToPayStatus] = column[DebtToPayStatus]("debt_paid_status");

  def file_pdf: Rep[String] = column[String]("file_pdf");

  override def * : ProvenShape[DebtsToPayEntity] =
    (debt_to_pay_key, amount, company_key, supplier_key, modifiedAt, insertedAt, debt_paid_status, file_pdf)
      .<>(DebtsToPayEntity.apply, DebtsToPayEntity.unapply)
}

object DebtsToPayTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = DebtsToPayEntity
  override type EntityTable = DebtsToPayTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.debt_to_pay_key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.debt_to_pay_key
    } { (entity, id) =>
      entity.copy(debt_to_pay_key = id)
    }
  }
}
