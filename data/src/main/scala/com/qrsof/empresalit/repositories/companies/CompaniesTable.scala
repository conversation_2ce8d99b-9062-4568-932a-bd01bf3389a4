package com.qrsof.empresalit.repositories.companies

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class CompaniesTable(tag: Tag) extends Table[CompaniesEntity](tag, Some("empresalit"), "companies") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def key: Rep[String] = column[String]("key", O.<PERSON>ey)

  def name: Rep[String] = column[String]("name")

  def rfc: Rep[String] = column[String]("rfc")

  def regimen_fiscal_clave = column[String]("regimen_fiscal_clave")

  def modifiedAt = column[Date]("modified_at")

  def insertedAt = column[Date]("inserted_at")

  def address_key: Rep[String] = column[String]("address_key")

  override def * : ProvenShape[CompaniesEntity] =
    (key, name, rfc, regimen_fiscal_clave, modifiedAt, insertedAt, address_key)
      .<>(CompaniesEntity.apply, CompaniesEntity.unapply)
}

object CompaniesTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = CompaniesEntity
  override type EntityTable = CompaniesTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.key
    } { (entity, id) =>
      entity.copy(key = id)
    }
  }

}
