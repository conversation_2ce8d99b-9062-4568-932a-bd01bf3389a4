package com.qrsof.empresalit.repositories.stores

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class StoresRepositoryImpl @Inject()()(
  implicit queryExecutor: QueryExecutor,
  executionContext: ExecutionContext
) extends StoresRepository {

  override def save(storesEntity: StoresEntity): StoresEntity = {
    queryExecutor.syncExecuteQuery {
      StoresTable.save(storesEntity)
    }
  }

  override def getAllStore(): Seq[StoresEntity] = {
    queryExecutor.syncExecuteQuery {
      StoresTable.list
    }
  }

  override def findStore(storeKey: String): Option[StoresEntity] = {
    queryExecutor.syncExecuteQuery {
      StoresTable.findOptionByProperty(_.store_key === storeKey)
    }
  }

  override def updateStore(storesEntity: StoresEntity): StoresEntity = {
    queryExecutor.syncExecuteQuery {
      StoresTable.saveOrUpdate(storesEntity)
    }
  }

  override def deleteStore(store_key: String): Unit = {
    queryExecutor.syncExecuteQuery {
      StoresTable.deleteByProperty(_.store_key === store_key)
    }
  }
}
