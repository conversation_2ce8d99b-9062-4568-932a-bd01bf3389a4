package com.qrsof.empresalit.repositories.jobs

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.ProvenShape

import java.sql.Timestamp
import java.util.Date

class JobsTable(tag: Tag) extends Table[JobsEntity](tag, Some("empresalit"), "jobs") {
  implicit val dateColumType:JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def job_key: Rep[String] = column[String]("job_key", O.<PERSON>ey);

  def company_key: Rep[String] = column[String]("company_key");

  def name: Rep[String] = column[String]("name");

  def inserted_at = column[Date]("inserted_at");

  def modified_at = column[Date]("modified_at");

  override def * : ProvenShape[JobsEntity] =
    (job_key, company_key, name, inserted_at, modified_at)
      .<>(JobsEntity.apply, JobsEntity.unapply)
}

object JobsTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = JobsEntity
  override type EntityTable = JobsTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.job_key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.job_key
    } { (entity, id) =>
      entity.copy(job_key = id)
    }
  }
}
