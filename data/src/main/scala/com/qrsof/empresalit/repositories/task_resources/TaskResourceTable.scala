package com.qrsof.empresalit.repositories.task_resources

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class TaskResourceTable(tag: Tag) extends Table[TaskResourceEntity](tag, Some("empresalit"), "task_resources") {
	implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
		d => new Timestamp(d.getTime),
		ts => new Date(ts.getTime)
	)

	def taskResourceKey: Rep[String] = column[String]("task_resource_key", O<PERSON>ey)

	def taskKey: Rep[String] = column[String]("task_key")

	def resourceKey = column[String]("resource_key")

	
	override def * : ProvenShape[TaskResourceEntity] = (
	 taskResourceKey,
	 taskKey,
	 resourceKey

	).<>(TaskResourceEntity.apply,TaskResourceEntity.unapply)
}

object TaskResourceTable extends GenericDao {
	override type Id = String

	override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

	override type Entity = TaskResourceEntity
	override type EntityTable = TaskResourceTable

	override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

	override def $id(table: EntityTable): Rep[Id] = table.taskResourceKey

	override def idLens: Lens[Entity, Id] = {
		Lens.lens { (entity: Entity) =>
			entity.taskResourceKey
		} { (entity, id) =>
			entity.copy(taskResourceKey = id)
		}
	}
}
