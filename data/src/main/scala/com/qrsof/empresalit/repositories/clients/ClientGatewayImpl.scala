package com.qrsof.empresalit.repositories.clients

import com.qrsof.empresalit.domain.client.{ClientDto, ClientGateway}
import jakarta.inject.Inject

class ClientGatewayImpl @Inject(clientsRepository: ClientsRepository) extends ClientGateway{

	override def getClientByKey(clientKey: String): Option[ClientDto] = {
		clientsRepository.getByKey(clientKey).map(item => ClientDto(clientKey = item.client_key, name = item.name, regimenFiscalClave = item.regimen_fiscal_clave, rfc = item.rfc, email = item.email, companyKey = item.company_key, addressKey = item.address_key, modifiedAt = item.modifiedAt, insertedAt = item.insertedAt))
	}
}
