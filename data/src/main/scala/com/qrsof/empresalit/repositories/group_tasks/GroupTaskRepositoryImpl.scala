package com.qrsof.empresalit.repositories.group_tasks

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.clients.ClientsTable
import com.qrsof.empresalit.repositories.group_task_resources.task_resources.{GroupTaskResourceEntity, GroupTaskResourceTable}
import com.qrsof.empresalit.repositories.task_resources.{TaskResourceEntity, TaskResourceTable}
import jakarta.inject.{Inject, Singleton}
import com.qrsof.empresalit.repositories.tasks.{TaskEntity, TaskTable}
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.pojos.{ReceptionOfEquipment, ReceptionOfEquipmentFilter}

import scala.collection.mutable
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext}

@Singleton
class GroupTaskRepositoryImpl @Inject()()(
 implicit queryExecutor: QueryExecutor,
 executionContext: ExecutionContext
) extends GroupTaskRepository {

	override def newEquipmentGroupRepository(groupTask: GroupTaskEntity, groupTasksResources: Seq[GroupTaskResourceEntity], tasks: Seq[TaskEntity], taskResources: Seq[TaskResourceEntity]): GroupTaskEntity = {
		val transactions = mutable.ListBuffer.empty[DBIO[Any]]
		transactions.+=(
			GroupTaskTable
			 .save(groupTask)
		)

		groupTasksResources.foreach { groupTasksResource =>
			transactions.+=(GroupTaskResourceTable.save(groupTasksResource))
		}

		tasks.foreach { task =>
			transactions.+=(TaskTable.save(task))
		}

		taskResources.foreach { taskResource =>
			transactions.+=(TaskResourceTable.save(taskResource))
		}


		val transactionsSeq: Seq[DBIOAction[_, NoStream, _]] = transactions.toSeq
		Await.result(
			queryExecutor.database.run(
				DBIO
				 .seq(transactionsSeq: _*)
				 .transactionally
			),
			Duration.Inf
		)
		groupTask
	}

	override def getTotalReceptionOfEquipment(companyKey: String): Int = queryExecutor.syncExecuteQuery {
		(for {
			groupTaskTable <- GroupTaskTable.tableQuery.filter(_.companyKey === companyKey)
		} yield (groupTaskTable)).length.result
	}

	override def getReceptionOfEquipment(companyKey: String, receptionOfEquipmentFilter: ReceptionOfEquipmentFilter, pageNumber: Int, pageSize: Int): Seq[ReceptionOfEquipment] = queryExecutor.syncExecuteQuery {
		(for {
			groupTaskTable <- GroupTaskTable.tableQuery.filter(_.companyKey === companyKey)
			clients <- ClientsTable.tableQuery.filter(_.company_key === companyKey)
			if groupTaskTable.clientKey === clients.client_key
		} yield (groupTaskTable, clients))
		.filterIf(receptionOfEquipmentFilter.text.isDefined)(data => (data._1.folio.toLowerCase like receptionOfEquipmentFilter.text.get) || (data._2.name.toLowerCase like receptionOfEquipmentFilter.text.get))
		 .drop(pageSize * pageNumber)
		 .take(pageSize).result.map(item => {item.map(data => {ReceptionOfEquipment(data._1.groupTaskKey, data._1.folio, data._1.entryDate, data._2.name, 0, "")})})
	}
}
