package com.qrsof.empresalit.repositories.company_certificates

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import com.qrsof.empresalit.repositories.company_certificates
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class CompanyCertificatesTable(tag: Tag) extends Table[CompanyCertificatesEntity](tag, Some("empresalit"), "company_certificates") {
  implicit val dateColumnType:JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def key: Rep[String] = column[String]("key", O.<PERSON>)

  def company_key: Rep[String] = column[String]("company_key")

  def no_certificado: Rep[String] = column[String]("no_certificado")

  def private_key: Rep[String] = column[String]("private_key")

  def public_key: Rep[String] = column[String]("public_key")

  def password: Rep[String] = column[String]("password")

  def modifiedAt = column[Date]("modified_at")

  def insertedAt = column[Date]("inserted_at")

  override def * : ProvenShape[CompanyCertificatesEntity] =
    (key, company_key, no_certificado, private_key, public_key, password, modifiedAt, insertedAt)
      .<>(CompanyCertificatesEntity.apply, CompanyCertificatesEntity.unapply)
}

object CompanyCertificatesTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = CompanyCertificatesEntity
  override type EntityTable = CompanyCertificatesTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.key
    } { (entity, id) =>
      entity.copy(key = id)
    }
  }
}
