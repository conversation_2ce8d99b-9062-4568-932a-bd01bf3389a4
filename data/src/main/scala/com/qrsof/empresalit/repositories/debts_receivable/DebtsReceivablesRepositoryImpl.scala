package com.qrsof.empresalit.repositories.debts_receivable

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class DebtsReceivablesRepositoryImpl @Inject()()(
  implicit queryExecutor: QueryExecutor,
  executionContext: ExecutionContext
) extends DebtsReceivablesRepository {

  override def newDebtReceivable(debtsReceivableEntity: DebtsReceivablesEntity): DebtsReceivablesTable.Entity = {
    queryExecutor.syncExecuteQuery {
      DebtsReceivablesTable.save(debtsReceivableEntity)
    }
  }

  override def getAllDebts(): Seq[DebtsReceivablesEntity] = {
    queryExecutor.syncExecuteQuery {
      DebtsReceivablesTable.list
    }
  }

  override def deleteDebt(debt_key: String): Unit = {
    queryExecutor.syncExecuteQuery {
      DebtsReceivablesTable.deleteByProperty(_.debt_receivable_key === debt_key)
    }
  }

  override def findDebt(key: String): Option[DebtsReceivablesEntity] = {
    queryExecutor.syncExecuteQuery {
      DebtsReceivablesTable.findOptionByProperty(_.debt_receivable_key === key)
    }
  }

  override def markAsPaid(debtsReceivablesEntity: DebtsReceivablesEntity): Unit = {
    queryExecutor.syncExecuteQuery {
      DebtsReceivablesTable.saveOrUpdate(debtsReceivablesEntity)
    }
  }
}
