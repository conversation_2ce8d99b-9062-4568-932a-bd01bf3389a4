package com.qrsof.empresalit.repositories.logbook

import com.google.inject.{Inject, Singleton}
import com.qrsof.empresalit.domain.logbook.LogbookGateway
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.NewLogbookDataRequest
import org.slf4j.{Logger, LoggerFactory}
@Singleton
class LogbookGatewayImpl @Inject() (logbookRepository: LogbookRepository) extends LogbookGateway {

  val logger: Logger = LoggerFactory.getLogger(classOf[LogbookGatewayImpl])
  override def createNewLogbookForTask(logbookDataRequest: NewLogbookDataRequest): Unit = {
    val logbookEntity: LogbookEntity = LogbookEntity(
      logbookKey = logbookDataRequest.logbookKey,
      author = logbookDataRequest.author,
      taskKey = logbookDataRequest.taskKey,
      logbookType = logbookDataRequest.logbookType,
      payload = logbookDataRequest.payload,
      keyAttachments = logbookDataRequest.keyAttachments,
      createdAt = logbookDataRequest.createdAt,
      updateAt = logbookDataRequest.updateAt
    )

    logbookRepository.newLogbookWithResources(logbook = logbookEntity)

  }

  override def createLogbookForNewInputs(createLogbookDataRequest: Seq[NewLogbookDataRequest]): Unit = {

    val logbooksEntity = createLogbookDataRequest.map(logbookEntity => {

      LogbookEntity(
        logbookKey = logbookEntity.logbookKey,
        author = logbookEntity.author,
        taskKey = logbookEntity.taskKey,
        logbookType = logbookEntity.logbookType,
        payload = logbookEntity.payload,
        keyAttachments = logbookEntity.keyAttachments,
        createdAt = logbookEntity.createdAt,
        updateAt = logbookEntity.updateAt
      )
    })
    logger.info(s"LogbookGatewayImpl::createLogbookForNewInputs::logbooksEntity->{}", logbooksEntity)
    logbookRepository.createNewLogbookForNewInputs(logbooksEntity)
  }
}
