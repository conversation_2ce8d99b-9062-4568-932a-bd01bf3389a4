package com.qrsof.empresalit.repositories.resources

trait ResourcesRepository {
	def saveResource(resourceEntity: ResourceEntity): Unit

	def getResource(resourceKey: String): Option[ResourceEntity]

	def getResourceByCompanyKeyAndResourceKey(companyKey: String, resourceKey: String): Option[ResourceEntity]

	def getResourcesByKeys(resourceKeys: Seq[String]): Seq[ResourceEntity]

	def deleteResourcesByKeys(resourceKeys: Seq[String]): Unit
}
