package com.qrsof.empresalit.repositories.logbook

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class LogbookTable(tag: Tag) extends Table[LogbookEntity](tag, Some("empresalit"), "logbook") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def logbookKey: Rep[String] = column[String]("logbook_key", O.PrimaryKey)

  def author = column[String]("autor")

  def taskKey: Rep[String] = column[String]("task_key")

  def logbookType = column[String]("logbook_type")

  def payload = column[String]("payload")

  def keyAttachments = column[String]("key_attachments")

  def createdAt = column[Date]("created_at")

  def updatedAt = column[Date]("updated_at")

  override def * : ProvenShape[LogbookEntity] = (logbookKey, author, taskKey, logbookType, payload, keyAttachments, createdAt, updatedAt).mapTo[LogbookEntity]

}

object LogbookTable extends GenericDao {

  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = LogbookEntity
  override type EntityTable = LogbookTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.logbookKey

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.logbookKey

    } { (entity, id) =>
      entity.copy(logbookKey = id)
    }
  }
}
