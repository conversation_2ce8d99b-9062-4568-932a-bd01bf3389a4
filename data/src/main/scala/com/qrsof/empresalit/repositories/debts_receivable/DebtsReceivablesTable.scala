package com.qrsof.empresalit.repositories.debts_receivable

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.PaymentStatus
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class DebtsReceivablesTable(tag: Tag) extends Table[DebtsReceivablesEntity](tag, Some("empresalit"), "debts_receivables") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  implicit val paymentStatusType: JdbcType[PaymentStatus] = MappedColumnType.base[PaymentStatus, String](
      en => en.entryName, 
      s => PaymentStatus.withName(s)
  )

  def debt_receivable_key: Rep[String] = column[String]("debt_receivable_key", O.PrimaryKey);

  def amount: Rep[BigDecimal] = column[BigDecimal]("amount", O.SqlType("decimal(10, 4)"))

  def company_key: Rep[String] = column[String]("company_key")

  def client_key: Rep[String] = column[String]("client_key")

  def modifiedAt = column[Date]("modified_at")

  def insertedAt = column[Date]("inserted_at")

  def paymentStatus: Rep[PaymentStatus] = column[PaymentStatus]("payment_status")

  override def * : ProvenShape[DebtsReceivablesEntity] = (
    debt_receivable_key, amount, company_key, client_key, modifiedAt, insertedAt, paymentStatus
  ).<>(DebtsReceivablesEntity.apply, DebtsReceivablesEntity.unapply)
}

object DebtsReceivablesTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = DebtsReceivablesEntity
  override type EntityTable = DebtsReceivablesTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.debt_receivable_key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.debt_receivable_key
    } { (entity, id) =>
      entity.copy(debt_receivable_key = id)
    }
  }
}
