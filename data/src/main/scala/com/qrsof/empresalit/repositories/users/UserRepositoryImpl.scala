package com.qrsof.empresalit.repositories.users

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class UserRepositoryImpl @Inject()()(
 implicit queryExecutor: QueryExecutor,
 executionContext: ExecutionContext
) extends UserRepository {

	override def save(userEntity: UserEntity): UserEntity = {
		queryExecutor.syncExecuteQuery {
			UserTable.save(userEntity)
		}
	}

	override def getUserByKey(userKey: String): Option[UserEntity] = queryExecutor.syncExecuteQuery { UserTable.findOptionByProperty(_.key === userKey) }
}
