package com.qrsof.empresalit.repositories.employee

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class EmployeeRepositoryImpl @Inject()()(
 implicit queryExecutor: QueryExecutor,
 executionContext: ExecutionContext
) extends EmployeeRepository {
	override def save(employeeEntity: EmployeeEntity): EmployeeEntity = {
		queryExecutor.syncExecuteQuery {
			EmployeeTable.save(employeeEntity)
		}
	}

	override def getAllEmployee(): Seq[EmployeeEntity] = {
		queryExecutor.syncExecuteQuery {
			EmployeeTable.list
		}
	}

	override def findEmployee(employeeKey: String): Option[EmployeeEntity] = {
		queryExecutor.syncExecuteQuery {
			EmployeeTable.findOptionByProperty(_.employeeKey === employeeKey)
		}
	}

	override def updateEmployee(employeeEntity: EmployeeEntity): EmployeeEntity = {
		queryExecutor.syncExecuteQuery {
			EmployeeTable.tableQuery.filter(_.employeeKey === employeeEntity.employeeKey).forUpdate.result.head.flatMap(employee => {
				EmployeeTable.saveOrUpdate(employee.copy(employeeNumber = employeeEntity.employeeNumber, fullname = employeeEntity.fullname, name = employeeEntity.name, lastname = employeeEntity.lastname, motherLastname = employeeEntity.motherLastname, entryDate = employeeEntity.entryDate, birthDate = employeeEntity.birthDate, jobKey = employeeEntity.jobKey, areaKey = employeeEntity.areaKey, accountNumber = employeeEntity.accountNumber, amount = employeeEntity.amount, bonus = employeeEntity.bonus, modifiedAt = employeeEntity.modifiedAt))
			})
		}
	}

	override def deleteEmployee(employeeKey: String): Unit = {
		queryExecutor.syncExecuteQuery {
			EmployeeTable.deleteByProperty(_.employeeKey === employeeKey)
		}
	}

	override def putAccessUserKey(employeeKey: String, accessUserKey: String): Unit = queryExecutor.syncExecuteQuery {
		EmployeeTable.tableQuery.filter(_.employeeKey === employeeKey).forUpdate.result.head.flatMap(employee => {
			EmployeeTable.saveOrUpdate(employee.copy(accessUserKey = Some(accessUserKey) ))
		})
	}

	override def findEmployeeByAccessUserKey(accessUserKey: String): Option[EmployeeEntity] = queryExecutor.syncExecuteQuery {
		EmployeeTable.findOptionByProperty(_.accessUserKey === accessUserKey)
	}
}
