package com.qrsof.empresalit.repositories.companies

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext
@Singleton
class CompaniesRepositoryImp @Inject() ()(implicit
    queryExecutor: QueryExecutor,
    executionContext: ExecutionContext
) extends CompaniesRepository {
  val logger: Logger = LoggerFactory.getLogger(classOf[CompaniesRepositoryImp])

  override def save(companiesEntity: CompaniesEntity): CompaniesEntity = {
    queryExecutor.syncExecuteQuery {
      CompaniesTable.save(companiesEntity)
    }
  }

  override def getCompany(companyKey: String): Seq[CompaniesEntity] = {
    queryExecutor.syncExecuteQuery {
      CompaniesTable.findListByProperty(_.key === companyKey)
    }
  }

  override def updateCompanyData(companyEntity: CompaniesEntity): CompaniesEntity = {
    queryExecutor.syncExecuteQuery {
      CompaniesTable.tableQuery
        .filter(_.key === companyEntity.key)
        .forUpdate
        .result
        .head
        .flatMap(company => {
          CompaniesTable
            .saveOrUpdate(company.copy(name = companyEntity.name, rfc = companyEntity.rfc, regimen_fiscal_clave = companyEntity.regimen_fiscal_clave, modifiedAt = companyEntity.modifiedAt))
        })
    }
  }
}
