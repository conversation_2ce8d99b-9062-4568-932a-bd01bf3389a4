package com.qrsof.empresalit.repositories.brand_company

import com.google.inject.{Inject, Singleton}
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor

import scala.concurrent.ExecutionContext

@Singleton
class BrandCompanyRepositoryImpl @Inject() (implicit queryExecutor: QueryExecutor, executionContext: ExecutionContext) extends BrandCompanyRepository {
  override def saveBrandCompany(brandCompanyEntity: BrandCompanyEntity): BrandCompanyEntity = {
    queryExecutor.syncExecuteQuery(
      BrandCompanyTable.save(brandCompanyEntity)
    )
  }

  override def getBrandCompany(companyKey: String): Option[BrandCompanyEntity] = {
    queryExecutor.syncExecuteQuery(
      BrandCompanyTable.findOptionByProperty(_.companyKey === companyKey)
    )
  }

  override def updateBrandCompany(brandCompanyEntity: BrandCompanyEntity): BrandCompanyEntity = {
    queryExecutor.syncExecuteQuery(
      BrandCompanyTable.tableQuery
        .filter(_.brandCompanyKey === brandCompanyEntity.brandCompanyKey)
        .forUpdate
        .result
        .head
        .flatMap(brandCompany => {
          BrandCompanyTable.saveOrUpdate(
            brandCompany.copy(
              colorPalette = brandCompanyEntity.colorPalette,
              color = brandCompanyEntity.color,
              logoResourceKey = brandCompanyEntity.logoResourceKey,
              modifiedAt = brandCompanyEntity.modifiedAt
            )
          )
        })
    )
  }
}
