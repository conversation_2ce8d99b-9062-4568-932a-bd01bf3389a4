package com.qrsof.empresalit.repositories.holidays

import com.google.inject.{Inject, Singleton}
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor

import scala.concurrent.ExecutionContext

@Singleton
class HolidaysRepositoryImpl @Inject() (implicit queryExecutor: QueryExecutor, executionContext: ExecutionContext) extends HolidaysRepository {
  override def getHolidays: Seq[HolidaysEntity] = {
    queryExecutor.syncExecuteQuery {
      HolidaysTable.list
    }
  }

  override def getHolidaysFiltered(startDate: String, endDate: String): Seq[HolidaysEntity] = {
    queryExecutor.syncExecuteQuery {
      HolidaysTable.tableQuery.filter(data => data.date >= startDate && data.date <= endDate).result
    }
  }

}
