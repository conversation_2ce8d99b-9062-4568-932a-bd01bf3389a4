package com.qrsof.empresalit.repositories.clients


import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class ClientsTable(tag: Tag) extends Table[ClientsEntity](tag, Some("empresalit"), "clients") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def client_key: Rep[String] = column[String]("client_key", O.<PERSON>ey)

  def name: Rep[String] = column[String]("name")

  def regimen_fiscal_clave = column[String]("regimen_fiscal_clave")

  def rfc = column[String]("rfc")

  def email = column[String]("email")

  def company_key: Rep[String] = column[String]("company_key")

  def address_key: Rep[String] = column[String]("address_key")

  def modifiedAt = column[Date]("modified_at")

  def insertedAt = column[Date]("inserted_at")

  override def * : ProvenShape[ClientsEntity] = (
    client_key,
    name,
    regimen_fiscal_clave,
    rfc,
    email.?,
    company_key,
    address_key,
    modifiedAt,
    insertedAt
  ).<>(ClientsEntity.apply, ClientsEntity.unapply)
}

object ClientsTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = ClientsEntity
  override type EntityTable = ClientsTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.client_key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.client_key
    } { (entity, id) =>
      entity.copy(client_key = id)
    }
  }
}
