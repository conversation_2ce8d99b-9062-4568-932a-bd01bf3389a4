package com.qrsof.empresalit.repositories.brand_company

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens}
import com.qrsof.empresalit.repositories.companies.CompaniesTable
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class BrandCompanyTable(tag: Tag) extends Table[BrandCompanyEntity](tag, Some("empresalit"), "brand_company") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def companyKey: Rep[String] = column[String]("company_key")

  def brandCompanyKey: Rep[String] = column[String]("brand_company_key", <PERSON><PERSON>, O.Unique)

  def colorPalette: Rep[List[String]] = column[List[String]]("color_palette")

  def color: Rep[Option[String]] = column[Option[String]]("color")
  
  def logoResourceKey: Rep[Option[String]] = column[Option[String]]("logo_resource_key")

  def createdAt = column[Date]("created_at")

  def modifiedAt = column[Date]("modified_at")

  def * : ProvenShape[BrandCompanyEntity] = (companyKey, brandCompanyKey, colorPalette, color, logoResourceKey, createdAt, modifiedAt).mapTo[BrandCompanyEntity]

  def indexBrandCompany = index("indexBrandCompany", (companyKey, brandCompanyKey), unique = true)
  def companyKeyFK = foreignKey("companyKeyFK", companyKey, CompaniesTable.tableQuery)(_.key, onUpdate = ForeignKeyAction.Cascade, onDelete = ForeignKeyAction.Cascade)
}

object BrandCompanyTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = BrandCompanyEntity
  override type EntityTable = BrandCompanyTable

  override def tableQuery: TableQuery[EntityTable] = TableQuery[EntityTable]
  override def $id(table: EntityTable): Rep[Id] = table.brandCompanyKey

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.brandCompanyKey
    } { (entity, id) =>
      entity.copy(brandCompanyKey = id)
    }
  }
}
