package com.qrsof.empresalit.repositories.quotations

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class QuoteRepositoryImpl @Inject()()(
  implicit queryExecutor: QueryExecutor,
  executionContext: ExecutionContext
) extends QuoteRepository {

  override def saveQuote(quoteEntity: QuoteEntity): QuoteEntity = {
    queryExecutor.syncExecuteQuery {
      QuoteTable.save(quoteEntity)
    }
  }

  override def getQuote(): Seq[QuoteEntity] = {
    queryExecutor.syncExecuteQuery {
      QuoteTable.list
    }
  }

  override def getAllQuote(): Seq[QuoteEntity] = {
    queryExecutor.syncExecuteQuery {
      QuoteTable.list
    }
  }

  override def findQuote(quote_key: String): Option[QuoteEntity] = {
    queryExecutor.syncExecuteQuery {
      QuoteTable.findOptionByProperty(_.quote_key === quote_key)
    }
  }

  override def updateQuote(quoteEntity: QuoteEntity): QuoteEntity = {
    queryExecutor.syncExecuteQuery {
      QuoteTable.saveOrUpdate(quoteEntity)
    }
  }

  override def deleteQuote(quote_key: String): Unit = {
    queryExecutor.syncExecuteQuery {
      QuoteTable.deleteByProperty(_.quote_key === quote_key)
    }
  }

  override def markAsFacture(quoteEntity: QuoteEntity): Unit = {
    queryExecutor.syncExecuteQuery {
      QuoteTable.saveOrUpdate(quoteEntity)
    }
  }

  override def markAsReject(quoteEntity: QuoteEntity): Unit = {
    queryExecutor.syncExecuteQuery {
      QuoteTable.saveOrUpdate(quoteEntity)
    }
  }

  override def markAsPending(quoteEntity: QuoteEntity): Unit = {
    queryExecutor.syncExecuteQuery {
      QuoteTable.saveOrUpdate(quoteEntity)
    }
  }
}
