package com.qrsof.empresalit.repositories.quotations

trait QuoteRepository {
  def saveQuote(quoteEntity: QuoteEntity): QuoteEntity

  def getAllQuote(): Seq[QuoteEntity]

  def getQuote(): Seq[QuoteEntity]

  def findQuote(quote_key: String): Option[QuoteEntity]

  def updateQuote(quoteEntity: QuoteEntity): QuoteEntity

  def deleteQuote(quote_key: String): Unit

  def markAsFacture(quoteEntity: QuoteEntity): Unit

  def markAsReject(quoteEntity: QuoteEntity): Unit

  def markAsPending(quoteEntity: QuoteEntity): Unit
}
