package com.qrsof.empresalit.repositories.company_certificates

import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class CompanyCertificatesRepositoryImpl @Inject()()(
  implicit queryExecutor: QueryExecutor,
  executionContext: ExecutionContext
) extends CompanyCertificatesRepository {

  override def save(companyCertificatesEntity: CompanyCertificatesEntity): CompanyCertificatesEntity = {
    queryExecutor.syncExecuteQuery {
      CompanyCertificatesTable.save(companyCertificatesEntity)
    }
  }

  override def getCompanyCertificates(): Seq[CompanyCertificatesEntity] = {
    queryExecutor.syncExecuteQuery {
      CompanyCertificatesTable.list
    }
  }
}
