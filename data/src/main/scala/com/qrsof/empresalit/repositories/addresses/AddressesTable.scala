package com.qrsof.empresalit.repositories.addresses

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import com.qrsof.empresalit.repositories.addresses
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class AddressesTable(tag: Tag) extends Table[AddressesEntity](tag, Some("empresalit"), "addresses") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def address_key: Rep[String] = column[String]("address_key", O.<PERSON>ey)

  def calle: Rep[String] = column[String]("calle")

  def estado: Rep[String] = column[String]("estado")

  def municipio: Rep[String] = column[String]("municipio")

  def codigo_postal: Rep[String] = column[String]("codigo_postal")

  def numero_exterior: Rep[String] = column[String]("numero_exterior")

  def numero_interior: Rep[String] = column[String]("numero_interior")

  def modifiedAt = column[Date]("modified_at")

  def insertedAt = column[Date]("inserted_at")

  override def * : ProvenShape[AddressesEntity] =
    (address_key, calle, estado, municipio, codigo_postal, numero_exterior, numero_interior, modifiedAt, insertedAt)
      .<>(AddressesEntity.apply, AddressesEntity.unapply)
}

object AddressesTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = AddressesEntity
  override type EntityTable = AddressesTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.address_key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.address_key
    } { (entity, id) =>
      entity.copy(address_key = id)
    }
  }
}
