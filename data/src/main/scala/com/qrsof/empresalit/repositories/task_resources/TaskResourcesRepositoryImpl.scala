package com.qrsof.empresalit.repositories.task_resources

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.resources.{ResourceEntity, ResourceTable}
import jakarta.inject.{Inject, Singleton}

import scala.collection.mutable
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext}

@Singleton
class TaskResourcesRepositoryImpl @Inject() ()(implicit
    queryExecutor: QueryExecutor,
    executionContext: ExecutionContext
) extends TaskResourcesRepository {

  override def getResourcesByTaskKey(taskKey: String): Seq[ResourceEntity] = queryExecutor.syncExecuteQuery {
    (for {
      taskResourceTable <- TaskResourceTable.tableQuery.filter(_.taskKey === taskKey)
      resourceTable <- ResourceTable.tableQuery.filter(_.resourceKey === taskResourceTable.resourceKey)
    } yield resourceTable).result
  }

  override def getResourceByResourceKey(resourceKey: String): Option[ResourceEntity] = {
    queryExecutor.syncExecuteQuery {
      ResourceTable.findOptionByProperty(_.resourceKey === resourceKey)
    }
  }

  override def saveResources(resources: Seq[TaskResourceEntity]): Unit = {
    var transactions = mutable.ListBuffer.empty[DBIO[Any]]
    resources.foreach(value => transactions.+=(TaskResourceTable.save(value)))
    val querySeq = transactions.toSeq
    Await.result(
      queryExecutor.database.run(
        DBIO
          .seq(querySeq: _*)
          .transactionally
      ),
      Duration.Inf
    )
  }
}
