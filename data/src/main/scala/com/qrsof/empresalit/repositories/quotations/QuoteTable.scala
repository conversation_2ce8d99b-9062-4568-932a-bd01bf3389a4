package com.qrsof.empresalit.repositories.quotations

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{<PERSON><PERSON><PERSON><PERSON>, Lens}
import com.qrsof.empresalit.views.maincontainer.quote.actions.QuoteStatus
import slick.jdbc.JdbcType
import slick.lifted.ProvenShape

import java.sql.Timestamp
import java.util.Date

class QuoteTable(tag: Tag) extends Table[QuoteEntity](tag, Some("empresalit"), "quote") {
  implicit val dateColumType:JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  implicit val quoteStatusType: JdbcType[QuoteStatus] & JdbcType[QuoteStatus] =
    MappedColumnType.base[QuoteStatus, String](en => en.toString, s => QuoteStatus.withName(s))

  def quote_key: Rep[String] = column[String]("quote_key", <PERSON><PERSON>ey);

  def store_key: Rep[String] = column[String]("store_key");

  def client_key: Rep[String] = column[String]("client_key");

  def folio: Rep[String] = column[String]("folio");

  def date_emission = column[Date]("date_emission");

  def valid_offer: Rep[String] = column[String]("valid_offer");

  def email: Rep[String] = column[String]("email");

  def condition: Rep[String] = column[String]("condition");

  def extra_information: Rep[String] = column[String]("extra_information");

  def time_send: Rep[String] = column[String]("time_send");

  def keys_products = column[List[String]]("keys_products");

  def file_quote: Rep[String] = column[String]("file_quote");

  def total_quote: Rep[BigDecimal] = column[BigDecimal]("total_quote");

  def status: Rep[QuoteStatus] = column[QuoteStatus]("status");

  def inserted_at = column[Date]("inserted_at");

  def modified_at = column[Date]("modified_at");

  override def * : ProvenShape[QuoteEntity] =
    (quote_key, store_key, client_key, folio, date_emission, valid_offer, email, condition, extra_information, time_send, 
      keys_products, file_quote, total_quote, status, inserted_at, modified_at).<>(QuoteEntity.apply, QuoteEntity.unapply)
}

object QuoteTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = QuoteEntity
  override type EntityTable = QuoteTable

  override def tableQuery: TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.quote_key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.quote_key
    } { (entity, id) =>
      entity.copy(quote_key = id)
    }
  }
}
