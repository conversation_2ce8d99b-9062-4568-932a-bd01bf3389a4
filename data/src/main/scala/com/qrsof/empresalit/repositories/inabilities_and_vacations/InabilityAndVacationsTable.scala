package com.qrsof.empresalit.repositories.inabilities_and_vacations

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class InabilityAndVacationsTable(tag: Tag) extends Table[InabilityAndVacationEntity](tag, Some("empresalit"), "inabilities_and_vacations") {
	implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
		d => new Timestamp(d.getTime),
		ts => new Date(ts.getTime)
	)

	def inabilityAndVacationKey: Rep[String] = column[String]("inability_and_vacation_key", O<PERSON>)

	def days: Rep[Int] = column[Int]("days")

	def requestType: Rep[String] = column[String]("request_type")

	def startDate: Rep[Date] = column[Date]("start_date")

	def endDate: Rep[Date] = column[Date]("end_date")

	def createdAt: Rep[Date] = column[Date]("created_at")

	def updatedAt = column[Date]("updated_at")

	def employeeKey = column[String]("employee_key")

	def registeredBy = column[String]("registered_by")

	override def * : ProvenShape[InabilityAndVacationEntity] = (
	 inabilityAndVacationKey,
	 days,
	 requestType,
	 startDate,
	 endDate,
	 createdAt,
	 updatedAt,
	 employeeKey,
	 registeredBy.?
	).<>(InabilityAndVacationEntity.apply, InabilityAndVacationEntity.unapply)
}

object InabilityAndVacationsTable extends GenericDao {
	override type Id = String

	override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

	override type Entity = InabilityAndVacationEntity
	override type EntityTable = InabilityAndVacationsTable

	override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

	override def $id(table: EntityTable): Rep[Id] = table.inabilityAndVacationKey

	override def idLens: Lens[Entity, Id] = {
		Lens.lens { (entity: Entity) =>
			entity.inabilityAndVacationKey
		} { (entity, id) =>
			entity.copy(inabilityAndVacationKey = id)
		}
	}
}
