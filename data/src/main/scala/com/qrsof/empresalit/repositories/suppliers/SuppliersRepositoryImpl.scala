package com.qrsof.empresalit.repositories.suppliers

import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class SuppliersRepositoryImpl @Inject()()(
  implicit queryExecutor: QueryExecutor,
  executionContext: ExecutionContext
) extends SuppliersRepository {

  override def save(suppliersEntity: SuppliersEntity): SuppliersEntity = {
    queryExecutor.syncExecuteQuery {
      SuppliersTable.save(suppliersEntity)
    }
  }

  override def getSupplier(): Seq[SuppliersEntity] = {
    queryExecutor.syncExecuteQuery {
      SuppliersTable.list
    }
  }

}
