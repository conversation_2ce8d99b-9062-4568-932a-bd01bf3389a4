package com.qrsof.empresalit.repositories.days_to_request


import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

@Singleton
class DaysToRequestRepositoryImpl @Inject() (implicit
																						 queryExecutor: QueryExecutor) extends DaysToRequestRepository{

	override def getDaysToRequest(): Seq[DaysToRequestEntity] = queryExecutor.syncExecuteQuery {
		DaysToRequestTable.list
	}
}
