package com.qrsof.empresalit.repositories.holidays

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class HolidaysTable(tag: Tag) extends Table[HolidaysEntity](tag, Some("empresalit"), "holidays") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )
  def key: Rep[String] = column[String]("key", O.<PERSON>ey)

  def name: Rep[String] = column[String]("name")

  def date: Rep[String] = column[String]("date")

  def year: Rep[Int] = column[Int]("year")

  def createdAt = column[Date]("created_at")

  def updatedAt = column[Date]("updated_at")

  override def * : ProvenShape[HolidaysEntity] = (key, name, date, year, createdAt, updatedAt).mapTo[HolidaysEntity]
}

object HolidaysTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = HolidaysEntity
  override type EntityTable = HolidaysTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.key
    } { (entity, id) =>
      entity.copy(key = id)
    }
  }
}
