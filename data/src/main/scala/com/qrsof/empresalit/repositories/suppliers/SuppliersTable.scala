package com.qrsof.empresalit.repositories.suppliers

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class SuppliersTable(tag: Tag) extends Table[SuppliersEntity](tag, Some("empresalit"), "suppliers") {
  implicit val dateColumnType:JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def supplier_key: Rep[String] = column[String]("supplier_key", O.<PERSON>ey)

  def name: Rep[String] = column[String]("name")

  def regimen_fiscal_clave = column[String]("regimen_fiscal_clave")

  def company_key: Rep[String] = column[String]("company_key")

  def modifiedAt = column[Date]("modified_at")

  def insertedAt = column[Date]("inserted_at")

  def address_key: Rep[String] = column[String]("address_key")

  def rfc: Rep[String] = column[String]("rfc")

  override def * : ProvenShape[SuppliersEntity] =
    (supplier_key, name, regimen_fiscal_clave, company_key, modifiedAt, insertedAt, address_key, rfc)
      .<>(SuppliersEntity.apply, SuppliersEntity.unapply)
}

object SuppliersTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = SuppliersEntity
  override type EntityTable = SuppliersTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]


  override def $id(table: EntityTable): Rep[Id] = table.supplier_key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.supplier_key
    } { (entity, id) =>
      entity.copy(supplier_key = id)
    }
  }
}
