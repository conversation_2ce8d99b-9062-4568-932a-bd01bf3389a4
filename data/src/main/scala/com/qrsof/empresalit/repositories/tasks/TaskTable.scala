package com.qrsof.empresalit.repositories.tasks

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class TaskTable(tag: Tag) extends Table[TaskEntity](tag, Some("empresalit"), "tasks") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def taskKey: Rep[String] = column[String]("task_key", O.PrimaryKey)
	
  def groupTaskKey: Rep[String] = column[String]("group_task_key")

  def statusKey: Rep[String] = column[String]("status_key")

  def title: Rep[String] = column[String]("title")

  def quantity = column[Int]("quantity")

  def observations = column[String]("observations")

  def entryDate = column[Date]("entry_date")

  def outputDate = column[Date]("output_date")

  def responsible: Rep[String] = column[String]("responsible")

  override def * : ProvenShape[TaskEntity] = (
    taskKey,
    groupTaskKey,
    statusKey.?,
    title,
    quantity,
    observations,
    entryDate,
    outputDate.?,
    responsible
  ).<>(TaskEntity.apply, TaskEntity.unapply)
}

object TaskTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = TaskEntity
  override type EntityTable = TaskTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.taskKey

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.taskKey
    } { (entity, id) =>
      entity.copy(taskKey = id)
    }
  }
}
