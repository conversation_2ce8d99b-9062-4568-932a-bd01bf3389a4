package com.qrsof.empresalit.repositories.kanbanBoard

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.group_tasks.{GroupTaskEntity, GroupTaskTable}
import com.qrsof.empresalit.repositories.status.{StatusEntity, StatusTable}
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class KanbanBoardRepositoryImpl @Inject() ()(implicit
    queryExecutor: QueryExecutor,
    executionContext: ExecutionContext
) extends KanbanBoardRepository {

  override def newStatus(statusEntity: StatusEntity): StatusEntity = {
    queryExecutor.syncExecuteQuery {
      StatusTable.save(statusEntity)
    }
  }

  override def getAllStatusByCompany(companyKey: String): Seq[StatusEntity] = {
    queryExecutor.syncExecuteQuery {
      StatusTable.findListByProperty(_.company_key === companyKey)
    }
  }

  override def getAllGroupTasksByCompany(company: String): Seq[GroupTaskEntity] = {
    queryExecutor.syncExecuteQuery(
      GroupTaskTable.findListByProperty(_.companyKey === company)
    )
  }
}
