package com.qrsof.empresalit.repositories.employee_resources

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.domain.employee_resources.pojos.EmployeeResourceData
import com.qrsof.empresalit.repositories.resources.{ResourceEntity, ResourceTable}
import jakarta.inject.{Inject, Singleton}

import scala.collection.mutable
import scala.concurrent.ExecutionContext
import scala.concurrent.Await
import scala.concurrent.duration.Duration

@Singleton
class EmployeeResourcesRepositoryImpl @Inject()()(implicit
																									queryExecutor: QueryExecutor,
																									executionContext: ExecutionContext
) extends EmployeeResourcesRepository {

	override def getResourcesByEmployeeKey(employeeKey: String): Seq[ResourceEntity] = queryExecutor.syncExecuteQuery {
		(for {
			employeeResourceTable <- EmployeeResourceTable.tableQuery.filter(_.employeeKey === employeeKey)
			resourceTable <- ResourceTable.tableQuery.filter(_.resourceKey === employeeResourceTable.resourceKey)
		} yield resourceTable).result
	}


	override def saveResources(resources: Seq[EmployeeResourceEntity]): Unit = {
		val transactions = mutable.ListBuffer.empty[DBIO[Any]]

		resources.map(resource => {
			transactions.+=(EmployeeResourceTable.save(resource))
		})

		val transactionsSeq: Seq[DBIOAction[_, NoStream, _]] = transactions.toSeq
		Await.result(
			queryExecutor.database.run(
				DBIO
				 .seq(transactionsSeq: _*)
				 .transactionally
			),
			Duration.Inf
		)
	}

	override def getResourcesDataByEmployeeKey(employeeKey: String): Seq[EmployeeResourceData] = queryExecutor.syncExecuteQuery {
		(for {
			employeeResourceTable <- EmployeeResourceTable.tableQuery.filter(_.employeeKey === employeeKey)
			resourceTable <- ResourceTable.tableQuery.filter(_.resourceKey === employeeResourceTable.resourceKey)
		} yield (resourceTable, employeeResourceTable.required)).result.map(result => {
			result.map( value => {
				EmployeeResourceData(
					resourceKey = value._1.resourceKey, 
					companyKey = value._1.companyKey, 
					name = value._1.name, 
					required = value._2, 
					reference = value._1.reference, 
					path = value._1.path)
			})
		})
	}
}
