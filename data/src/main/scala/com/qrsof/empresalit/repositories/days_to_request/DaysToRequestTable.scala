package com.qrsof.empresalit.repositories.days_to_request

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class DaysToRequestTable(tag: Tag) extends Table[DaysToRequestEntity](tag, Some("empresalit"), "days_to_request_catalog") {
	implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
		d => new Timestamp(d.getTime),
		ts => new Date(ts.getTime)
	)

	def dayId: Rep[Int] = column[Int]("day_id", <PERSON><PERSON>)

	def name: Rep[String] = column[String]("name")

	def createdAt = column[Date]("created_at")

	def updatedAt = column[Date]("updated_at")
	
	override def * : ProvenShape[DaysToRequestEntity] = (
	 dayId,
	 name,
	 createdAt,
	 updatedAt
	).<>(DaysToRequestEntity.apply,DaysToRequestEntity.unapply)
}

object DaysToRequestTable extends GenericDao {
	override type Id = Int

	override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

	override type Entity = DaysToRequestEntity
	override type EntityTable = DaysToRequestTable

	override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

	override def $id(table: EntityTable): Rep[Id] = table.dayId

	override def idLens: Lens[Entity, Id] = {
		Lens.lens { (entity: Entity) =>
			entity.dayId
		} { (entity, id) =>
			entity.copy(dayId = id)
		}
	}
}
