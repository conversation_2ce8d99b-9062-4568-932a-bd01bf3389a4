package com.qrsof.empresalit.repositories.jobs

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class JobsRepositoryImpl @Inject()()(
  implicit queryExecutor: QueryExecutor,
  executionContext: ExecutionContext
) extends JobsRepository {
  override def getAllJobs(): Seq[JobsEntity] = {
    queryExecutor.syncExecuteQuery {
      JobsTable.list
    }
  }

  override def save(jobsEntity: JobsEntity): JobsEntity = {
    queryExecutor.syncExecuteQuery {
      JobsTable.save(jobsEntity)
    }
  }

  override def deleteJobs(jobs_key: String): Unit = {
    queryExecutor.syncExecuteQuery {
      JobsTable.deleteByProperty(_.job_key === jobs_key)
    }
  }

  override def updateJobs(jobsEntity: JobsEntity): Unit = {
    queryExecutor.syncExecuteQuery {
      JobsTable.saveOrUpdate(jobsEntity)
    }
  }

  override def findJob(jobsKey: String): Option[JobsEntity] = {
    queryExecutor.syncExecuteQuery {
      JobsTable.findOptionByProperty(_.job_key === jobsKey)
    }
  }
}
