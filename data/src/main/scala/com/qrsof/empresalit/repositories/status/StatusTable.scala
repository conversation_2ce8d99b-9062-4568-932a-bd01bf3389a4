package com.qrsof.empresalit.repositories.status

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class StatusTable(tag: Tag) extends Table[StatusEntity](tag, Some("empresalit"), "status") {
  implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def status_key: Rep[String] = column[String]("status_key", O.<PERSON>Key)

  def status: Rep[String] = column[String]("status")

  def order_process = column[Int]("order_process")

  def company_key: Rep[String] = column[String]("company_key")

  def created_at = column[Date]("created_at")

  def updated_at = column[Date]("updated_at")

  def * : ProvenShape[StatusEntity] = (status_key, status, order_process, company_key, created_at, updated_at).<>(StatusEntity.apply, StatusEntity.unapply)
}

object StatusTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = StatusEntity
  override type EntityTable = StatusTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[StatusTable] = TableQuery[StatusTable]

  override def $id(table: EntityTable): Rep[Id] = table.status_key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.status_key
    } { (entity, id) =>
      entity.copy(status_key = id)
    }
  }
}
