package com.qrsof.empresalit.repositories.seniority_benefits

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class SeniorityBenefitsRepositoryImpl @Inject()()(implicit
                                                  queryExecutor: QueryExecutor,
                                                  executionContext: ExecutionContext
) extends SeniorityBenefitsRepository {


	override def getSeniorityBenefitLastByYear(year: Int): Option[SeniorityBenefitsEntity] = queryExecutor.syncExecuteQuery {
		SeniorityBenefitsTable.tableQuery.filter(_.yearId <= year ).sortBy(_.yearId.desc).result.headOption

	}

}
