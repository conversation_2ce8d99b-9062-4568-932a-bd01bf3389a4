package com.qrsof.empresalit.repositories.debts_receivable


trait DebtsReceivablesRepository {
  def newDebtReceivable(debtsReceivableEntity: DebtsReceivablesEntity): DebtsReceivablesEntity

  def getAllDebts(): Seq[DebtsReceivablesEntity]

  def deleteDebt(debt_key: String): Unit

  def findDebt(key: String): Option[DebtsReceivablesEntity]

  def markAsPaid(debtsReceivablesEntity: DebtsReceivablesEntity): Unit
}
