package com.qrsof.empresalit.repositories.stores

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.ProvenShape

import java.sql.Timestamp
import java.util.Date

class StoresTable(tag: Tag) extends Table[StoresEntity](tag, Some("empresalit"), "stores") {
  implicit val dateColumType:JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def store_key: Rep[String] = column[String]("store_key", O.<PERSON>Key);

  def company_key: Rep[String] = column[String]("company_key");

  def code: Rep[String] = column[String]("code");

  def name: Rep[String] = column[String]("name");

  def country: Rep[String] = column[String]("country");

  def state: Rep[String] = column[String]("state");

  def city: Rep[String] = column[String]("city");

  def direction: Rep[String] = column[String]("direction");

  def cellphone: Rep[String] = column[String]("cellphone");

  def inserted_at = column[Date]("inserted_at");

  def modified_at = column[Date]("modified_at");

  override def * : ProvenShape[StoresEntity] =
    (store_key, company_key, code, name, country, state, city, direction, cellphone, inserted_at, modified_at)
      .<>(StoresEntity.apply, StoresEntity.unapply)
}

object StoresTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = StoresEntity
  override type EntityTable = StoresTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.store_key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.store_key
    } { (entity, id) =>
      entity.copy(store_key = id)
    }
  }
}
