package com.qrsof.empresalit.repositories.resources

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class ResourceTable(tag: Tag) extends Table[ResourceEntity](tag, Some("empresalit"), "resources") {
	implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
		d => new Timestamp(d.getTime),
		ts => new Date(ts.getTime)
	)

	def resourceKey: Rep[String] = column[String]("resource_key", O.PrimaryKey)

	def companyKey: Rep[String] = column[String]("company_key")

	def name = column[String]("name")

	def reference = column[String]("reference")

	def path = column[String]("path")

	
	override def * : ProvenShape[ResourceEntity] = (
	 resourceKey,
	 companyKey,
	 name,
	 reference,
	 path.?
	).<>(ResourceEntity.apply,ResourceEntity.unapply)
}

object ResourceTable extends GenericDao {
	override type Id = String

	override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

	override type Entity = ResourceEntity
	override type EntityTable = ResourceTable

	override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

	override def $id(table: EntityTable): Rep[Id] = table.resourceKey

	override def idLens: Lens[Entity, Id] = {
		Lens.lens { (entity: Entity) =>
			entity.resourceKey
		} { (entity, id) =>
			entity.copy(resourceKey = id)
		}
	}
}
