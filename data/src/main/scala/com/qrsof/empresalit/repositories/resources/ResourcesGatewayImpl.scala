package com.qrsof.empresalit.repositories.resources

import com.qrsof.empresalit.domain.resources.ResourcesGateway
import com.qrsof.empresalit.domain.resources.pojos.ResourceRequest
import jakarta.inject.{Inject, Singleton}

@Singleton
class ResourcesGatewayImpl @Inject()(resourcesRepository: ResourcesRepository) extends ResourcesGateway {

	override def saveResource(resourceRequest: ResourceRequest): Unit = {
		val resourceEntity: ResourceEntity = ResourceEntity(resourceRequest.resourceKey, resourceRequest.companyKey, resourceRequest.name, resourceRequest.reference, resourceRequest.path)
		resourcesRepository.saveResource(resourceEntity)
	}

	override def getResource(resourceKey: String): ResourceRequest = {
		val resourceEntityOption: Option[ResourceEntity] = resourcesRepository.getResource(resourceKey)
		if (resourceEntityOption.isEmpty) {
			throw new Exception("Not Found")
		}
		ResourceRequest(resourceEntityOption.get.resourceKey, resourceEntityOption.get.companyKey, resourceEntityOption.get.name, resourceEntityOption.get.reference, resourceEntityOption.get.path)
	}

	override def getResourceByCompanyKeyAndResourceKey(companyKey: String, resourceKey: String): ResourceRequest = {
		val resourceEntityOption: Option[ResourceEntity] = resourcesRepository.getResourceByCompanyKeyAndResourceKey(companyKey, resourceKey)
		if (resourceEntityOption.isEmpty) {
			throw new Exception("Not Found")
		}
		ResourceRequest(resourceEntityOption.get.resourceKey, resourceEntityOption.get.companyKey, resourceEntityOption.get.name, resourceEntityOption.get.reference, resourceEntityOption.get.path)
	}

	override def getResourcesByKeys(resourceKeys: Seq[String]): Seq[ResourceRequest] = {
		resourcesRepository.getResourcesByKeys(resourceKeys).map(resource => {
			ResourceRequest(resource.resourceKey, resource.companyKey, resource.name, resource.reference, resource.path)
		})
	}

	override def deleteResourcesByKeys(resourceKeys: Seq[String]): Unit = {
		resourcesRepository.deleteResourcesByKeys(resourceKeys)
	}
}
