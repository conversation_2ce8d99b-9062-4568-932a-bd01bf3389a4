package com.qrsof.empresalit.repositories.employee_resources

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class EmployeeResourceTable(tag: Tag) extends Table[EmployeeResourceEntity](tag, Some("empresalit"), "employee_resources") {
	implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
		d => new Timestamp(d.getTime),
		ts => new Date(ts.getTime)
	)

	def employeeResourceKey: Rep[String] = column[String]("employee_resource_key", <PERSON><PERSON>)

	def employeeKey: Rep[String] = column[String]("employee_key")
	def required: Rep[Boolean] = column[Boolean]("required")

	def resourceKey = column[String]("resource_key")

	
	override def * : ProvenShape[EmployeeResourceEntity] = (
	 employeeResourceKey,
	 required,
	 employeeKey,
	 resourceKey
	).<>(EmployeeResourceEntity.apply,EmployeeResourceEntity.unapply)
}

object EmployeeResourceTable extends GenericDao {
	override type Id = String

	override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

	override type Entity = EmployeeResourceEntity
	override type EntityTable = EmployeeResourceTable

	override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

	override def $id(table: EntityTable): Rep[Id] = table.employeeResourceKey

	override def idLens: Lens[Entity, Id] = {
		Lens.lens { (entity: Entity) =>
			entity.employeeResourceKey
		} { (entity, id) =>
			entity.copy(employeeResourceKey = id)
		}
	}
}
