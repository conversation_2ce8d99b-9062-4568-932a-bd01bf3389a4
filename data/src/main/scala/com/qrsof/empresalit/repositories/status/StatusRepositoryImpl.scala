package com.qrsof.empresalit.repositories.status

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.domain.status.pojos.ChangeStatusOrderRequest
import jakarta.inject.{Inject, Singleton}

import scala.collection.mutable
import scala.concurrent.{Await, ExecutionContext}
import scala.concurrent.duration.Duration

@Singleton
class StatusRepositoryImpl @Inject() ()(implicit
    queryExecutor: QueryExecutor,
    executionContext: ExecutionContext
) extends StatusRepository {

  def getStatusByCompanyKey(companyKey: String): Seq[StatusEntity] = queryExecutor.syncExecuteQuery {
    StatusTable.findListByProperty(_.company_key === companyKey)
  }

  override def getStatusByStatusKey(statusKey: String): Option[StatusEntity] = queryExecutor.syncExecuteQuery {
    StatusTable.findOptionByProperty(_.status_key === statusKey)
  }

  override def changeStatusOrder(changeStatusOrderRequest: ChangeStatusOrderRequest): Unit = {
    val transactions = mutable.ListBuffer.empty[DBIO[Any]]
    transactions.+=(
      StatusTable.tableQuery
        .filter(_.status_key === changeStatusOrderRequest.statusKey)
        .forUpdate
        .result
        .head
        .flatMap(status => {
          StatusTable.saveOrUpdate(status.copy(order_process = changeStatusOrderRequest.newOrderIndex))
        })
    )

    for (i <- changeStatusOrderRequest.firstOrderIndexToUpdate to changeStatusOrderRequest.lastOrderIndexToUpdate) {
      if (i != changeStatusOrderRequest.previousIndex) {
        transactions.+=(
          StatusTable.tableQuery
            .filter(status => status.status_key =!= changeStatusOrderRequest.statusKey && status.order_process === i)
            .forUpdate
            .result
            .head
            .flatMap(status => {
              if (changeStatusOrderRequest.isReduce) {
                StatusTable.saveOrUpdate(status.copy(order_process = i - 1))
              } else {
                StatusTable.saveOrUpdate(status.copy(order_process = i + 1))
              }
            })
        )
      }
    }
    val transactionsSeq: Seq[DBIOAction[_, NoStream, _]] = transactions.toSeq
    Await.result(
      queryExecutor.database.run(
        DBIO
          .seq(transactionsSeq: _*)
          .transactionally
      ),
      Duration.Inf
    )
  }
}
