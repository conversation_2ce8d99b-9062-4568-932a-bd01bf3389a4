package com.qrsof.empresalit.repositories.kanbanBoard

import com.qrsof.empresalit.repositories.group_tasks.GroupTaskEntity
import com.qrsof.empresalit.repositories.status.StatusEntity

trait KanbanBoardRepository {
  def newStatus(statusEntity: StatusEntity): StatusEntity

  def getAllStatusByCompany(companyKey: String): Seq[StatusEntity]
  def getAllGroupTasksByCompany(company: String): Seq[GroupTaskEntity]
}
