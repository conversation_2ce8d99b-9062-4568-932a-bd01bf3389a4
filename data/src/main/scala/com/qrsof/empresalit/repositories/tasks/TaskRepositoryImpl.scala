package com.qrsof.empresalit.repositories.tasks

import com.google.inject.{Inject, Singleton}
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor

import scala.concurrent.ExecutionContext

@Singleton
class TaskRepositoryImpl @Inject() ()(implicit queryExecutor: QueryExecutor, ec: ExecutionContext) extends TaskRepository {
  override def updateTaskAssign(taskUpdated: TaskEntity): Unit = {
    queryExecutor.syncExecuteQuery(
      TaskTable.tableQuery.filter(_.taskKey === taskUpdated.taskKey).forUpdate.result.head.flatMap(task => TaskTable.saveOrUpdate(task.copy(responsible = taskUpdated.responsible)))
    )
  }
}
