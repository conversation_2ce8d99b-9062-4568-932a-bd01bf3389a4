package com.qrsof.empresalit.repositories.resources

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}
import scala.concurrent.ExecutionContext

@Singleton
class ResourcesRepositoryImpl @Inject()()(
  implicit queryExecutor: QueryExecutor,
  executionContext: ExecutionContext
) extends ResourcesRepository {

	override def saveResource(resourceEntity: ResourceEntity): Unit = queryExecutor.syncExecuteQuery {
		ResourceTable.save(resourceEntity)
	}

	override def getResource(resourceKey: String): Option[ResourceEntity] = queryExecutor.syncExecuteQuery {
		ResourceTable.findOptionByProperty(_.resourceKey === resourceKey)
	}

	override def getResourceByCompanyKeyAndResourceKey(companyKey: String, resourceKey: String): Option[ResourceEntity] = queryExecutor.syncExecuteQuery {
		ResourceTable.findOptionByProperty(item => item.resourceKey === resourceKey && item.companyKey === companyKey)
	}

	override def getResourcesByKeys(resourceKeys: Seq[String]): Seq[ResourceEntity] =  queryExecutor.syncExecuteQuery {
		ResourceTable.findListByProperty(_.resourceKey.inSet(resourceKeys))
	}

	override def deleteResourcesByKeys(resourceKeys: Seq[String]): Unit =  queryExecutor.syncExecuteQuery {
		ResourceTable.deleteByProperty(_.resourceKey.inSet(resourceKeys))
	}
}
