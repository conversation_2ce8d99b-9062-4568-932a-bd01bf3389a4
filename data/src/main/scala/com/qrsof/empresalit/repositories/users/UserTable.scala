package com.qrsof.empresalit.repositories.users

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import com.qrsof.empresalit.repositories.users
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class UserTable(tag: Tag) extends Table[UserEntity](tag, Some("empresalit"), "users") {
  implicit val dateColumnType:JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def key = column[String]("user_key", <PERSON><PERSON>)
  def username = column[String]("username")
  def name = column[String]("name")

  def modifiedAt = column[Date]("modified_at")

  def insertedAt = column[Date]("inserted_at")

  override def * : ProvenShape[UserEntity] = (key,username.?, name.?, modifiedAt, insertedAt).<>(UserEntity.apply, UserEntity.unapply)
}

object UserTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = UserEntity
  override type EntityTable = UserTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.key
    } { (entity, id) =>
      entity.copy(key = id)
    }
  }
}
