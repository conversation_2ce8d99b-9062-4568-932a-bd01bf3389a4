package com.qrsof.empresalit.repositories.task_resources

import com.qrsof.empresalit.domain.resources.pojos.ResourceRequest
import com.qrsof.empresalit.domain.task_resources.TaskResourcesGateway
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.pojos.TaskResource
import jakarta.inject.{Inject, Singleton}

@Singleton
class TaskResourcesGatewayImpl @Inject() (taskResourcesRepository: TaskResourcesRepository) extends TaskResourcesGateway {

  override def getResourcesByTaskKey(taskKey: String): Seq[ResourceRequest] = {
    taskResourcesRepository
      .getResourcesByTaskKey(taskKey)
      .map(resource => {
        ResourceRequest(resource.resourceKey, resource.companyKey, resource.name, resource.reference, resource.path)
      })
  }

  override def saveResourcesByTaskKey(resources: Seq[TaskResource]): Unit = {
    val taskResourceEntity: Seq[TaskResourceEntity] = resources.map(resource => {
      TaskResourceEntity(taskResourceKey = resource.taskResourceKey, taskKey = resource.taskKey, resourceKey = resource.reference.get)
    })
    taskResourcesRepository.saveResources(taskResourceEntity)
  }
}
