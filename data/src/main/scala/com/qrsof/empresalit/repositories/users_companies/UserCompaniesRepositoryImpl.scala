package com.qrsof.empresalit.repositories.users_companies

import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class UserCompaniesRepositoryImpl @Inject()()
                                           (
                                             implicit queryExecutor: QueryExecutor,
                                             executionContext: ExecutionContext
                                           ) extends UserCompaniesRepository {
  override def save(usersCompaniesEntity: UsersCompaniesEntity): UsersCompaniesEntity = {
    queryExecutor.syncExecuteQuery {
      UserCompaniesTable.save(usersCompaniesEntity)
    }
  }

  override def getUserCompany(): Seq[UsersCompaniesEntity] = {
    queryExecutor.syncExecuteQuery {
      UserCompaniesTable.list
    }
  }
}
