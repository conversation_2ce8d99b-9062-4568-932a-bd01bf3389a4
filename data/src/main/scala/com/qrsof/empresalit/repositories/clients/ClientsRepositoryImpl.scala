package com.qrsof.empresalit.repositories.clients

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

@Singleton
class ClientsRepositoryImpl @Inject() ()(implicit
    queryExecutor: QueryExecutor,
    executionContext: ExecutionContext
) extends ClientsRepository {

  val logger: Logger = LoggerFactory.getLogger(classOf[ClientsRepository])

  override def newClient(clientsEntity: ClientsEntity): ClientsEntity = {
    logger.info(s"Cliente entity:->{}", clientsEntity)
    queryExecutor.syncExecuteQuery {
      ClientsTable.save(clientsEntity)
    }
  }

  override def getAllClients(): Seq[ClientsEntity] = {
    queryExecutor.syncExecuteQuery {
      ClientsTable.list
    }
  }

  override def deleteClient(client_key: String): Unit = {
    queryExecutor.syncExecuteQuery {
      ClientsTable.deleteByProperty(_.client_key === client_key)
    }
  }

  override def getByName(name: String): Seq[ClientsEntity] = {
    queryExecutor.syncExecuteQuery {
      ClientsTable.listByProperty(_.name like ("%" + name + "%"))
    }
  }

  override def getByRfc(rfc: String): Option[ClientsEntity] = {
    queryExecutor.syncExecuteQuery {
      ClientsTable.findOptionByProperty(_.rfc === rfc)
    }
  }

  override def getByKey(clientKey: String): Option[ClientsEntity] = {
    queryExecutor.syncExecuteQuery {
      ClientsTable.findOptionByProperty(_.client_key === clientKey)
    }
  }
}
