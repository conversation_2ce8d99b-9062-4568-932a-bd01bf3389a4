package com.qrsof.empresalit.repositories.employee

trait EmployeeRepository {
	def save(employeeEntity: EmployeeEntity): EmployeeEntity

	def getAllEmployee(): Seq[EmployeeEntity]

	def findEmployee(employeeKey: String): Option[EmployeeEntity]

	def updateEmployee(employeeEntity: EmployeeEntity): EmployeeEntity

	def deleteEmployee(employeeKey: String): Unit

	def putAccessUserKey(employeeKey: String, accessUserKey: String): Unit

	def findEmployeeByAccessUserKey(accessUserKey: String): Option[EmployeeEntity]
}
