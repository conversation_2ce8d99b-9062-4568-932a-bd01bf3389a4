package com.qrsof.empresalit.repositories.group_tasks

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class GroupTaskTable(tag: Tag) extends Table[GroupTaskEntity](tag, Some("empresalit"), "group_tasks") {
	implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
		d => new Timestamp(d.getTime),
		ts => new Date(ts.getTime)
	)

	def groupTaskKey: Rep[String] = column[String]("group_task_key", O.PrimaryKey)

	def folio: Rep[String] = column[String]("folio")

	def entryDate = column[Date]("entry_date")
	def outputDate = column[Date]("output_date")

	def companyKey: Rep[String] = column[String]("company_key")
	def clientKey: Rep[String] = column[String]("client_key")

	def createdAt = column[String]("created_at")

	def updatedAt = column[String]("updated_at")

	override def * : ProvenShape[GroupTaskEntity] = (
	 groupTaskKey,
	 companyKey,
	 clientKey,
	 folio,
	 entryDate,
	 outputDate.?
	).<>(GroupTaskEntity.apply, GroupTaskEntity.unapply)
}

object GroupTaskTable extends GenericDao {
	override type Id = String

	override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

	override type Entity = GroupTaskEntity
	override type EntityTable = GroupTaskTable

	override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

	override def $id(table: EntityTable): Rep[Id] = table.groupTaskKey

	override def idLens: Lens[Entity, Id] = {
		Lens.lens { (entity: Entity) =>
			entity.groupTaskKey
		} { (entity, id) =>
			entity.copy(groupTaskKey = id)
		}
	}
}
