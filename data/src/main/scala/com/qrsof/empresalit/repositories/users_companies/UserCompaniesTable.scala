package com.qrsof.empresalit.repositories.users_companies

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import com.qrsof.empresalit.repositories.users_companies
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class UserCompaniesTable(tag: Tag) extends Table[UsersCompaniesEntity](tag, Some("empresalit"), "user_companies") {
  implicit val dateColumnType:JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def user_company_key: Rep[String] = column[String]("user_company_key", <PERSON><PERSON>)

  def company_key: Rep[String] = column[String]("company_key")

  def user_key: Rep[String] = column[String]("user_key")

  def modifiedAt = column[Date]("modified_at")

  def insertedAt = column[Date]("inserted_at")

  override def * : ProvenShape[UsersCompaniesEntity] =
    (user_company_key, company_key, user_key, modifiedAt, insertedAt)
      .<>(UsersCompaniesEntity.apply, UsersCompaniesEntity.unapply)
}

object UserCompaniesTable extends GenericDao {
  override type Id = String

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

  override type Entity = UsersCompaniesEntity
  override type EntityTable = UserCompaniesTable

  override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

  override def $id(table: EntityTable): Rep[Id] = table.user_company_key

  override def idLens: Lens[Entity, Id] = {
    Lens.lens { (entity: Entity) =>
      entity.user_company_key
    } { (entity, id) =>
      entity.copy(user_company_key = id)
    }
  }
}
