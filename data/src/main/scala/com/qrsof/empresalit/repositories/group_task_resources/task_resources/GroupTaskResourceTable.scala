package com.qrsof.empresalit.repositories.group_task_resources.task_resources

import com.qrsof.core.database.qrslick
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens, QrPostgresProfile}
import slick.jdbc.JdbcType
import slick.lifted.{ProvenShape, Tag}

import java.sql.Timestamp
import java.util.Date

class GroupTaskResourceTable(tag: Tag) extends Table[GroupTaskResourceEntity](tag, Some("empresalit"), "group_task_resources") {
	implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
		d => new Timestamp(d.getTime),
		ts => new Date(ts.getTime)
	)

	def groupTaskResourceKey: Rep[String] = column[String]("group_task_resource_key", <PERSON><PERSON>)

	def groupTaskKey: Rep[String] = column[String]("group_task_key")

	def resourceKey = column[String]("resource_key")

	
	override def * : ProvenShape[GroupTaskResourceEntity] = (
	 groupTaskResourceKey,
	 groupTaskKey,
	 resourceKey

	).<>(GroupTaskResourceEntity.apply,GroupTaskResourceEntity.unapply)
}

object GroupTaskResourceTable extends GenericDao {
	override type Id = String

	override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]

	override type Entity = GroupTaskResourceEntity
	override type EntityTable = GroupTaskResourceTable

	override def tableQuery: QrPostgresProfile.api.TableQuery[EntityTable] = TableQuery[EntityTable]

	override def $id(table: EntityTable): Rep[Id] = table.groupTaskResourceKey

	override def idLens: Lens[Entity, Id] = {
		Lens.lens { (entity: Entity) =>
			entity.groupTaskResourceKey
		} { (entity, id) =>
			entity.copy(groupTaskResourceKey = id)
		}
	}
}
