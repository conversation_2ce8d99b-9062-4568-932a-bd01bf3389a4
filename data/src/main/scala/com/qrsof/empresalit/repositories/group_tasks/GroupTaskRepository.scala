package com.qrsof.empresalit.repositories.group_tasks

import com.qrsof.empresalit.repositories.group_task_resources.task_resources.GroupTaskResourceEntity
import com.qrsof.empresalit.repositories.task_resources.TaskResourceEntity
import com.qrsof.empresalit.repositories.tasks.TaskEntity
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.pojos.{ReceptionOfEquipment, ReceptionOfEquipmentFilter}

trait GroupTaskRepository {
	def newEquipmentGroupRepository(equipmentGroup: GroupTaskEntity, groupTasksResources: Seq[GroupTaskResourceEntity], equipments: Seq[TaskEntity], equipmentEvidences: Seq[TaskResourceEntity]): GroupTaskEntity

	def getTotalReceptionOfEquipment(companyKey: String): Int

	def getReceptionOfEquipment(companyKey: String,receptionOfEquipmentFilter: ReceptionOfEquipmentFilter, pageNumber: Int, pageSize: Int): Seq[ReceptionOfEquipment]
}
