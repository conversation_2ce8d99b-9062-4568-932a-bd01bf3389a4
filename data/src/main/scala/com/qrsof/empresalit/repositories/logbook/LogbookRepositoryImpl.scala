package com.qrsof.empresalit.repositories.logbook

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import jakarta.inject.{Inject, Singleton}

import scala.collection.mutable
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext}

@Singleton
class LogbookRepositoryImpl @Inject() ()(implicit
    queryExecutor: QueryExecutor,
    executionContext: ExecutionContext
) extends LogbookRepository {

  override def newLogbookWithResources(logbook: LogbookEntity): Unit = {
    queryExecutor.syncExecuteQuery(LogbookTable.save(logbook))
  }

  override def getLogbookByTaskKey(taskKey: String): Seq[LogbookEntity] = {
    queryExecutor.syncExecuteQuery {
      LogbookTable.findListByProperty(_.taskKey === taskKey)
    }
  }

  override def createNewLogbook(logbookEntity: LogbookEntity): Unit = {
    queryExecutor.syncExecuteQuery(LogbookTable.save(logbookEntity))
  }

  override def createNewLogbookForNewInputs(newLogbooks: Seq[LogbookEntity]): Unit = {

    val transactions = mutable.ListBuffer.empty[DBIO[Any]]
    newLogbooks.foreach { newLogbook =>
      transactions.+=(LogbookTable.save(newLogbook))
    }
    val transactionsSeq = transactions.toSeq

    Await.result(
      queryExecutor.database.run(
        DBIO.seq(transactionsSeq: _*).transactionally
      ),
      Duration.Inf
    )
  }

}
