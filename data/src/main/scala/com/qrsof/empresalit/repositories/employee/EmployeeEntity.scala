package com.qrsof.empresalit.repositories.employee

import java.util.Date

case class EmployeeEntity(
													employeeKey: String,
													companyKey: String,
													employeeNumber: String,
													fullname: String,
													name: String,
													lastname: String,
													motherLastname: String,
													email: Option[String],
													entryDate: Date,
													birthDate: Date,
													areaKey: Option[String],
													jobKey: String,
													accountNumber: Option[String],
													amount: BigDecimal,
													bonus: BigDecimal,
													insertAt: Date,
													modifiedAt: Date,
													accessUserKey: Option[String])
