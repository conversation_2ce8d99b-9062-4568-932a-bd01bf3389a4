package com.qrsof.empresalit.domain.holidays

import com.google.inject.{Inject, Singleton}
import com.qrsof.empresalit.domain.holidays.models.HoliDayDTO
import com.qrsof.empresalit.repositories.holidays.HolidaysRepository
import org.slf4j.{Logger, LoggerFactory}

@Singleton
class HolidaysGatewayImpl @Inject() (holidaysRepository: HolidaysRepository) extends HoliDaysGateway {
  val logger: Logger = LoggerFactory.getLogger(classOf[HolidaysGatewayImpl])
  override def getHoliDays: Seq[HoliDayDTO] = {
    val holiDays = holidaysRepository.getHolidays.map(holiDay => HoliDayDTO(holiDay.key, holiDay.name, holiDay.date))
    holiDays
  }

  override def getHoliDaysFiltered(startDate: String, endDate: String): Seq[HoliDayDTO] = {
    holidaysRepository.getHolidaysFiltered(startDate, endDate).map(holiDay => HoliDayDTO(holiDay.key, holiDay.name, holiDay.date))
  }
}
