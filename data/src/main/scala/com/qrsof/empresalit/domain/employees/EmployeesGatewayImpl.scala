package com.qrsof.empresalit.domain.employees

import com.qrsof.empresalit.domain.employees.pojos.EmployeeDto
import com.qrsof.empresalit.repositories.employee.EmployeeRepository
import jakarta.inject.{Inject, Singleton}

@Singleton
class EmployeesGatewayImpl @Inject()(employeeRepository: EmployeeRepository) extends EmployeesGateway{

	override def getEmployeeByKey(employeeKey: String): Option[EmployeeDto] = {
		employeeRepository.findEmployee(employeeKey).map(employee => {
			EmployeeDto(employeeKey = employee.employeeKey, companyKey = employee.companyKey, employeeNumber = employee.employeeNumber, fullname = employee.fullname, name = employee.name, lastname = employee.lastname, motherLastname = employee.motherLastname, entryDate = employee.entryDate, birthDate = employee.birthDate, areaKey = employee.areaKey, jobKey = employee.jobKey, accountNumber = employee.accountNumber, amount = employee.amount, bonus = employee.bonus, insertAt = employee.insertAt, modifiedAt = employee.modifiedAt, accessUserKey = employee.accessUserKey)
		})
	}

	override def putAccessUserKey(employeeKey: String, accessUserKey: String): Unit = {
		employeeRepository.putAccessUserKey(employeeKey, accessUserKey)
	}

	override def getEmployeeByAccessUserKey(accessUserKey: String): Option[EmployeeDto] = {
		employeeRepository.findEmployeeByAccessUserKey(accessUserKey).map(employee => {
			EmployeeDto(employeeKey = employee.employeeKey, companyKey = employee.companyKey, employeeNumber = employee.employeeNumber, fullname = employee.fullname, name = employee.name, lastname = employee.lastname, motherLastname = employee.motherLastname, entryDate = employee.entryDate, birthDate = employee.birthDate, areaKey = employee.areaKey, jobKey = employee.jobKey, accountNumber = employee.accountNumber, amount = employee.amount, bonus = employee.bonus, insertAt = employee.insertAt, modifiedAt = employee.modifiedAt, accessUserKey = employee.accessUserKey)
		})
	}
}
