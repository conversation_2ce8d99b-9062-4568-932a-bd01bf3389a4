package com.qrsof.empresalit.domain.brand_company

import com.google.inject.{Inject, Singleton}
import com.qrsof.empresalit.domain.brand_company.models.{BrandCompanyGatewayRequest, UpdateBrandCompanyRequestGateway}
import com.qrsof.empresalit.domain.resources.ResourcesService
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import com.qrsof.empresalit.repositories.brand_company.{BrandCompanyEntity, BrandCompanyRepository}
import io.scalaland.chimney.dsl.*
import org.slf4j.{Logger, LoggerFactory}

import java.util.Date

@Singleton
class BrandCompanyGatewayImpl @Inject() (branchCompanyRepository: BrandCompanyRepository, resourceService: ResourcesService, empresalitUtils: EmpresalitUtils) extends BrandCompanyGateway {
  val logger: Logger = LoggerFactory.getLogger(classOf[BrandCompanyGatewayImpl])
  override def addBrandCompany(brandCompany: BrandCompanyGatewayRequest): String = {
    val brandCompanySaved = branchCompanyRepository.saveBrandCompany(
      brandCompany
        .into[BrandCompanyEntity]
        .withFieldConst(_.colorPalette, brandCompany.colorPalette.getOrElse(List.empty))
        .withFieldConst(_.color, brandCompany.color)
        .withFieldConst(_.brandCompanyKey, empresalitUtils.generateKey())
        .withFieldConst(_.createdAt, empresalitUtils.getDate)
        .withFieldConst(_.modifiedAt, empresalitUtils.getDate)
        .transform
    )
    brandCompanySaved.brandCompanyKey
  }

  override def updateBrandCompany(brandCompany: UpdateBrandCompanyRequestGateway): Unit = {

    var newLogoResourceKey: String = String()
    brandCompany.logoFile match {
      case Some(value) =>
        newLogoResourceKey = resourceService.saveResource(
          brandCompany.companyKey,
          s"${brandCompany.companyKey}/logo",
          brandCompany.logoFile.get.filename.get,
          brandCompany.logoFile.get.entity.data.toArray
        )
        branchCompanyRepository.updateBrandCompany(
          BrandCompanyEntity(
            brandCompany.companyKey,
            brandCompany.brandKey,
            brandCompany.colorPalette.getOrElse(List.empty),
            brandCompany.color,
            Some(newLogoResourceKey),
            new Date(),
            new Date()
          )
        )
      case None =>
        branchCompanyRepository.updateBrandCompany(
          BrandCompanyEntity(
            brandCompany.companyKey,
            brandCompany.brandKey,
            brandCompany.colorPalette.getOrElse(List.empty),
            brandCompany.color,
            brandCompany.logoResourceKey,
            new Date(),
            new Date()
          )
        )
    }
  }
}
