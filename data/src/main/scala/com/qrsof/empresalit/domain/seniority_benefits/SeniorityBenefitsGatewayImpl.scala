package com.qrsof.empresalit.domain.seniority_benefits

import com.qrsof.empresalit.domain.seniority_benefits.pojos.SeniorityBenefitData
import com.qrsof.empresalit.repositories.seniority_benefits.SeniorityBenefitsRepository
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}


@Singleton
class SeniorityBenefitsGatewayImpl @Inject()(seniorityBenefitsRepository: SeniorityBenefitsRepository) extends SeniorityBenefitsGateway{
	val logger: Logger = LoggerFactory.getLogger(classOf[SeniorityBenefitsGatewayImpl])

	override def getSeniorityBenefitLastByYear(year: Int): Option[SeniorityBenefitData] = {
		val seniority = seniorityBenefitsRepository.getSeniorityBenefitLastByYear(year).map(seniorityBenefit => SeniorityBenefitData(seniorityBenefit.yearId, vacationDays = seniorityBenefit.vacationDays))
		seniority
	}
}
