package com.qrsof.empresalit.domain.employee_resources

import com.qrsof.empresalit.domain.employee_resources.pojos.EmployeeResourceData
import com.qrsof.empresalit.repositories.employee_resources.EmployeeResourcesRepository
import jakarta.inject.{Inject, Singleton}

@Singleton
class EmployeeResourcesGatewayImpl @Inject()(employeeResourcesRepository: EmployeeResourcesRepository) extends EmployeeResourcesGateway {
	
	
	override def getResourcesByEmployeeKey(employeeKey: String): Seq[EmployeeResourceData] = {
		employeeResourcesRepository.getResourcesDataByEmployee<PERSON>ey(employeeKey)
	}

	override def getResourcesDataByEmployeeKey(employeeKey: String): Seq[EmployeeResourceData] = {
		employeeResourcesRepository.getResourcesDataByEmployee<PERSON>ey(employeeKey)
	}
}
