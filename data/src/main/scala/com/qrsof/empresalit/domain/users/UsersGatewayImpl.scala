package com.qrsof.empresalit.domain.users

import com.qrsof.empresalit.repositories.users.{UserEntity, UserRepository}
import jakarta.inject.{Inject, Singleton}

import java.util.Date

@Singleton
class UsersGatewayImpl @Inject() (userRepository: UserRepository) extends UsersGateway {
  override def addNewUser(user: User): CreatedUser = {
    //		val key = UUID.randomUUID().toString;
    val userEntity = userRepository.save(
      UserEntity(user.key, Some(user.username), user.name, new Date(), new Date())
    )
    CreatedUser(userEntity.key)
  }

  override def getUserByKey(userKey: String): Option[User] = {
    userRepository.getUserByKey(userKey).map(item => User(item.key, item.username.orNull, item.name))
  }
}
