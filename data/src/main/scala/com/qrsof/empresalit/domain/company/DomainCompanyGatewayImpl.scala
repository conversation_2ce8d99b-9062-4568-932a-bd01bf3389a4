package com.qrsof.empresalit.domain.company

import com.qrsof.empresalit.domain.company.actions.models.*
import com.qrsof.empresalit.repositories.addresses.{AddressesEntity, AddressesRepository}
import com.qrsof.empresalit.repositories.brand_company.BrandCompanyRepository
import com.qrsof.empresalit.repositories.companies.{CompaniesEntity, CompaniesRepository}
import com.qrsof.empresalit.repositories.company_certificates.CompanyCertificatesRepository
import com.qrsof.empresalit.repositories.users_companies.UserCompaniesRepository
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import java.util.Date

@Singleton
class DomainCompanyGatewayImpl @Inject() (
    companyCertificatesRepository: CompanyCertificatesRepository,
    userCompaniesRepository: UserCompaniesRepository,
    companyRepository: CompaniesRepository,
    addressRepository: AddressesRepository,
    brandCompanyRepository: BrandCompanyRepository
) extends DomainCompanyGateway {
  val logger: Logger = LoggerFactory.getLogger(classOf[DomainCompanyGatewayImpl])

  override def getCompanyDataByCompanyKey(companyKey: String): DataCompanyResponse = {
    var dataCompany: DataCompanyResponse = null
    val companyData = companyRepository.getCompany(companyKey).head
    val addressData = addressRepository.getAddress(companyData.address_key)
    val brandCompanyData = brandCompanyRepository.getBrandCompany(companyKey)
    if (brandCompanyData.isEmpty) {
      dataCompany = DataCompanyResponse(
        companyData.into[Company].withFieldConst(_.regimenFiscalClave, companyData.regimen_fiscal_clave).transform,
        addressData.into[CompanyAddress].withFieldConst(_.addressKey, addressData.address_key).transform,
        None
      )
    } else {
      dataCompany = DataCompanyResponse(
        companyData.into[Company].withFieldConst(_.regimenFiscalClave, companyData.regimen_fiscal_clave).transform,
        addressData.into[CompanyAddress].withFieldConst(_.addressKey, addressData.address_key).transform,
        Some(brandCompanyData.head.into[BrandCompany].withFieldConst(_.brandKey, brandCompanyData.head.brandCompanyKey).transform)
      )
    }
    dataCompany
  }

  override def updateCompanyDataGateway(dataCompanyRequestAction: CompanyDataRequestAction): Unit = {
    companyRepository.updateCompanyData(
      dataCompanyRequestAction
        .into[CompaniesEntity]
        .withFieldConst(_.key, dataCompanyRequestAction.companyKey)
        .withFieldConst(_.modifiedAt, new Date())
        .withFieldConst(_.insertedAt, new Date())
        .withFieldConst(_.address_key, String())
        .transform
    )
  }
}
