package com.qrsof.empresalit.domain.addressess

import com.qrsof.empresalit.domain.addressess.actions.models.AddressRequestGateway
import com.qrsof.empresalit.domain.addressess.actions.{AddressDataActionRequest, AddressEnt, AddressKey}
import com.qrsof.empresalit.repositories.addresses.{AddressesEntity, AddressesRepository}
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}

import java.util.{Date, UUID}

@Singleton
class AddressGatewayImpl @Inject() (addressesRepository: AddressesRepository) extends AddressGateway {
  override def findAddress(addressKey: String): AddressEnt = {
    addressesRepository.getAddress(addressKey).into[AddressEnt].transform
  }

  override def newAddress(address: AddressDataActionRequest): AddressKey = {
    val newAddress = addressesRepository.save(
      address
        .into[AddressesEntity]
        .withFieldConst(_.address_key, UUID.randomUUID().toString)
        .withFieldConst(_.insertedAt, new Date())
        .withFieldConst(_.modifiedAt, new Date())
        .transform
    )
    AddressKey(newAddress.address_key)
  }

  override def deleteAddress(address_key: String): Unit = {
    addressesRepository.delete(address_key)
  }

  override def updateAddress(address: AddressRequestGateway): Unit = {
    addressesRepository.update(
      address
        .into[AddressesEntity]
        .withFieldConst(_.numero_interior, "0")
        .withFieldConst(_.modifiedAt, new Date())
        .withFieldConst(_.insertedAt, new Date())
        .transform
    )
  }
}
