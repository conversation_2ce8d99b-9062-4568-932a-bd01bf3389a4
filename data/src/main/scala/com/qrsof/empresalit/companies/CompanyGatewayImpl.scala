package com.qrsof.empresalit.companies

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.domain.storage.StorageGateway
import io.scalaland.chimney.dsl.transformInto
import io.scalaland.chimney.inlined.into
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import java.util.Date
import scala.concurrent.ExecutionContext

@Singleton
class CompanyGatewayImpl @Inject() (
    queryExecutor: QueryExecutor,
    companyCertificateStore: StorageGateway
)(implicit
    executionContext: ExecutionContext
) extends CompanyGateway {
  val logger: Logger = LoggerFactory.getLogger(classOf[CompanyGatewayImpl])

  implicit def companyMapper: CompanyEntity => Company = (entity: CompanyEntity) =>
    Company(
      key = entity.key,
      name = entity.name,
      rfc = entity.rfc,
      regimenFiscalClave = entity.regimenFiscalClave
    )

  implicit def companyEntityMapper: Company => CompanyEntity = (company: Company) =>
    CompanyEntity(
      key = company.key,
      name = company.name,
      rfc = company.rfc,
      regimenFiscalClave = company.regimenFiscalClave
    )

  def save(company: Company): Unit = {
    queryExecutor.syncExecuteQuery {
      CompanyRepository.save(this.companyEntityMapper(company))
    }
  }

  override def getCompanyByKey(companyKey: String): Option[Company] = {
    queryExecutor
      .syncExecuteQuery {
        CompanyRepository.findOptionByProperty(_.key === companyKey)
      }
      .map(entity => this.companyMapper(entity))
  }

  override def getCompanyCertificateByCompanyKey(companyKey: String): Option[CompanyCertificate] = {
    queryExecutor
      .syncExecuteQuery {
        CompanyCertificateRepository.findOptionByProperty(_.companyKey === companyKey)
      }
      .map(entity =>
        CompanyCertificate(
          noCertificado = entity.noCertificado,
          privateKey = companyCertificateStore.getResource(Some(s"${companyKey}/certificates"), entity.privateKeyReference),
          password = entity.password,
          publicKey = companyCertificateStore.getResource(Some(s"${companyKey}/certificates"), entity.publicKeyReference),
          key = entity.key,
          companyKey = entity.companyKey
        )
      )
  }

  override def saveCompanyCertificate(certificate: CompanyCertificate): Unit = {
    val privateKeyReference = companyCertificateStore.saveResource(s"${certificate.companyKey}/certificates", certificate.privateKey)
    val publicCertificateKey = companyCertificateStore.saveResource(s"${certificate.companyKey}/certificates", certificate.publicKey)

    queryExecutor.syncExecuteQuery {
      CompanyCertificateRepository.save(
        CompanyCertificateEntity(
          key = certificate.key,
          companyKey = certificate.companyKey,
          noCertificado = certificate.noCertificado,
          privateKeyReference = privateKeyReference,
          publicKeyReference = publicCertificateKey,
          password = certificate.password,
          modifiedAt = new Date(),
          insertedAt = new Date()
        )
      )
    }
  }

  override def updateCompanyCertificates(certificate: CompanyCertificate): Unit = {
    queryExecutor.syncExecuteQuery {
      CompanyCertificateRepository.tableQuery
        .filter(_.companyKey === certificate.companyKey)
        .forUpdate
        .result
        .head
        .flatMap(certificateEntity => {
          CompanyCertificateRepository.saveOrUpdate(
            certificateEntity.copy(
              noCertificado = certificate.noCertificado,
              privateKeyReference = companyCertificateStore.saveResource(s"${certificate.companyKey}/certificates", certificate.privateKey),
              publicKeyReference = companyCertificateStore.saveResource(s"${certificate.companyKey}/certificates", certificate.publicKey),
              password = certificate.password,
              modifiedAt = new Date()
            )
          )
        })
    }
  }
}
