package com.qrsof.empresalit.companies

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens}
import slick.jdbc.JdbcType

import java.sql.Timestamp
import java.util.Date

class CompanyCertificateRepository(tag: Tag) extends Table[CompanyCertificateEntity](tag, Some("empresalit"), "company_certificates") {
  implicit val dateColumnType:JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
    d => new Timestamp(d.getTime),
    ts => new Date(ts.getTime)
  )

  def key = column[String]("key", O.<PERSON>ey)

  def companyKey = column[String]("company_key")

  def noCertificado = column[String]("no_certificado")

  def privateKey = column[String]("private_key")

  def password = column[String]("password")

  def publicKey = column[String]("public_key")

  def modifiedAt = column[Date]("modified_at")

  def insertedAt = column[Date]("inserted_at")

  def company = foreignKey("company_fk", companyKey, CompanyRepository.tableQuery)(_.key, onUpdate = ForeignKeyAction.Cascade, onDelete = ForeignKeyAction.Cascade)

  override def * = (key, companyKey, noCertificado, privateKey, password, publicKey, modifiedAt, insertedAt).<>(CompanyCertificateEntity.apply, CompanyCertificateEntity.unapply)
}

object CompanyCertificateRepository extends GenericDao {
  override type Entity = CompanyCertificateEntity
  override type Id = String
  override type EntityTable = CompanyCertificateRepository

  override def $id(table: EntityTable): Rep[Id] = table.key

  override def idLens: Lens[Entity, Id] = Lens.lens { (entity: Entity) => entity.key } { (entity, id) => entity.copy(key = id) }

  override def tableQuery: TableQuery[EntityTable] = TableQuery[EntityTable]

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]
}
