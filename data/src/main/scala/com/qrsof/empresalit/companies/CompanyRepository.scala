package com.qrsof.empresalit.companies

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.{GenericDao, Lens}
import slick.jdbc.JdbcType

class CompanyRepository(tag: Tag) extends Table[CompanyEntity](tag, Some("empresalit"), "companies") {

  def key = column[String]("key", <PERSON><PERSON>)

  def name = column[String]("name")

  def rfc = column[String]("rfc")

  def regimenFiscalClave = column[String]("regimen_fiscal_clave")

  override def * = (key, name, rfc, regimenFiscalClave).<>(CompanyEntity.apply, CompanyEntity.unapply)
}

object CompanyRepository extends GenericDao {
  override type Entity = CompanyEntity
  override type Id = String
  override type EntityTable = CompanyRepository

  override def $id(table: EntityTable): Rep[Id] = table.key

  override def idLens: Lens[Entity, Id] = Lens.lens { (entity: Entity) => entity.key } { (entity, id) => entity.copy(key = id) }

  override def tableQuery: TableQuery[EntityTable] = TableQuery[EntityTable]

  override def baseTypedType: JdbcType[Id] = implicitly[JdbcType[Id]]
}
