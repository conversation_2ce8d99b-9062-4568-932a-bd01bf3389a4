package com.qrsof.empresalit

import com.google.inject.{AbstractModule, Provides}
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.core.evolution.database.{DatabaseEvolutionModule, QrEvolutionResources}
import com.qrsof.core.storage.filesystem.FileSystemStorageConfig
import com.qrsof.empresalit.AppEnvironment.{Development, Production, Sandbox, Test}
import com.qrsof.empresalit.companies.{CompanyGateway, CompanyGatewayImpl}
import com.qrsof.empresalit.domain.addressess.{AddressGateway, AddressGatewayImpl}
import com.qrsof.empresalit.domain.brand_company.{BrandCompanyGateway, BrandCompanyGatewayImpl}
import com.qrsof.empresalit.domain.client.ClientGateway
import com.qrsof.empresalit.domain.company.{DomainCompanyGateway, DomainCompanyGatewayImpl}
import com.qrsof.empresalit.domain.employee_resources.{EmployeeResourcesGateway, EmployeeResourcesGatewayImpl}
import com.qrsof.empresalit.domain.employees.{EmployeesGateway, EmployeesGatewayImpl}
import com.qrsof.empresalit.domain.holidays.{HoliDaysGateway, HolidaysGatewayImpl}
import com.qrsof.empresalit.domain.logbook.LogbookGateway
import com.qrsof.empresalit.domain.resources.ResourcesGateway
import com.qrsof.empresalit.domain.seniority_benefits.{SeniorityBenefitsGateway, SeniorityBenefitsGatewayImpl}
import com.qrsof.empresalit.domain.status.StatusGateway
import com.qrsof.empresalit.domain.task_resources.TaskResourcesGateway
import com.qrsof.empresalit.domain.users.{UsersGateway, UsersGatewayImpl}
import com.qrsof.empresalit.repositories.addresses.{AddressesRepository, AddressesRepositoryImpl}
import com.qrsof.empresalit.repositories.brand_company.{BrandCompanyRepository, BrandCompanyRepositoryImpl}
import com.qrsof.empresalit.repositories.clients.{ClientGatewayImpl, ClientsRepository, ClientsRepositoryImpl}
import com.qrsof.empresalit.repositories.companies.{CompaniesRepository, CompaniesRepositoryImp}
import com.qrsof.empresalit.repositories.company_certificates.{CompanyCertificatesRepository, CompanyCertificatesRepositoryImpl}
import com.qrsof.empresalit.repositories.days_to_request.{DaysToRequestRepository, DaysToRequestRepositoryImpl}
import com.qrsof.empresalit.repositories.debs_to_pay.{DebtsToPayRepository, DebtsToPayRepositoryImpl}
import com.qrsof.empresalit.repositories.debts_receivable.{DebtsReceivablesRepository, DebtsReceivablesRepositoryImpl}
import com.qrsof.empresalit.repositories.employee.{EmployeeRepository, EmployeeRepositoryImpl}
import com.qrsof.empresalit.repositories.employee_resources.{EmployeeResourcesRepository, EmployeeResourcesRepositoryImpl}
import com.qrsof.empresalit.repositories.group_tasks.{GroupTaskRepository, GroupTaskRepositoryImpl}
import com.qrsof.empresalit.repositories.holidays.{HolidaysRepository, HolidaysRepositoryImpl}
import com.qrsof.empresalit.repositories.inabilities_and_vacations.{InabilityAndVacationRepository, InabilityAndVacationRepositoryImpl}
import com.qrsof.empresalit.repositories.jobs.{JobsRepository, JobsRepositoryImpl}
import com.qrsof.empresalit.repositories.kanbanBoard.{KanbanBoardRepository, KanbanBoardRepositoryImpl}
import com.qrsof.empresalit.repositories.logbook.{LogbookGatewayImpl, LogbookRepository, LogbookRepositoryImpl}
import com.qrsof.empresalit.repositories.quotations.{QuoteRepository, QuoteRepositoryImpl}
import com.qrsof.empresalit.repositories.resources.{ResourcesGatewayImpl, ResourcesRepository, ResourcesRepositoryImpl}
import com.qrsof.empresalit.repositories.seniority_benefits.{SeniorityBenefitsRepository, SeniorityBenefitsRepositoryImpl}
import com.qrsof.empresalit.repositories.status.{StatusGatewayImpl, StatusRepository, StatusRepositoryImpl}
import com.qrsof.empresalit.repositories.stores.{StoresRepository, StoresRepositoryImpl}
import com.qrsof.empresalit.repositories.suppliers.{SuppliersRepository, SuppliersRepositoryImpl}
import com.qrsof.empresalit.repositories.task_resources.{TaskResourcesGatewayImpl, TaskResourcesRepository, TaskResourcesRepositoryImpl}
import com.qrsof.empresalit.repositories.tasks.{TaskRepository, TaskRepositoryImpl}
import com.qrsof.empresalit.repositories.users.{UserRepository, UserRepositoryImpl}
import com.qrsof.empresalit.repositories.users_companies.{UserCompaniesRepository, UserCompaniesRepositoryImpl}
import com.qrsof.empresalit.views.maincontainer.clients.{ViewClientsGateway, ViewClientsGatewayImpl}
import com.qrsof.empresalit.views.maincontainer.companies.{ViewCompaniesGateway, ViewCompaniesGatewayImpl}
import com.qrsof.empresalit.views.maincontainer.debtsReceivable.ViewDebtsReceivableGatewayImpl
import com.qrsof.empresalit.views.maincontainer.debtsReceivables.ViewDebtsReceivableGateway
import com.qrsof.empresalit.views.maincontainer.debtsToPay.{ViewDebtToPayGateway, ViewDebtsToPayGatewayImpl}
import com.qrsof.empresalit.views.maincontainer.employee.{ViewEmployeeGateway, ViewEmployeeGatewayImpl}
import com.qrsof.empresalit.views.maincontainer.jobs.{ViewJobsGateway, ViewJobsGatewayImpl}
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.{ViewKanbanBoardGateWayImpl, ViewKanbanBoardGateway, ViewTaskConfirmationGateway, ViewTaskConfirmationGatewayImpl}
import com.qrsof.empresalit.views.maincontainer.presenteeism.{ViewPresenteeismGateway, ViewPresenteeismGatewayImpl}
import com.qrsof.empresalit.views.maincontainer.quotations.ViewQuoteGatewayImpl
import com.qrsof.empresalit.views.maincontainer.quote.ViewQuoteGateway
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.{ViewReceptionOfEquipmentGateway, ViewReceptionOfEquipmentGatewayImpl}
import com.qrsof.empresalit.views.maincontainer.store.ViewStoresGatewayImpl
import com.qrsof.empresalit.views.maincontainer.stores.ViewStoresGateway
import com.qrsof.empresalit.views.maincontainer.supplier.actions.gw.ViewSuppliersGatewayImpl
import com.qrsof.empresalit.views.maincontainer.suppliers.ViewSuppliersGateway
import com.qrsof.empresalit.views.maincontainer.{ViewMainContainerGateway, ViewMainContainerGatewayImpl}
import com.qrsof.empresalit.views.onboarding.{ViewOnboardingGateway, ViewOnboardingGatewayImpl}
import jakarta.inject.{Named, Singleton}
import net.codingwell.scalaguice.ScalaModule
import com.qrsof.core.storage.digitalocean.DigitalOceanStorageConfig

import java.util.Properties

class DataModule(appConfigurations: AppConfigurations) extends AbstractModule with ScalaModule {

  override def configure(): Unit = {
    bind[QueryExecutor]
    bind[CompanyGateway].to[CompanyGatewayImpl]
    bind[QrEvolutionResources].to[QrEvolutionResourcesEmpresalit]
    bind[UsersGateway].to[UsersGatewayImpl].in(classOf[Singleton])
    bind[UserRepository].to[UserRepositoryImpl].in(classOf[Singleton])
    bind[CompaniesRepository].to[CompaniesRepositoryImp].in(classOf[Singleton])
    bind[AddressesRepository].to[AddressesRepositoryImpl].in(classOf[Singleton])
    bind[UserCompaniesRepository].to[UserCompaniesRepositoryImpl].in(classOf[Singleton])
    bind[CompanyCertificatesRepository].to[CompanyCertificatesRepositoryImpl].in(classOf[Singleton])
    bind[DomainCompanyGateway].to[DomainCompanyGatewayImpl].in(classOf[Singleton])
    bind[ViewOnboardingGateway].to[ViewOnboardingGatewayImpl].in(classOf[Singleton])
    bind[ViewMainContainerGateway].to[ViewMainContainerGatewayImpl].in(classOf[Singleton])
    bind[SuppliersRepository].to[SuppliersRepositoryImpl].in(classOf[Singleton])
    bind[DebtsToPayRepository].to[DebtsToPayRepositoryImpl].in(classOf[Singleton])
    bind[ViewDebtToPayGateway].to[ViewDebtsToPayGatewayImpl].in(classOf[Singleton])
    bind[ViewSuppliersGateway].to[ViewSuppliersGatewayImpl].in(classOf[Singleton])
    bind[ClientsRepository].to[ClientsRepositoryImpl].in(classOf[Singleton])
    bind[DebtsReceivablesRepository].to[DebtsReceivablesRepositoryImpl].in(classOf[Singleton])
    bind[ViewDebtsReceivableGateway].to[ViewDebtsReceivableGatewayImpl].in(classOf[Singleton])
    bind[AddressGateway].to[AddressGatewayImpl].in(classOf[Singleton])
    bind[ViewClientsGateway].to[ViewClientsGatewayImpl].in(classOf[Singleton])
    bind[ViewCompaniesGateway].to[ViewCompaniesGatewayImpl].in(classOf[Singleton])
    bind[ViewEmployeeGateway].to[ViewEmployeeGatewayImpl].in(classOf[Singleton])
    bind[EmployeeRepository].to[EmployeeRepositoryImpl].in(classOf[Singleton])
    bind[ViewJobsGateway].to[ViewJobsGatewayImpl].in(classOf[Singleton])
    bind[JobsRepository].to[JobsRepositoryImpl].in(classOf[Singleton])
    bind[ViewStoresGateway].to[ViewStoresGatewayImpl].in(classOf[Singleton])
    bind[StoresRepository].to[StoresRepositoryImpl].in(classOf[Singleton])
    bind[ViewQuoteGateway].to[ViewQuoteGatewayImpl].in(classOf[Singleton])
    bind[QuoteRepository].to[QuoteRepositoryImpl].in(classOf[Singleton])
    bind[ViewReceptionOfEquipmentGateway].to[ViewReceptionOfEquipmentGatewayImpl].in(classOf[Singleton])
    bind[GroupTaskRepository].to[GroupTaskRepositoryImpl].in(classOf[Singleton])
    bind[ResourcesGateway].to[ResourcesGatewayImpl].in(classOf[Singleton])
    bind[ResourcesRepository].to[ResourcesRepositoryImpl].in(classOf[Singleton])
    bind[ViewKanbanBoardGateway].to[ViewKanbanBoardGateWayImpl].in(classOf[Singleton])
    bind[KanbanBoardRepository].to[KanbanBoardRepositoryImpl].in(classOf[Singleton])
    bind[ClientGateway].to[ClientGatewayImpl].in(classOf[Singleton])
    bind[LogbookRepository].to[LogbookRepositoryImpl].in(classOf[Singleton])
    bind[TaskResourcesRepository].to[TaskResourcesRepositoryImpl].in(classOf[Singleton])
    bind[TaskResourcesGateway].to[TaskResourcesGatewayImpl].in(classOf[Singleton])
    bind[StatusRepository].to[StatusRepositoryImpl].in(classOf[Singleton])
    bind[StatusGateway].to[StatusGatewayImpl].in(classOf[Singleton])
    bind[ViewTaskConfirmationGateway].to[ViewTaskConfirmationGatewayImpl].in(classOf[Singleton])
    bind[LogbookGateway].to[LogbookGatewayImpl].in(classOf[Singleton])
    bind[TaskRepository].to[TaskRepositoryImpl].in(classOf[Singleton])
    bind[EmployeeResourcesRepository].to[EmployeeResourcesRepositoryImpl].in(classOf[Singleton])
    bind[EmployeeResourcesGateway].to[EmployeeResourcesGatewayImpl].in(classOf[Singleton])
    bind[EmployeesGateway].to[EmployeesGatewayImpl].in(classOf[Singleton])
    bind[SeniorityBenefitsRepository].to[SeniorityBenefitsRepositoryImpl].in(classOf[Singleton])
    bind[SeniorityBenefitsGateway].to[SeniorityBenefitsGatewayImpl].in(classOf[Singleton])
    bind[InabilityAndVacationRepository].to[InabilityAndVacationRepositoryImpl].in(classOf[Singleton])
    bind[DaysToRequestRepository].to[DaysToRequestRepositoryImpl].in(classOf[Singleton])
    bind[ViewPresenteeismGateway].to[ViewPresenteeismGatewayImpl].in(classOf[Singleton])
    bind[BrandCompanyRepository].to[BrandCompanyRepositoryImpl].in(classOf[Singleton])
    bind[BrandCompanyGateway].to[BrandCompanyGatewayImpl].in(classOf[Singleton])
    bind[HolidaysRepository].to[HolidaysRepositoryImpl].in(classOf[Singleton])
    bind[HoliDaysGateway].to[HolidaysGatewayImpl].in(classOf[Singleton])

    if (appConfigurations.environment.equals(Development) || appConfigurations.environment.equals(Sandbox) || appConfigurations.environment.equals(Production)) {
      install(new DatabaseEvolutionModule())
    }

  }

  @Provides
  def getDatabase(): Database = {
    if (appConfigurations.environment.equals(Test)) {
      Database.forConfig("");
    } else {
      Database.forConfig("db.postgres")
    }
  }

  @Provides
  @Singleton
  @Named("jdbc-properties") def getSessionFactory(database: Database): Properties = {

    val schemaName = appConfigurations.dbSchemaName
    val properties = new Properties
    properties.put("hibernate.default_schema", schemaName)
    properties.put("hibernate.show_sql", true)
    properties.put("hibernate.format_sql", true)
    properties.put("hibernate.highlight_sql", true)
    properties
  }

  @Provides
  def digitalOceanStorageConfig(databaseApp: Database): DigitalOceanStorageConfig = {
    new DigitalOceanStorageConfig(
      appConfigurations.digitalOceanConfigs.region,
      appConfigurations.digitalOceanConfigs.secretKey,
      appConfigurations.digitalOceanConfigs.accessKey,
      appConfigurations.digitalOceanConfigs.bucketName,
      databaseApp.source.createConnection(),
      Some(appConfigurations.digitalOceanConfigs.schema)
    )
  }

  @Provides
  def fileSystemStorageConfig(database: Database): FileSystemStorageConfig = {
    FileSystemStorageConfig(
      basePath = "./localstorage",
      connection = database.source.createConnection(),
      schema = Some(appConfigurations.dbSchemaName)
    )
  }

}
