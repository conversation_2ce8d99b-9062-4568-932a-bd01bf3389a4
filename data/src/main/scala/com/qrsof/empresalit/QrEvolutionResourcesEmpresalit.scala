package com.qrsof.empresalit

import com.qrsof.core.evolution.database.QrEvolutionResources
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import jakarta.inject.{Inject, Singleton}

import java.sql.Connection

@Singleton
class QrEvolutionResourcesEmpresalit @Inject()(appEnvironment: AppEnvironment, database: Database) extends QrEvolutionResources {

  override def schema: Option[String] = Some("empresalit")

  override def changeLog: String = {
    "liquibase/master.yaml"
  }

  override def context: Seq[String] = {
    appEnvironment match {
      case AppEnvironment.Production => Seq("prod")
      case AppEnvironment.Development => Seq("dev")
      case AppEnvironment.Sandbox => Seq("sandbox")
      case AppEnvironment.Test => Seq("test")
    }
  }

  override def connection: Connection = {
    database.source.createConnection()
  }

}
