package com.qrsof.empresalit.views.maincontainer.debtsReceivable

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.clients.{ClientsRepository, ClientsTable}
import com.qrsof.empresalit.repositories.debts_receivable.{DebtsReceivablesEntity, DebtsReceivablesRepository, DebtsReceivablesTable}
import com.qrsof.empresalit.views.maincontainer.debtsReceivables.ViewDebtsReceivableGateway
import com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.*
import io.scalaland.chimney.dsl._
import jakarta.inject.{Inject, Singleton}
import slick.jdbc.JdbcType

import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.util.{Calendar, Date, UUID}

@Singleton
class ViewDebtsReceivableGatewayImpl @Inject()
(debtsReceivablesRepository: DebtsReceivablesRepository,
 clientsRepository: ClientsRepository)
(implicit queryExecutor: QueryExecutor)
  extends ViewDebtsReceivableGateway {
  override def getDebtsReceivable(filters: Filters): DebtsReceivableWithTotal = {
    implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
      d => new Timestamp(d.getTime),
      ts => new Date(ts.getTime)
    )
    implicit val paymentStatusType: JdbcType[PaymentStatus] = MappedColumnType.base[PaymentStatus, String](
      en => en.entryName,
      s => PaymentStatus.withName(s)
    )
    val dateFormat = new SimpleDateFormat("dd-MM-yyyy")
    val calendar = Calendar.getInstance()

    val offset: Int = filters.page * filters.pageSize
    val startDate: Option[Date] = filters.startDate.map(startDate => dateFormat.parse(startDate))
    val endDate: Option[Date] = filters.endDate.map { endDateString =>
      val date = dateFormat.parse(endDateString)
      calendar.setTime(date)
      calendar.set(Calendar.HOUR_OF_DAY, 23)
      calendar.set(Calendar.MINUTE, 59)
      calendar.set(Calendar.SECOND, 59)
      new Date(calendar.getTimeInMillis)
    }

    val query = for {
      client <- ClientsTable.tableQuery if
        client.company_key === filters.company_Key &&
          filters.client_key.fold[Rep[Boolean]](true)(key => client.client_key === key) && (
          filters.searchBar.fold[Rep[Boolean]](true)({ name =>
            val nameLowerCase = client.name.toLowerCase
            val searchLowerCase = name.toLowerCase
            nameLowerCase like ("%" + searchLowerCase + "%")
          }) ||
            filters.searchBar.fold[Rep[Boolean]](true) { rfc =>
              val searchTermUpperCase = rfc.toUpperCase
              client.rfc like ("%" + searchTermUpperCase + "%")
            })
      debts <- DebtsReceivablesTable.tableQuery if
        debts.client_key === client.client_key &&
          filters.paymenStatus.fold[Rep[Boolean]](true)(status => debts.paymentStatus === status) &&
          startDate.fold[Rep[Boolean]](true)(fecha => debts.insertedAt >= fecha) &&
          endDate.fold[Rep[Boolean]](true)(fecha => debts.insertedAt <= fecha)
    } yield (client, debts)

    val total: Int = queryExecutor.syncExecuteQuery {
      query.length.result
    }

    val debts = queryExecutor.syncExecuteQuery {
      query.drop(offset).take(filters.pageSize).result
    }

    DebtsReceivableWithTotal(
      debtsWithClients = debts.map {
        debt =>
          DebtsWithClients(
            debt_receivable_key = debt._2.debt_receivable_key,
            amount = debt._2.amount,
            inserted_at = debt._2.insertedAt.toString,
            name = debt._1.name,
            regimen_fiscal_clave = debt._1.regimen_fiscal_clave,
            paymentStatus = debt._2.paymentStatus,
            rfc = debt._1.rfc
          )
      },
      total
    )
  }

  override def newDebtReceivable(debtReceivable: DebtReceivable): DebtReceivableResponse = {
    val debt_entity = debtsReceivablesRepository.newDebtReceivable(
      DebtsReceivablesEntity(
        UUID.randomUUID().toString,
        debtReceivable.amount,
        debtReceivable.company_key,
        debtReceivable.client_key,
        new Date(),
        new Date(),
        debtReceivable.paymentStatus
      )
    )
    DebtReceivableResponse(
      debt_entity.debt_receivable_key
    )
  }

  override def findDebt(debt_key: String): Option[DebtReceivableEntRes] = {
    debtsReceivablesRepository.findDebt(debt_key).map(_.transformInto[DebtReceivableEntRes])
  }

  override def deleteDebt(debt_key: String): DebtReceivableResponse = {
    debtsReceivablesRepository.deleteDebt(debt_key)

    DebtReceivableResponse(debt_key)
  }

  def markAsPaid(debt: DebtReceivableEntRes): DebtReceivableResponse = {
    debtsReceivablesRepository.markAsPaid(
      debt.into[DebtsReceivablesEntity].withFieldConst(_.paymentStatus, PaymentStatus.pagado).transform
    )
    DebtReceivableResponse(
      debt.debt_receivable_key
    )
  }
}
