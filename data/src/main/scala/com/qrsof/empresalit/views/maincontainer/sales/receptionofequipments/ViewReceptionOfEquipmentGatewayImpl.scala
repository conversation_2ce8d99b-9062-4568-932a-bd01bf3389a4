package com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.clients.{ClientsEntity, ClientsTable}
import com.qrsof.empresalit.repositories.group_task_resources.task_resources.GroupTaskResourceEntity
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.pojos.{ClientByReceptionOfEquipment, GroupTaskRequest, ReceptionOfEquipment, ReceptionOfEquipmentFilter}
import com.qrsof.empresalit.repositories.group_tasks.{GroupTaskEntity, GroupTaskRepository}
import com.qrsof.empresalit.repositories.task_resources.TaskResourceEntity
import com.qrsof.empresalit.repositories.tasks.TaskEntity
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

@Singleton
class ViewReceptionOfEquipmentGatewayImpl @Inject() (equipmentGroupRepository: GroupTaskRepository)(implicit queryExecutor: QueryExecutor) extends ViewReceptionOfEquipmentGateway {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewReceptionOfEquipmentGatewayImpl])

  override def getClientsByCompanyKey(companyKey: String): Seq[ClientByReceptionOfEquipment] = {
    val clients: Seq[ClientsEntity] = queryExecutor.syncExecuteQuery(ClientsTable.findListByProperty(_.company_key === companyKey))
    clients.map { client =>
      ClientByReceptionOfEquipment(client.client_key, client.name)
    }
  }

  override def newReceptionOfEquipment(groupTask: GroupTaskRequest): Unit = {
    val groupTaskEntity: GroupTaskEntity = GroupTaskEntity(groupTask.groupTaskKey, groupTask.companyKey, groupTask.clientKey, groupTask.folio, groupTask.entryDate, None)
    val groupTaskResources: Seq[GroupTaskResourceEntity] = groupTask.groupTaskResources.map { resource =>
      GroupTaskResourceEntity(resource.groupTaskResourceKey, resource.groupTaskKey, resource.reference.get)
    }
    val tasks: Seq[TaskEntity] = groupTask.tasks.map { task =>
      TaskEntity(
        taskKey = task.taskKey,
        groupTaskKey = task.groupTaskKey,
        statusKey = task.statusKey,
        title = task.name,
        quantity = task.quantity,
        observations = task.observations,
        entryDate = task.entryDate,
        outputDate = None,
        responsible = task.responsible
      )
    }
    val taskResources: Seq[TaskResourceEntity] = groupTask.taskResources.map { resource =>
      TaskResourceEntity(resource.taskResourceKey, resource.taskKey, resource.reference.get)
    }
    equipmentGroupRepository.newEquipmentGroupRepository(groupTaskEntity, groupTaskResources, tasks, taskResources)
  }

  override def getTotalReceptionOfEquipment(companyKey: String, receptionOfEquipmentFilter: ReceptionOfEquipmentFilter): Int = {
    equipmentGroupRepository.getTotalReceptionOfEquipment(companyKey)
  }

  override def getReceptionOfEquipment(companyKey: String, receptionOfEquipmentFilter: ReceptionOfEquipmentFilter, pageNumber: Int, pageSize: Int): Seq[ReceptionOfEquipment] = {
    var filter = receptionOfEquipmentFilter;
    if (filter.text.isDefined) {
      filter = filter.copy(text = Some("%".concat(receptionOfEquipmentFilter.text.get.toLowerCase).concat("%")))
    }
    logger.info("getReceptionOfEquipment::filter: {}", filter)
    equipmentGroupRepository.getReceptionOfEquipment(companyKey, filter, pageNumber, pageSize)
  }
}
