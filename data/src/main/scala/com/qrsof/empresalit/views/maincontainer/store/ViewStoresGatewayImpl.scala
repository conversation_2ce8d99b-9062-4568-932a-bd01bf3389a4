package com.qrsof.empresalit.views.maincontainer.store

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.stores.{StoresEntity, StoresRepository, StoresTable}
import com.qrsof.empresalit.views.maincontainer.stores.ViewStoresGateway
import com.qrsof.empresalit.views.maincontainer.stores.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}

import java.sql.Timestamp
import java.util.{Date, UUID}

@Singleton
class ViewStoresGatewayImpl @Inject() (storesRepository: StoresRepository)(implicit queryExecutor: QueryExecutor) extends ViewStoresGateway {

  override def addStore(newStoreData: NewStoreData): NewStoreResponse = {
    val newStore = storesRepository.save(
      StoresEntity(
        UUID.randomUUID().toString,
        newStoreData.company_key,
        newStoreData.code,
        newStoreData.name,
        newStoreData.country,
        newStoreData.state,
        newStoreData.city,
        newStoreData.direction,
        newStoreData.cellphone,
        new Date(),
        new Date()
      )
    )
    NewStoreResponse(newStore.store_key)
  }

  override def getStore(filtersStore: FiltersStore): Stores = {
    MappedColumnType.base[Date, Timestamp](
      d => new Timestamp(d.getTime),
      ts => new Date(ts.getTime)
    )

    val offset: Int = filtersStore.page * filtersStore.pageSize;
    val query = for {
      store <- StoresTable.tableQuery if store.company_key === filtersStore.company_key &&
        filtersStore.company_key.fold[Rep[Boolean]](true)(key => store.company_key === key) && (filtersStore.searchBar.fold[Rep[Boolean]](true)({ name =>
          val nameLowerCase = store.name.toLowerCase
          val searchLowerCase = name.toLowerCase
          nameLowerCase like ("%" + searchLowerCase + "%")
        }) ||
          filtersStore.searchBar.fold[Rep[Boolean]](true) { code =>
            val codeLowerCase = store.code.toLowerCase
            val searchLowerCase = code.toLowerCase
            codeLowerCase like ("%" + searchLowerCase + "%")
          })
    } yield (store)

    val total: Int = queryExecutor.syncExecuteQuery {
      query.length.result
    }
    val stores = queryExecutor.syncExecuteQuery {
      query.drop(offset).take(filtersStore.pageSize).result
    }
    Stores(
      storeData = stores.map { store =>
        Store(
          store_key = store.store_key,
          code = store.code,
          name = store.name,
          country = store.country,
          state = store.state,
          city = store.city,
          direction = store.direction,
          cellphone = store.cellphone
        )
      },
      total
    )
  }

  override def findStore(store_key: String): Option[StoreEnt] = {
    storesRepository.findStore(store_key).map(_.transformInto[StoreEnt])
  }

  override def updateStore(generalDto: GeneralDtoInStoreActionRequest): Either[StoreUpdateException, SuccessUpdateStore] = {
    val query = for {
      store <- StoresTable.tableQuery if store.store_key === generalDto.store_key
    } yield (store)

    val result = queryExecutor.syncExecuteQuery(
      query.result.headOption
    )

    result match {
      case Some((store)) =>
        val newStoreEnt = StoresEntity(
          generalDto.store_key,
          store.company_key,
          generalDto.code,
          generalDto.name,
          generalDto.country,
          generalDto.state,
          generalDto.city,
          generalDto.direction,
          generalDto.cellphone,
          store.inserted_at,
          new Date()
        )
        storesRepository.updateStore(newStoreEnt)
        Right(SuccessUpdateStore("UPDATED"))

      case None =>
        Left(StoreNotFoundExceptionUpdate(generalDto.store_key))
    }
  }

  override def deleteStore(store_key: String): StoreKey = {
    storesRepository.deleteStore(store_key)
    StoreKey(
      store_key
    )
  }
}
