package com.qrsof.empresalit.views.maincontainer.clients

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.addresses.AddressesTable
import com.qrsof.empresalit.repositories.clients.{ClientsEntity, ClientsRepository, ClientsTable}
import com.qrsof.empresalit.views.maincontainer.clients.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import java.util.{Date, UUID}

@Singleton
class ViewClientsGatewayImpl @Inject() (clientsRepository: ClientsRepository)(implicit queryExecutor: QueryExecutor) extends ViewClientsGateway {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewClientsGatewayImpl])

  override def newClient(client: Client): ClientResponse = {
    logger.info(s"Client: ->{}", client)
    val newClient = clientsRepository.newClient(
      ClientsEntity(
        UUID.randomUUID().toString,
        client.clientData.name,
        client.clientData.regimen_fiscal_clave,
        client.clientData.rfc,
        Some(client.clientData.email),
        company_key = client.company_key,
        address_key = client.address_key,
        new Date(),
        new Date()
      )
    )
    ClientResponse(newClient.client_key)
  }

  override def findClientByRfc(rfc: String): Option[ClientEnti] = {
    clientsRepository.getByRfc(rfc).map(_.into[ClientEnti].transform)
  }

  override def findClientByKey(clientKey: String): Option[ClientEnti] = {
    clientsRepository.getByKey(clientKey).map(_.into[ClientEnti].transform)
  }

  override def deleteClient(clientKey: String): ClientResponse = {
    clientsRepository.deleteClient(clientKey)
    ClientResponse(
      clientKey
    )
  }

  override def getClients(filters: FiltersClientsActionRequest): ClientsResponse = {
    logger.info("ViewClienteGatewayImpl::getAllClients::company_key->{}", filters)
    val offset: Int = filters.page * filters.pageSize

    val query = for {
      client <- ClientsTable.tableQuery
        .filter(_.company_key === filters.companyKey)
        .filterOpt(filters.searchBar)((clients, filterTerm) =>
          (clients.name.toLowerCase like ("%" + filterTerm.toLowerCase + "%")) || (clients.rfc.toLowerCase like ("%" + filterTerm.toLowerCase + "%"))
        )

//      if client.company_key === filters.companyKey &&
//        filters.searchBar.fold[Rep[Boolean]](true)(name => client.name like ("%" + name + "%")) ||
//        filters.searchBar.fold[Rep[Boolean]](true)(rfc => client.rfc like ("%" + rfc + "%")) ||
//        filters.searchBar.fold[Rep[Boolean]](true)(name => client.name.toLowerCase like ("%" + name + "%")) ||
//        filters.searchBar.fold[Rep[Boolean]](true)(rfc => client.rfc.toLowerCase like ("%" + rfc + "%")) ||
//        filters.searchBar.fold[Rep[Boolean]](true)(name => client.name.toUpperCase like ("%" + name + "%")) ||
//        filters.searchBar.fold[Rep[Boolean]](true)(rfc => client.rfc.toUpperCase like ("%" + rfc + "%"))
      address <- AddressesTable.tableQuery if client.address_key === address.address_key
    } yield (client, address)

    val total: Int = queryExecutor.syncExecuteQuery(query.length.result)

    val clients = queryExecutor.syncExecuteQuery {
      query.drop(offset).take(filters.pageSize).result
    }

    ClientsResponse(
      clients.map { client =>
        ClientDataResponse(
          client_key = client._1.client_key,
          name = client._1.name,
          regimen_fiscal_clave = client._1.regimen_fiscal_clave,
          rfc = client._1.rfc,
          company_key = client._1.company_key,
          estado = client._2.estado,
          municipio = client._2.municipio,
          calle = client._2.calle,
          numero_exterior = client._2.numero_exterior,
          fecha_de_creacion = client._1.insertedAt
        )
      },
      total
    )
  }

  override def getAllClients(company_key: Option[String]): ClientsResponse = {
    val query = for {
      client <- ClientsTable.tableQuery if client.company_key === company_key.get
      address <- AddressesTable.tableQuery if client.address_key === address.address_key
    } yield (client, address)

    val total: Int = queryExecutor.syncExecuteQuery(query.length.result)

    val clients = queryExecutor.syncExecuteQuery {
      query.result
    }

    ClientsResponse(
      clients.map { client =>
        ClientDataResponse(
          client_key = client._1.client_key,
          name = client._1.name,
          regimen_fiscal_clave = client._1.regimen_fiscal_clave,
          rfc = client._1.rfc,
          company_key = client._1.company_key,
          estado = client._2.estado,
          municipio = client._2.municipio,
          calle = client._2.calle,
          numero_exterior = client._2.numero_exterior,
          fecha_de_creacion = client._1.insertedAt
        )
      },
      total
    )
  }
}
