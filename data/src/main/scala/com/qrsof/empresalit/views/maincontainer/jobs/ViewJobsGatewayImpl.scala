package com.qrsof.empresalit.views.maincontainer.jobs

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.jobs.{JobsEntity, JobsRepository, JobsTable}
import com.qrsof.empresalit.views.maincontainer.jobs.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}

import java.sql.Timestamp
import java.util.{Date, UUID}

@Singleton
class ViewJobsGatewayImpl @Inject() (jobsRepository: JobsRepository)(implicit queryExecutor: QueryExecutor) extends ViewJobsGateway {
  override def getJobs(listJobsActionRequest: ListJobsActionRequest): JobsWithTotal = {
    MappedColumnType.base[Date, Timestamp](
      d => new Timestamp(d.getTime),
      ts => new Date(ts.getTime)
    )
    val query = for {
      job <- JobsTable.tableQuery if job.company_key === listJobsActionRequest.company_key
    } yield (job)

    val total: Int = queryExecutor.syncExecuteQuery {
      query.length.result
    }

    val jobs = queryExecutor.syncExecuteQuery {
      query.result
    }

    println(s"Resultados de la consulta: $jobs")

    JobsWithTotal(
      jobsWithEmployee = jobs.map { job =>
        JobsWithEmployee(
          job_key = job.job_key,
          company_key = job.company_key,
          name = job.name
        )
      },
      total
    )
  }

  override def addJobs(jobsData: JobsData): JobResponse = {
    val newJob = jobsRepository.save(
      JobsEntity(
        UUID.randomUUID().toString,
        jobsData.company_key,
        jobsData.name,
        new Date(),
        new Date()
      )
    )
    JobResponse(newJob.job_key)
  }

  override def deleteJobs(employee_key: String): JobKey = {
    jobsRepository.deleteJobs(employee_key)
    JobKey(
      employee_key
    )
  }

  override def updateJobs(jobsEnt: JobsEnt): JobKey = {
    jobsRepository.updateJobs(
      jobsEnt.into[JobsEntity].withFieldConst(_.name, jobsEnt.name).transform
    )
    JobKey(
      jobsEnt.name
    )
  }

  override def findJobs(jobsKey: String): Option[JobsEnt] = {
    jobsRepository.findJob(jobsKey).map(_.transformInto[JobsEnt])
  }
}
