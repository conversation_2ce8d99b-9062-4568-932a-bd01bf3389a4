package com.qrsof.empresalit.views.maincontainer.kanbanBoard

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.domain.resources.pojos.ResourceRequest
import com.qrsof.empresalit.repositories.clients.{ClientsEntity, ClientsTable}
import com.qrsof.empresalit.repositories.employee.{EmployeeEntity, EmployeeTable}
import com.qrsof.empresalit.repositories.group_tasks.{GroupTaskEntity, GroupTaskTable}
import com.qrsof.empresalit.repositories.kanbanBoard.KanbanBoardRepository
import com.qrsof.empresalit.repositories.logbook.LogbookRepository
import com.qrsof.empresalit.repositories.resources.{ResourceEntity, ResourcesRepository}
import com.qrsof.empresalit.repositories.status.{StatusEntity, StatusTable}
import com.qrsof.empresalit.repositories.tasks.{TaskEntity, TaskRepository, TaskTable}
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}
import play.api.libs.json.Json

import scala.concurrent.ExecutionContext

@Singleton
class ViewKanbanBoardGateWayImpl @Inject() (
    kanbanRepository: KanbanBoardRepository,
    logbookRepository: LogbookRepository,
    resourcesRepository: ResourcesRepository,
    taskRepository: TaskRepository
)(implicit
    queryExecutor: QueryExecutor,
    executionContext: ExecutionContext
) extends ViewKanbanBoardGateway {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewKanbanBoardGateWayImpl])

  private def getStatus(companyKey: String): Seq[StatusListResponse] = {
    val statusList = kanbanRepository
      .getAllStatusByCompany(companyKey)
      .map { status =>
        StatusListResponse(
          statusKey = status.status_key,
          status = status.status,
          order = status.order_process
        )
      }
      .sortBy(status => status.order)
    statusList
  }

  override def getAllTasksAndStatus(company_key: String): KanbanTasksResponse = {

    val allTasks = getTasksByCompanyKeyAndFolio(company_key, Some(""))

    KanbanTasksResponse(Seq.empty, allTasks.length)
  }

  override def getKanbanTasksFiltered(filters: FiltersKanbanActionRequest): KanbanTasksResponse = {
    val statusList = getStatus(filters.companyKey)
    val tasks = getTasksByCompanyKeyAndFolio(companyKey = filters.companyKey, term = filters.term)
    val tasksAndStatus: Seq[StatusAndTasks] = statusList.map(status => {
      StatusAndTasks(statusKey = status.statusKey, status = status.status, order = status.order, tasks = tasks.filter(_.statusKey == status.statusKey))
    })

    KanbanTasksResponse(statusAndTasks = tasksAndStatus, total = tasks.length)

  }

  override def getLogbookAndAttachmentsData(companyKey: String, taskKey: String): LogbookAndAttachmentData = {
    val logbookTask: Seq[LogbookResponse] = logbookRepository
      .getLogbookByTaskKey(taskKey)
      .map(logbook => {
        val keyAttachments = Json.parse(logbook.keyAttachments).as[Seq[String]]
        val logbookAttachments: Seq[ResourceRequest] = keyAttachments.map(resourceKey => {
          resourcesRepository.getResource(resourceKey = resourceKey).map(_.transformInto[ResourceRequest]).get
        })

        LogbookResponse(
          autor = logbook.author,
          keyAttachments = keyAttachments,
          logbookAttachments = logbookAttachments,
          createdAt = logbook.createdAt,
          logbookKey = logbook.logbookKey,
          logbookType = logbook.logbookType,
          payload = logbook.payload,
          taskKey = logbook.taskKey
        )
      })
      .sortBy(_.createdAt)
      .reverse
    LogbookAndAttachmentData(logbookTask)
  }

  override def assignTaskGateway(companyKey: String, updatedTask: Task): Either[TaskException, Unit] = {
    val query = for {
      task <- TaskTable.tableQuery if task.taskKey === updatedTask.taskKey
    } yield task

    val result = queryExecutor.syncExecuteQuery(query.result.headOption)
    result match {
      case Some(task) =>
        val taskEntity: TaskEntity = TaskEntity(
          taskKey = updatedTask.taskKey,
          groupTaskKey = updatedTask.groupTaskKey,
          statusKey = Some(updatedTask.statusKey),
          title = updatedTask.title,
          quantity = updatedTask.quantity,
          observations = updatedTask.observations,
          entryDate = updatedTask.entryDate,
          outputDate = updatedTask.outputDate,
          responsible = updatedTask.responsible
        )
        taskRepository.updateTaskAssign(taskEntity)
        Right(())
      case None =>
        Left(TaskExceptionError(updatedTask.taskKey))
    }
  }

  private def getTasksByCompanyKeyAndFolio(companyKey: String, term: Option[String]): Seq[Task] = queryExecutor.syncExecuteQuery {
    //    logger.info(s"getTasksByCompanyKeyAndFolio::companyKey -> {}", companyKey)
    //    logger.info(s"getTasksByCompanyKeyAndFolio:: term -> {}", term)
    (for {
      groupTask <- GroupTaskTable.tableQuery.filter(_.companyKey === companyKey)
      task <- TaskTable.tableQuery.filter(_.groupTaskKey === groupTask.groupTaskKey)
      client <- ClientsTable.tableQuery.filter(_.client_key === groupTask.clientKey)
      (task2, status) <- TaskTable.tableQuery joinLeft StatusTable.tableQuery on (_.statusKey === _.status_key)
      (task3, responsible) <- TaskTable.tableQuery joinLeft EmployeeTable.tableQuery on (_.responsible === _.employeeKey)
      if task2.taskKey === task.taskKey && task3.taskKey === task.taskKey
    } yield (groupTask, task, client, status, responsible))
      .distinctOn(_._2.taskKey)
      .filterOpt(term)((data, termToSearch) => {
        // logger.info(s"Filtering with term: ${t.toUpperCase}")
        (data._1.folio.toLowerCase like ("%" + termToSearch.toLowerCase + "%")) || (data._3.name.toLowerCase like ("%" + termToSearch.toLowerCase + "%"))
      })
      .result
      .map({ _.map((value: (GroupTaskEntity, TaskEntity, ClientsEntity, Option[StatusEntity], Option[EmployeeEntity])) => { buildData(value) }) })
  }

  private def getAllTasksWithFilters(filters: FiltersKanbanActionRequest): Seq[Task] = queryExecutor.syncExecuteQuery {
    val query = (for {
      groupTask <- GroupTaskTable.tableQuery.filter(_.companyKey === filters.companyKey)
      task <- TaskTable.tableQuery.filter(_.groupTaskKey === groupTask.groupTaskKey)
      client <- ClientsTable.tableQuery.filter(_.client_key === groupTask.clientKey)
      (task2, status) <- TaskTable.tableQuery joinLeft StatusTable.tableQuery on (_.statusKey === _.status_key)
      (task3, responsible) <- TaskTable.tableQuery joinLeft EmployeeTable.tableQuery on (_.responsible === _.employeeKey)
      if task.taskKey === task2.taskKey && task.taskKey === task3.taskKey
    } yield (groupTask, task, client, status, responsible))

    query
      .distinctOn(_._2.taskKey)
      .filterOpt(filters.term)((data, term) => (data._1.folio.toLowerCase like ("%" + term.toLowerCase + "%")) || (data._3.name.toLowerCase like ("%" + term.toLowerCase + "%")))
      .filterOpt(filters.folio)((data, folio) => data._1.folio.toLowerCase like ("%" + folio.toLowerCase + "%"))
      .filterOpt(filters.userKey)((data, userKey) => data._5.isDefined && data._5.map(_.accessUserKey === Some(userKey)).isDefined)
      .filterOpt(filters.employee)((data, employee) => data._5.isDefined && data._5.map(_.accessUserKey === Some(employee)).isDefined)
      .result
      .map({ _.map((value: (GroupTaskEntity, TaskEntity, ClientsEntity, Option[StatusEntity], Option[EmployeeEntity])) => { buildData(value) }) })
  }

  override def getAllTasksAndStatusWithFilters(filters: FiltersKanbanActionRequest): KanbanTasksResponse = {
    val statusList = getStatus(filters.companyKey)
    val tasks = getAllTasksWithFilters(filters)
    val tasksAndStatus: Seq[StatusAndTasks] = statusList.map(status => {
      StatusAndTasks(statusKey = status.statusKey, status = status.status, order = status.order, tasks = tasks.filter(_.statusKey == status.statusKey))
    })

    KanbanTasksResponse(statusAndTasks = tasksAndStatus, total = tasks.length)
  }

  private def buildData(value: (GroupTaskEntity, TaskEntity, ClientsEntity, Option[StatusEntity], Option[EmployeeEntity])): Task = {
    var statusKey: String = null;
    var status: String = null;
    var responsible: String = null;
    if (value._4.isDefined) {
      statusKey = value._4.get.status_key
      status = value._4.get.status
    }
    if (value._5.isDefined) {
      responsible = value._5.get.name
    }
    Task(
      groupTaskKey = value._1.groupTaskKey,
      folio = value._1.folio,
      client = value._3.name,
      taskKey = value._2.taskKey,
      statusKey = statusKey,
      status = status,
      title = value._2.title,
      quantity = value._2.quantity,
      observations = value._2.observations,
      entryDate = value._2.entryDate,
      outputDate = value._2.outputDate,
      responsible = responsible
    )
  }

  
}
