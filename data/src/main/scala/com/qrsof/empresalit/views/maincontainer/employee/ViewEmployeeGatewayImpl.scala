package com.qrsof.empresalit.views.maincontainer.employee

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.domain.employee_resources.pojos.EmployeeResource
import com.qrsof.empresalit.repositories.employee.{EmployeeEntity, EmployeeRepository, EmployeeTable}
import com.qrsof.empresalit.repositories.employee_resources.{EmployeeResourceEntity, EmployeeResourcesRepository}
import com.qrsof.empresalit.repositories.jobs.JobsTable
import com.qrsof.empresalit.views.maincontainer.employee.actions.*
import com.qrsof.empresalit.views.maincontainer.employee.actions.pojos.EmployeeData
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import java.sql.Timestamp
import java.util.Date
import scala.concurrent.ExecutionContext

@Singleton
class ViewEmployeeGatewayImpl @Inject() (employeeRepository: EmployeeRepository, employeeResourcesRepository: EmployeeResourcesRepository)(implicit queryExecutor: QueryExecutor, exc: ExecutionContext)
    extends ViewEmployeeGateway {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewEmployeeGatewayImpl])

  override def addEmployee(newEmployeeData: NewEmployeeData, employeeResources: Seq[EmployeeResource]): NewEmployeeResponse = {

    val files: Seq[EmployeeResourceEntity] = employeeResources.map(file => {
      EmployeeResourceEntity(file.employeeResourceKey, file.required, file.employeeKey, file.resourceKey)
    })

    val newEmployee = employeeRepository.save(
      EmployeeEntity(
        newEmployeeData.employeeKey,
        newEmployeeData.companyKey,
        newEmployeeData.employeeNumber,
        newEmployeeData.fullname,
        newEmployeeData.name,
        newEmployeeData.lastname,
        newEmployeeData.motherLastname,
        Some(newEmployeeData.email),
        newEmployeeData.entryDate,
        newEmployeeData.birthDate,
        newEmployeeData.areaKey,
        newEmployeeData.jobKey,
        newEmployeeData.accountNumber,
        newEmployeeData.amount,
        newEmployeeData.bonus,
        new Date(),
        new Date(),
        None
      )
    )
    employeeResourcesRepository.saveResources(files)
    NewEmployeeResponse(newEmployee.employeeKey)
  }

  override def getEmployee(filtersEmployee: FiltersEmployee): Employees = {
    MappedColumnType.base[Date, Timestamp](
      d => new Timestamp(d.getTime),
      ts => new Date(ts.getTime)
    )

    val offset: Int = filtersEmployee.page * filtersEmployee.pageSize;
    val query = for {
      employee <- EmployeeTable.tableQuery
        .filter(_.companyKey === filtersEmployee.companyKey)
        .filterOpt(filtersEmployee.searchBar)((data, term) => (data.fullname.toLowerCase like ("%" + term.toLowerCase + "%")) || (data.name.toLowerCase like ("%" + term.toLowerCase + "%")))
      job <- JobsTable.tableQuery if job.job_key === employee.jobKey
    } yield (employee, job)

    val total: Int = queryExecutor.syncExecuteQuery {
      query.length.result
    }
    val employees = queryExecutor.syncExecuteQuery {
      query.drop(offset).take(filtersEmployee.pageSize).result
    }
    Employees(
      employeeData = employees.map { employee =>
        Employee(
          employeeKey = employee._1.employeeKey,
          employeeNumber = employee._1.employeeNumber,
          fullname = employee._1.fullname,
          name = employee._1.name,
          lastname = employee._1.lastname,
          motherLastname = employee._1.motherLastname,
          email = employee._1.email,
          entryDate = employee._1.entryDate,
          birthDate = employee._1.birthDate,
          jobKey = employee._2.job_key,
          jobName = employee._2.name,
          amount = employee._1.amount,
          bonus = employee._1.bonus,
          area = None,
          accountNumber = employee._1.accountNumber,
          insertAt = employee._1.insertAt,
          modifiedAt = employee._1.modifiedAt,
          accessUserKey = employee._1.accessUserKey
        )
      },
      total
    )
  }

  override def findEmployee(employeeKey: String): Option[EmployeeEnt] = {
    employeeRepository.findEmployee(employeeKey).map(_.transformInto[EmployeeEnt])
  }

  override def deleteEmployee(employee_key: String): EmployeeKey = {
    employeeRepository.deleteEmployee(employee_key)
    EmployeeKey(
      employee_key
    )
  }

  override def updateEmployee(generalData: EmployeeDataUpdate): Either[EmployeeException, SuccessUpdateResponse] = {
    try {
      val files: Seq[EmployeeResourceEntity] = generalData.files.map(file => {
        EmployeeResourceEntity(file.employeeResourceKey, file.required, file.employeeKey, file.resourceKey)
      })
      employeeResourcesRepository.saveResources(files)

      val updateData = EmployeeEntity(
        generalData.employeeKey,
        "",
        generalData.employeeNumber,
        generalData.fullname,
        generalData.name,
        generalData.lastname,
        generalData.motherLastname,
        Some(generalData.email),
        generalData.entryDate,
        generalData.birthDate,
        generalData.areaKey,
        generalData.jobKey,
        generalData.accountNumber,
        generalData.amount,
        generalData.bonus,
        null,
        new Date(),
        None
      )

      employeeRepository.updateEmployee(updateData)
      Right(SuccessUpdateResponse("UPDATED"))
    } catch
      case exception: Exception => {
        throw exception
      }
  }

  override def getEmployeeByKey(employeeKey: String): Option[EmployeeData] = queryExecutor.syncExecuteQuery {
    (for {
      employee <- EmployeeTable.tableQuery.filter(_.employeeKey === employeeKey)
      job <- JobsTable.tableQuery.filter(_.job_key === employee.jobKey)
    } yield (employee, job.name)).result.headOption.map(result => {
      result.map(value => {
        EmployeeData(
          employeeKey = value._1.employeeKey,
          employeeNumber = value._1.employeeNumber,
          fullname = value._1.fullname,
          name = value._1.name,
          lastname = value._1.lastname,
          motherLastname = value._1.motherLastname,
          email = value._1.email,
          entryDate = value._1.entryDate,
          birthDate = value._1.birthDate,
          jobKey = value._1.jobKey,
          nameJob = value._2,
          amount = value._1.amount,
          bonus = value._1.bonus,
          area = None,
          accountNumber = value._1.accountNumber,
          accessUserKey = value._1.accessUserKey
        )
      })
    })
  }
}
