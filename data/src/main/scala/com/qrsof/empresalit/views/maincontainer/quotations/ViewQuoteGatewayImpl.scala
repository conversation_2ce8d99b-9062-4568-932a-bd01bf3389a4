package com.qrsof.empresalit.views.maincontainer.quotations

import com.qrsof.core.database.qrslick.*
import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.empresalit.repositories.clients.*
import com.qrsof.empresalit.repositories.quotations.*
import com.qrsof.empresalit.repositories.stores.*
import com.qrsof.empresalit.views.maincontainer.quote.*
import com.qrsof.empresalit.views.maincontainer.quote.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import slick.jdbc.JdbcType

import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.util.{Date, UUID}

@Singleton
class ViewQuoteGatewayImpl @Inject()
(quoteRepository: QuoteRepository,
 clientsRepository: ClientsRepository,
 storesRepository: StoresRepository)
(implicit queryExecutor: QueryExecutor)
  extends ViewQuoteGateway {
  override def addQuote(newQuoteData: NewQuoteData): NewQuoteResponse = {
    val newQuote = quoteRepository.saveQuote(
      QuoteEntity(
        UUID.randomUUID().toString,
        newQuoteData.store_key,
        newQuoteData.client_key,
        newQuoteData.folio,
        newQuoteData.date_emission,
        newQuoteData.valid_offer,
        newQuoteData.email,
        newQuoteData.condition,
        newQuoteData.extra_information,
        newQuoteData.time_send,
        newQuoteData.keys_products,
        newQuoteData.file_quote,
        newQuoteData.total_quote,
        newQuoteData.status,
        new Date(),
        new Date()
      )
    )
    NewQuoteResponse(newQuote.store_key)
  }

  override def getAllQuoteByUser(company_key: Option[String]): Quotations = {
    val query = for {
      client <- ClientsTable.tableQuery if
        client.company_key === company_key &&
          company_key.fold[Rep[Boolean]](true)(key => client.company_key === key)

      store <- StoresTable.tableQuery if
        store.company_key === company_key &&
          company_key.fold[Rep[Boolean]](true)(key => store.company_key === key)

      quote <- QuoteTable.tableQuery if
        quote.client_key === client.client_key &&
          quote.store_key === store.store_key
    } yield (client, store, quote)

    val total: Int = queryExecutor.syncExecuteQuery {
      query.length.result
    }

    val cotizacion = queryExecutor.syncExecuteQuery(
      query.result
    )

    Quotations(
      quoteData = cotizacion.map( quote =>
        Quote(
          quote_key = quote._3.quote_key,
          store_key = quote._2.store_key,
          name_store = quote._2.name,
          client_key = quote._1.client_key,
          name_client = quote._1.name,
          folio = quote._3.folio,
          date_emission = quote._3.date_emission,
          valid_offer = quote._3.valid_offer,
          email = quote._3.email,
          condition = quote._3.condition,
          extra_information = quote._3.extra_information,
          time_send = quote._3.time_send,
          keys_products = quote._3.keys_products,
          file_quote = quote._3.file_quote,
          total_quote = quote._3.total_quote,
          status = quote._3.status
        )
      ),
      total
    )
  }

  override def getQuote(filterQuote: FiltersQuote): Quotations = {
    implicit val dateColumnType:JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
      d => new Timestamp(d.getTime),
      ts => new Date(ts.getTime)
    )
    val dateFormat = new SimpleDateFormat("dd-MM-yyyy")

    val dateSearch: Option[Date] = filterQuote.dateSearch.map(dateSearch => dateFormat.parse(dateSearch))

    val offset: Int = filterQuote.page * filterQuote.pageSize;

    val query = for {
      client <- ClientsTable.tableQuery if
        client.company_key === filterQuote.company_key &&
          filterQuote.company_key.fold[Rep[Boolean]](true)(key => client.company_key === key)

      store <- StoresTable.tableQuery if
        store.company_key === filterQuote.company_key &&
          filterQuote.company_key.fold[Rep[Boolean]](true)(key => store.company_key === key)

      quote <- QuoteTable.tableQuery if
        quote.client_key === client.client_key &&
          quote.store_key === store.store_key &&
          dateSearch.fold[Rep[Boolean]](true)(date => quote.date_emission >= date) && (
          filterQuote.searchBar.fold[Rep[Boolean]](true)({ name =>
            val nameLowerCase = client.name.toLowerCase
            val searchLowerCase = name.toLowerCase
            nameLowerCase like ("%" + searchLowerCase + "%")
          }) ||
            filterQuote.searchBar.fold[Rep[Boolean]](true) { name =>
              val nameLowerCase = store.name.toLowerCase
              val searchLowerCase = name.toLowerCase
              nameLowerCase like ("%" + searchLowerCase + "%")
            } ||
            filterQuote.searchBar.fold[Rep[Boolean]](true) { code =>
              quote.folio like ("%" + code + "%")
            }
          )
    } yield (client, store, quote)


    val total: Int = queryExecutor.syncExecuteQuery {
      query.length.result
    }

    val cotizacion = queryExecutor.syncExecuteQuery(
      query.drop(offset).take(filterQuote.pageSize).result
    )

    Quotations(
      quoteData = cotizacion.map { quote =>
        Quote(
          quote_key = quote._3.quote_key,
          store_key = quote._2.store_key,
          name_store = quote._2.name,
          client_key = quote._1.client_key,
          name_client = quote._1.name,
          folio = quote._3.folio,
          date_emission = quote._3.date_emission,
          valid_offer = quote._3.valid_offer,
          email = quote._3.email,
          condition = quote._3.condition,
          extra_information = quote._3.extra_information,
          time_send = quote._3.time_send,
          keys_products = quote._3.keys_products,
          file_quote = quote._3.file_quote,
          total_quote = quote._3.total_quote,
          status = quote._3.status
        )
      },
      total
    )
  }

  override def findQuote(quote_key: String): Option[QuoteEnt] = {
    quoteRepository.findQuote(quote_key).map(_.transformInto[QuoteEnt])
  }

  override def updateQuote(quoteDto: GeneralDtoInQuoteActionRequest): Either[QuoteUpdateException, SuccessUpdateQuote] = {
    val query = for {
      quote <- QuoteTable.tableQuery if
        quote.quote_key === quoteDto.quote_key
    } yield (quote)

    val result = queryExecutor.syncExecuteQuery(
      query.result.headOption
    )

    result match {
      case Some((quote)) =>
        val newQuoteEnt = QuoteEntity(
          quote.quote_key,
          quote.store_key,
          quote.client_key,
          quoteDto.folio,
          quoteDto.date_emission,
          quoteDto.valid_offer,
          quoteDto.email,
          quoteDto.condition,
          quoteDto.extra_information,
          quoteDto.time_send,
          quoteDto.keys_products,
          quoteDto.file_quote,
          quoteDto.total_quote,
          quoteDto.status,
          quote.inserted_at,
          new Date()
        )
        quoteRepository.updateQuote(newQuoteEnt)
        Right(SuccessUpdateQuote("UPDATED"))

      case None =>
        Left(QuoteNotFoundExceptionUpdate(quoteDto.quote_key))
    }
  }

  override def deleteQuote(quote_key: String): QuoteKey = {
    quoteRepository.deleteQuote(quote_key)
    QuoteKey(
      quote_key
    )
  }

  override def markAsFacture(quoteEnt: QuoteEnt): QuoteKey = {
    quoteRepository.markAsFacture(
      quoteEnt
        .into[QuoteEntity]
        .withFieldConst(_.status, QuoteStatus.Facturado)
        .transform
    )
    QuoteKey(
      quoteEnt.quote_key
    )
  }

  override def markAsReject(quoteEnt: QuoteEnt): QuoteKey = {
    quoteRepository.markAsReject(
      quoteEnt
        .into[QuoteEntity]
        .withFieldConst(_.status, QuoteStatus.Rechazado)
        .transform
    )
    QuoteKey(
      quoteEnt.quote_key
    )
  }

  override def markAsPending(quoteEnt: QuoteEnt): QuoteKey = {
    quoteRepository.markAsPending(
      quoteEnt
        .into[QuoteEntity]
        .withFieldConst(_.status, QuoteStatus.Pendiente)
        .transform
    )
    QuoteKey(
      quoteEnt.quote_key
    )
  }
}
