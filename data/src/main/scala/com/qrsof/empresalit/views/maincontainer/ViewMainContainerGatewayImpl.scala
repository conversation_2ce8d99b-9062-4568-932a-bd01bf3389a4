package com.qrsof.empresalit.views.maincontainer

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.companies.{CompaniesEntity, CompaniesTable}
import com.qrsof.empresalit.repositories.users_companies.{UserCompaniesRepository, UserCompaniesTable}
import com.qrsof.empresalit.views.maincontainer.actions.UserCompaniesView
import io.scalaland.chimney.dsl._
import jakarta.inject.{Inject, Singleton}

@Singleton
class ViewMainContainerGatewayImpl @Inject()
(
  userCompaniesRepository: UserCompaniesRepository,
  companiesRepository: UserCompaniesRepository
)
(
  implicit queryExecutor: QueryExecutor
) extends ViewMainContainerGateway {

  override def getUserCompanies(userKey: String): Seq[UserCompaniesView] = {
    val userCompanies: Seq[CompaniesEntity] = queryExecutor.syncExecuteQuery {
      (for {
        userComp <- UserCompaniesTable.tableQuery if userComp.user_key === userKey
        com <- CompaniesTable.tableQuery if com.key === userComp.company_key
      } yield com).result
    }
    userCompanies.map(
      _.into[UserCompaniesView].transform
    )
  }

}
