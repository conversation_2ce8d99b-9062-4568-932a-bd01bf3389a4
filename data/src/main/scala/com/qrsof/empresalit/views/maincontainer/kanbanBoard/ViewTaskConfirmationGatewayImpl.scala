package com.qrsof.empresalit.views.maincontainer.kanbanBoard

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.clients.ClientsTable
import com.qrsof.empresalit.repositories.group_tasks.GroupTaskTable
import com.qrsof.empresalit.repositories.status.StatusTable
import com.qrsof.empresalit.repositories.tasks.TaskTable
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.{TaskDetail, TaskForConfirmationRequest}
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

@Singleton
class ViewTaskConfirmationGatewayImpl @Inject() ()(implicit queryExecutor: QueryExecutor, executionContext: ExecutionContext) extends ViewTaskConfirmationGateway {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewTaskConfirmationGatewayImpl])

  override def getTaskDetail(taskKey: String): Option[TaskDetail] = queryExecutor.syncExecuteQuery {
    (for {
      taskTable <- TaskTable.tableQuery.filter(_.taskKey === taskKey)
      groupTaskTable <- GroupTaskTable.tableQuery.filter(_.groupTaskKey === taskTable.groupTaskKey)
      (task2, status) <- TaskTable.tableQuery joinLeft StatusTable.tableQuery on (_.statusKey === _.status_key)
      (groupTaskTable2, clientTable) <- GroupTaskTable.tableQuery joinLeft ClientsTable.tableQuery on (_.clientKey === _.client_key)
      if taskTable.taskKey === task2.taskKey && groupTaskTable.groupTaskKey === groupTaskTable2.groupTaskKey
    } yield (taskTable, groupTaskTable, status, clientTable)).result.headOption.map(item => {
      item.map(value => {
        logger.info("getTaskDetail::value: {}", value)
        var task = TaskDetail(
          value._1.taskKey,
          value._1.title,
          value._1.quantity,
          value._1.observations,
          value._1.entryDate,
          value._1.outputDate,
          value._1.responsible,
          value._2.groupTaskKey,
          value._2.companyKey,
          value._2.folio,
          null,
          null,
          null,
          Seq.empty
        )
        if (value._3.isDefined) {
          task = task.copy(statusKey = value._3.get.status_key, status = value._3.get.status)
        }
        if (value._4.isDefined) {
          task = task.copy(client = value._4.get.name)
        }
        task
      })
    })
  }

  override def saveTaskConfirmation(taskForConfirmationRequest: TaskForConfirmationRequest): Unit = queryExecutor.syncExecuteQuery {
    TaskTable.tableQuery
      .filter(_.taskKey === taskForConfirmationRequest.taskKey)
      .forUpdate
      .result
      .head
      .flatMap(task => {
        if (taskForConfirmationRequest.observations.isDefined) {
          TaskTable.saveOrUpdate(task.copy(statusKey = Some(taskForConfirmationRequest.statusKey), observations = taskForConfirmationRequest.observations.get))
        } else {
          TaskTable.saveOrUpdate(task.copy(statusKey = Some(taskForConfirmationRequest.statusKey)))
        }
      })
  }
}
