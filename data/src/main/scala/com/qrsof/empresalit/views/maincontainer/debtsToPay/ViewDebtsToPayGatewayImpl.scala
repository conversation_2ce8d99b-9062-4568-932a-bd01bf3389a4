package com.qrsof.empresalit.views.maincontainer.debtsToPay

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.debs_to_pay.{DebtsToPayEntity, DebtsToPayRepository, DebtsToPayTable}
import com.qrsof.empresalit.repositories.suppliers.{SuppliersRepository, SuppliersTable}
import com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.*
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import slick.jdbc.JdbcType

import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.util.{Calendar, Date, UUID}

@Singleton
class ViewDebtsToPayGatewayImpl @Inject()
(debtsToPayRepository: DebtsToPayRepository,
 suppliersRepository: SuppliersRepository)
(implicit queryExecutor: QueryExecutor)
  extends ViewDebtToPayGateway {

  override def getDebtsToPay(listDebtToPayActionRequest: ListDebtToPayActionRequest): DebtsToPay = {
    implicit val dateColumnType: JdbcType[Date] = MappedColumnType.base[Date, Timestamp](
      d => new Timestamp(d.getTime),
      ts => new Date(ts.getTime)
    )
    implicit val debtToPayStatusType: JdbcType[DebtToPayStatus] = MappedColumnType.base[DebtToPayStatus, String](
      en => en.entryName, s => DebtToPayStatus.withName(s))
    val dateFormat = new SimpleDateFormat("dd-MM-yyyy")
    val calendar = Calendar.getInstance()

    val offset: Int = listDebtToPayActionRequest.page * listDebtToPayActionRequest.pageSize;
    val startDate: Option[Date] = listDebtToPayActionRequest.startDate.map(startDate => dateFormat.parse(startDate))
    val endDate: Option[Date] = listDebtToPayActionRequest.endDate.map { endDateString =>
      val date = dateFormat.parse(endDateString)
      calendar.setTime(date)
      calendar.set(Calendar.HOUR_OF_DAY, 23)
      calendar.set(Calendar.MINUTE, 59)
      calendar.set(Calendar.SECOND, 59)
      new Date(calendar.getTimeInMillis)
    }

    val consulta = for {
      suppl <- SuppliersTable.tableQuery if
        suppl.company_key === listDebtToPayActionRequest.company_Key &&
          listDebtToPayActionRequest.supplier_key.fold[Rep[Boolean]](true)(supp_key => suppl.supplier_key === supp_key) &&
          (
            listDebtToPayActionRequest.searchBar.fold[Rep[Boolean]](true)({ name =>
              val nameLowerCase = suppl.name.toLowerCase
              val searchLowerCase = name.toLowerCase
              nameLowerCase like ("%" + searchLowerCase + "%")
            }) ||
              listDebtToPayActionRequest.searchBar.fold[Rep[Boolean]](true) { rfc =>
                val searchTermUpperCase = rfc.toUpperCase
                suppl.rfc like ("%" + searchTermUpperCase + "%")
              }
            )

      debts <- DebtsToPayTable.tableQuery if
        debts.supplier_key === suppl.supplier_key &&
          listDebtToPayActionRequest.paymenStatus.fold[Rep[Boolean]](true)(dato => debts.debt_paid_status === dato) &&
          startDate.fold[Rep[Boolean]](true)(fecha => debts.insertedAt >= fecha) &&
          endDate.fold[Rep[Boolean]](true)(fecha => debts.insertedAt <= fecha)
    } yield (suppl, debts)

    val total: Int = queryExecutor.syncExecuteQuery {
      consulta.length.result
    }

    val facturas = queryExecutor.syncExecuteQuery(
      consulta.drop(offset).take(listDebtToPayActionRequest.pageSize).result
    )

    DebtsToPay(
      facturas = facturas.map { factura =>
        DebtToPay(
          debt_to_pay_key = factura._2.debt_to_pay_key,
          amount = factura._2.amount,
          inserted_at = factura._2.insertedAt.toString,
          name = factura._1.name,
          regimen_fiscal_clave = factura._1.regimen_fiscal_clave,
          debt_paid_status = factura._2.debt_paid_status,
          rfc = factura._1.rfc,
        )
      },
      total
    )

  }

  override def addDebtToPay(newDebtToPayData: NewDebtToPayData): NewDebtToPayResponse = {
    val newDebt = debtsToPayRepository.save(
      DebtsToPayEntity(
        UUID.randomUUID().toString,
        newDebtToPayData.amount,
        newDebtToPayData.company_key,
        newDebtToPayData.supplier_key,
        new Date(),
        new Date(),
        newDebtToPayData.debt_paid_status,
        newDebtToPayData.file_pdf
      )
    )
    NewDebtToPayResponse(newDebt.debt_to_pay_key)
  }

  override def findDebtToPay(debtToPayKey: String): Option[DebtsToPayEnt] = {
    debtsToPayRepository.findDebtToPay(debtToPayKey).map(_.transformInto[DebtsToPayEnt])
  }

  override def deleteDebtToPay(debtToPayKey: String): DebtToPayKey = {
    debtsToPayRepository.deleteDebtToPay(debtToPayKey)
    DebtToPayKey(
      debtToPayKey
    )
  }

  override def markAsPaid(debtToPay: DebtsToPayEnt): DebtToPayKey = {
    debtsToPayRepository.markAsPaid(
      debtToPay
        .into[DebtsToPayEntity]
        .withFieldConst(_.debt_paid_status, DebtToPayStatus.pagado)
        .transform
    )
    DebtToPayKey(
      debtToPay.debt_to_pay_key
    )
  }

}
