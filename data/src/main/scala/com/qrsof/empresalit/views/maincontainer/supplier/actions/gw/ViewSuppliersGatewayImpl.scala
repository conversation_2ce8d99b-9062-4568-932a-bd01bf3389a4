package com.qrsof.empresalit.views.maincontainer.supplier.actions.gw

import com.qrsof.core.database.qrslick.QrPostgresProfile.api.*
import com.qrsof.core.database.qrslick.QueryExecutor
import com.qrsof.empresalit.repositories.addresses.AddressesTable
import com.qrsof.empresalit.repositories.debs_to_pay.DebtsToPayRepository
import com.qrsof.empresalit.repositories.suppliers.{SuppliersEntity, SuppliersRepository, SuppliersTable}
import com.qrsof.empresalit.views.maincontainer.suppliers.ViewSuppliersGateway
import com.qrsof.empresalit.views.maincontainer.suppliers.actions.*
import jakarta.inject.{Inject, Singleton}

import java.util.{Date, UUID}

@Singleton
class ViewSuppliersGatewayImpl @Inject()
(debtsToPayRepository: DebtsToPayRepository,
 suppliersRepository: SuppliersRepository)
(implicit queryExecutor: QueryExecutor)
  extends ViewSuppliersGateway {

  override def getSuppliers(filters: FiltersActionRequest): SuppliersWithFilters = {
    val offset: Int = filters.page * filters.pageSize

    val query = for {
      suppl <- SuppliersTable.tableQuery
      if suppl.company_key === filters.companyKey &&
        filters.searchBar.fold[Rep[Boolean]](true)(name => suppl.name like ("%" + name + "%"))
      addre <- AddressesTable.tableQuery if suppl.address_key === addre.address_key
    } yield (suppl, addre)

    val total: Int = queryExecutor.syncExecuteQuery(query.length.result)

    val suppliers = queryExecutor.syncExecuteQuery {
      query.drop(offset).take(filters.pageSize).result
    }

    SuppliersWithFilters(
      suppliers.map { supplier =>
        Supplier(
          supplier_key = supplier._1.supplier_key,
          name = supplier._1.name,
          regimen_fiscal_clave = supplier._1.regimen_fiscal_clave,
          company_key = supplier._1.company_key,
          estado = supplier._2.estado,
          municipio = supplier._2.municipio,
          calle = supplier._2.calle,
          numero_exterior = supplier._2.numero_exterior,
        )
      },
      total
    )
  }

  override def getAllSuppliers(company_key: Option[String]): SuppliersWithFilters = {
    val query = for {
      suppl <- SuppliersTable.tableQuery if suppl.company_key === company_key
      address <- AddressesTable.tableQuery if suppl.address_key === address.address_key
    } yield (suppl, address)

    val total: Int = queryExecutor.syncExecuteQuery(query.length.result)

    val suppliers = queryExecutor.syncExecuteQuery {
      query.result
    }

    SuppliersWithFilters(
      suppliers.map { supplier =>
        Supplier(
          supplier_key = supplier._1.supplier_key,
          name = supplier._1.name,
          regimen_fiscal_clave = supplier._1.regimen_fiscal_clave,
          company_key = supplier._1.company_key,
          estado = supplier._2.estado,
          municipio = supplier._2.municipio,
          calle = supplier._2.calle,
          numero_exterior = supplier._2.numero_exterior,
        )
      },
      total
    )
  }

  override def addNewSupplier(newSupplierFormDataAction: NewSupplierData): NewSupplierResponseAction = {
    val newSupplier = suppliersRepository.save(
      SuppliersEntity(
        UUID.randomUUID().toString,
        newSupplierFormDataAction.generalData.name,
        newSupplierFormDataAction.generalData.regimen_fiscal_clave,
        newSupplierFormDataAction.company_key,
        new Date(),
        new Date(),
        newSupplierFormDataAction.address_key,
        newSupplierFormDataAction.generalData.rfc,
      )
    )
    NewSupplierResponseAction(newSupplier.supplier_key)
  }

}
