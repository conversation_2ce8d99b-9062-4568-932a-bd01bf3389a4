pipeline {
    options {
//        quietPeriod(60)
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '5'))
        skipDefaultCheckout(true)
    }

    agent {
        label 'docker'
    }

    environment {
        REPO_NAME = 'qrsof/empresalit-api'
        CONTAINER_NAME = 'empresalit-api'
        IMAGE = ''
        ECRURL = 'https://417954770034.dkr.ecr.us-west-2.amazonaws.com'
        TEAMS_WEBHOOK = "https://outlook.office.com/webhook/35c87ee0-33c7-492b-a091-259cf6027416@e9d6a817-9043-4a9f-bae9-b6dd2ad827fa/IncomingWebhook/e4fa770fbdbc4145a7ea12579c1a12a1/2762f300-e118-4c9d-b3c3-3ce226ad7d80"
        DB_USER = "postgres"
        DB_PASSWORD = "postgres"
        DB_HOST = "db"
        AUTHOR = ""
        GIT_LOCAL_BRANCH = ""
        AWS_ACCESS_KEY_ID = credentials('ECR_QRSOF_DEPLOY_ACCESS_KEY')
        AWS_SECRET_ACCESS_KEY = credentials('ECR_QRSOF_DEPLOY_SECRET_KEY')
        AWS_REGION = "us-west-2"
        HOST_APP = ""
        HOST_APP_CREDENTIAL = ""
        CONFIG_FILE = ""
    }

    stages {

        stage('Clone') {
            steps {
                script {
                    sh "whoami"
                    sh "sudo chown ubuntu: -R \$PWD/"
                    doCheckout()
                }
            }
        }
        stage('Init GIT') {
            steps {
                script {
                    sh 'git config  user.email "<EMAIL>"'
                    sh 'git config  user.name "Jenkins"'
                    passedBuilds = []
                    lastSuccessfulBuild(passedBuilds, currentBuild);
                    def changeLog = getChangeLog(passedBuilds)
                    COMMIT_MESSAGE = changeLog
                    office365ConnectorSend color: "#229954", message: "Build:${env.BUILD_NUMBER} STARTED by ${AUTHOR} \n\n <ul>${COMMIT_MESSAGE}</ul>", webhookUrl: "${TEAMS_WEBHOOK}"
                }
            }
        }
        stage('Build') {
            steps {
                script {
                    docker.image('postgres:12').withRun('-e "POSTGRES_PASSWORD=postgres"') { c ->
                        docker.image('openjdk:15').inside("-u 0 --link ${c.id}:db -v /.sbt:/.sbt -v /.ivy2:/.ivy2") {
                            sh 'pwd'
                            sh './sbt/bin/sbt -Dsbt.global.base=/.sbt -Dsbt.boot.directory=/.sbt -Dsbt.ivy.home=/.ivy2 clean coverage test coverageAggregate'
                        }
                    }
                }
            }
        }

        stage('Docker Stage') {
            when {
                anyOf {
                    branch 'master'
                    branch 'production'
                }
            }
            steps {
                script {
                    docker.image('openjdk:15').inside("-u 0") {

                        CONFIG_FILE = 'empresalit-sandbox-config'

                        if (env.BRANCH_NAME == 'production') {
                            CONFIG_FILE = 'empresalit-production-config'
                        }

                        configFileProvider(
                                [configFile(
                                        fileId: CONFIG_FILE,
                                        targetLocation: 'conf/application.conf'
                                )]) {
                            sh './sbt/bin/sbt -Dsbt.global.base=/.sbt -Dsbt.boot.directory=/.sbt -Dsbt.ivy.home=/.ivy2 docker:stage'
                        }
                    }
                }
            }
        }

        stage('Docker Build') {
            when {
                anyOf {
                    branch 'master'
                    branch 'production'
                }
            }
            steps {
                script {
                    IMAGE = "${REPO_NAME}:${env.BUILD_ID}"
                    docker.build(IMAGE, "target/docker/stage")
                }
            }
        }

        stage('Docker Push') {
            when {
                anyOf {
                    branch 'master'
                    branch 'production'
                }
            }
            steps {
                withAWS(credentials: 'jenkins', region: 'us-west-2') {
                    script {
                        sh "set +x; ${ecrLogin()}"
                        docker.withRegistry(ECRURL) {
                            if (env.BRANCH_NAME == 'production') {
                                docker.image(IMAGE).push('latest')
                            } else {
                                docker.image(IMAGE).push('sandbox-latest')
                            }
                        }
                    }
                }
                sh "sudo docker image prune -a -f"
            }
        }


        stage('Deploy') {
            when {
                anyOf {
                    branch 'master'
                    branch 'production'
                }
            }
            steps {
                script {
                    if (env.BRANCH_NAME == 'production') {
                        HOST_APP = '*************'
                        HOST_APP_CREDENTIAL = 'empresalit-prod'
                        deploy('empresalit-api', "${REPO_NAME}:latest", CONTAINER_NAME, '9000', '9000')
                    } else {
                        HOST_APP_CREDENTIAL = 'qr-develop-aws-lightsail'
                        HOST_APP = 'sandbox.api.empresalit.com'
                        deploy('sandbox-empresalit-api', "${REPO_NAME}:sandbox-latest", "${CONTAINER_NAME}-sandbox", '9000', '9000')
                    }
                }
            }
        }

    }

    post {
        failure {
            office365ConnectorSend color: "#E74C3C", message: "Build:${env.BUILD_NUMBER} FAILED", webhookUrl: "${TEAMS_WEBHOOK}"
        }

        success {
            office365ConnectorSend color: "#229954", message: "Build:${env.BUILD_NUMBER} SUCCESS", webhookUrl: "${TEAMS_WEBHOOK}"
        }
//         always {
//             publishHTML(target: [
//                     allowMissing         : false,
//                     alwaysLinkToLastBuild: false,
//                     keepAll              : true,
//                     reportDir            : 'target/test-reports',
//                     reportFiles          : 'index.html',
//                     reportName           : "Test report"
//             ])
//
//             publishHTML(target: [
//                     allowMissing         : false,
//                     alwaysLinkToLastBuild: false,
//                     keepAll              : true,
//                     reportDir            : 'target/scala-2.13/scoverage-report',
//                     reportFiles          : 'index.html',
//                     reportName           : "Coverage report"
//             ])
//         }
    }
}

def deploy(remoteName, repoName, containerName, portHost, portContainer) {
    def remote = [:]
    remote.name = remoteName
    remote.host = HOST_APP
    remote.allowAnyHosts = true
    withCredentials([sshUserPrivateKey(credentialsId: HOST_APP_CREDENTIAL, keyFileVariable: 'identity', usernameVariable: 'userName')]) {
        remote.user = userName
        remote.identityFile = identity
        sshCommand remote: remote, command: "export AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} && export AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY} && export AWS_DEFAULT_REGION=${AWS_REGION} && aws ecr get-login-password --region us-west-2 | sudo docker login --username AWS --password-stdin 417954770034.dkr.ecr.us-west-2.amazonaws.com"
        sshCommand remote: remote, command: "sudo docker pull 417954770034.dkr.ecr.us-west-2.amazonaws.com/${repoName}"
        sshCommand remote: remote, command: "sudo docker container stop ${containerName}", failOnError: false
        sshCommand remote: remote, command: "sudo docker container wait ${containerName}", failOnError: false
        sshCommand remote: remote, command: "sudo docker container rm ${containerName}", failOnError: false
        sshCommand remote: remote, command: "sudo docker run -d -p ${portHost}:${portContainer} -v /home/<USER>/logs:/logs --restart always --name ${containerName} 417954770034.dkr.ecr.us-west-2.amazonaws.com/${repoName}"
        sshCommand remote: remote, command: "sudo docker image prune -a -f"
    }
}

private void doCheckout() {
    def repoMap = checkout(scm: [
            $class                           : 'GitSCM',
            branches                         : scm.branches,
            extensions                       : scm.extensions + [
                    [$class: 'LocalBranch', localBranch: ''],
                    [$class: 'WipeWorkspace'],
            ],
            userRemoteConfigs                : scm.userRemoteConfigs,
            doGenerateSubmoduleConfigurations: false
    ], poll: false, changelog: true)
    GIT_LOCAL_BRANCH = repoMap.GIT_LOCAL_BRANCH
    AUTHOR = author()

    if (!env.CHANGE_ID && AUTHOR == 'Jenkins') {
        currentBuild.result = 'ABORTED'
        error('Last commit released library, aborting the build to prevent a loop.')
    }
    sh "git status"
}

private String author() {
    return sh(returnStdout: true, script: "git log -1 --pretty=format:'%an'").trim()
}

def lastSuccessfulBuild(passedBuilds, build) {
    if ((build != null) && (build.result != 'SUCCESS')) {
        passedBuilds.add(build)
        lastSuccessfulBuild(passedBuilds, build.getPreviousBuild())
    }
}

@NonCPS
def getChangeLog(passedBuilds) {
    def log = ""
    for (int x = 0; x < passedBuilds.size(); x++) {
        def currentBuild = passedBuilds[x];
        def changeLogSets = currentBuild.rawBuild.changeSets
        for (int i = 0; i < changeLogSets.size(); i++) {
            def entries = changeLogSets[i].items
            for (int j = 0; j < entries.length; j++) {
                def entry = entries[j]
                log += "<li>${entry.msg} by ${entry.author}</li>"
            }
        }
    }
    return log;
}

