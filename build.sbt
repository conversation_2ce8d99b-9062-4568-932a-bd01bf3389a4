import com.typesafe.sbt.packager.docker.DockerChmodType
import com.typesafe.sbt.packager.docker.ExecCmd
// Docker
enablePlugins(DockerPlugin)
enablePlugins(AshScriptPlugin)
dockerChmodType := DockerChmodType.UserGroupWriteExecute
dockerBaseImage := "docker.io/amazoncorretto:21-alpine"
dockerRepository := Some("417954770034.dkr.ecr.us-west-2.amazonaws.com")
dockerExposedPorts := Seq(80)
dockerExposedVolumes := Seq("/logs", "/conf")
dockerCommands := dockerCommands.value.filterNot {
  case ExecCmd("ENTRYPOINT", args @ _*) => true
  case _                                => false
}
dockerCommands += ExecCmd("ENTRYPOINT", "/opt/docker/bin/empresalit-api", "-Dhttp.port=80", "-Dpidfile.path=/dev/null")
// End Docker

lazy val scala3 = "3.5.2"

val jacksonVersion = "2.17.0"
val swaggerVersion = "2.2.21"
val awsVersion = "1.12.398"

lazy val swaggerDependencies = Seq(
  "jakarta.ws.rs" % "jakarta.ws.rs-api" % "4.0.0",
  "com.fasterxml.jackson.core" % "jackson-databind" % jacksonVersion,
  "com.fasterxml.jackson.core" % "jackson-core" % jacksonVersion,
  "com.fasterxml.jackson.core" % "jackson-annotations" % jacksonVersion,
  "com.fasterxml.jackson.module" %% "jackson-module-scala" % jacksonVersion,
  "com.qrsof.core.api" %% "swagger" % "1.0.0-01-SNAPSHOT",
  "io.swagger.core.v3" % "swagger-jaxrs2-jakarta" % swaggerVersion
)

lazy val testDependencies = Seq(
  "org.scalatest" %% "scalatest" % "3.2.18" % Test,
  "org.scalamock" %% "scalamock" % "6.0.0" % Test,
  "com.vladsch.flexmark" % "flexmark-all" % "0.64.8" % Test,
  "org.scalatestplus" %% "mockito-4-6" % "3.2.15.0" % "test"
)

lazy val commonDependencies = Seq(
  "com.beachape" %% "enumeratum-play-json" % "1.8.0",
  "com.nimbusds" % "nimbus-jose-jwt" % "9.40",
  "io.scalaland" %% "chimney" % "1.3.0",
  "com.qrsof.core" %% "crypto" % "2.0.0-01-SNAPSHOT",
  "com.qrsof.core" %% "jwt" % "4.0.0-01-SNAPSHOT",
  "com.qrsof.core" %% "app-core" % "4.0.0-03-SNAPSHOT",
  "com.qrsof.core" %% "app-play" % "5.0.0-01-SNAPSHOT",
  "com.qrsof.core" %% "app" % "1.0.0-11-SNAPSHOT",
  "com.qrsof.core" %% "certificates" % "5.0.0-01-SNAPSHOT",
  "com.qrsof.apptack" %% "apptack-client" % "4.0.0-11-SNAPSHOT",
  "org.playframework" %% "play" % "3.0.4",
  "commons-io" % "commons-io" % "2.16.1",
  "com.qrsof.core" %% "http-components" % "1.0.0-02-SNAPSHOT",
  "org.apache.commons" % "commons-compress" % "1.26.1"
) ++ testDependencies

lazy val commonSettings = Seq(
  scalaVersion := scala3,
  organization := "dev.cmsoftware.empresalit",
  maintainer := "<EMAIL>",
  resolvers += "Nexus Releases" at "https://nexus-ci.qrsof.com/repository/maven-public",
  credentials += Credentials(
    "Sonatype Nexus Repository Manager",
    "nexus-ci.qrsof.com",
    "deployment",
    "4jDzLGNHgaWiWFj"
  ),
  scalacOptions ++= Seq(
    "-feature",
    "-Wunused:all",
    "-Wshadow:all",
    "-explain"
  ),
  semanticdbEnabled := true,
  coverageExcludedPackages := ".*Module.*;.*Module",
  Test / testOptions ++= Seq(
    Tests.Argument(TestFrameworks.ScalaTest, "-o"),
    Tests.Argument(TestFrameworks.ScalaTest, "-h", "target/test-reports"),
    Tests.Argument(TestFrameworks.ScalaTest, "-u", "target/test-reports")
  ),
  libraryDependencies ++= commonDependencies
)

lazy val funTests = (project in file("fun-tests"))
  .settings(
    name := "fun-tests",
    libraryDependencies ++= Seq(
      "com.softwaremill.sttp.client3" %% "core" % "3.10.1"
    ),
    commonSettings
  )
  .dependsOn(root)
  .aggregate(root)

lazy val itTests = (project in file("it-tests"))
  .settings(
    name := "fun-tests",
    libraryDependencies ++= Seq(
      "com.softwaremill.sttp.client3" %% "core" % "3.10.1"
    ),
    commonSettings
  )
  .dependsOn(root)
  .aggregate(root)

lazy val business = (project in file("business"))
  .settings(
    libraryDependencies ++= Seq(
      "com.qrsof.empresalit" % "invoice-xml" % "15-4.0-1-SNAPSHOT",
      "com.qrsof.empresalit" % "stamp-sifei-client" % "1.11.1",
      "com.qrsof.empresalit" % "cancel-sifei-client" % "1.11.1",
      "com.qrsof.core" %% "jwt" % "3.0.0-SNAPSHOT",
      "com.ximpleware" % "vtd-xml" % "2.13.4",
      "com.google.zxing" % "core" % "3.5.3",
      "com.google.zxing" % "javase" % "3.5.3",
      "org.postgresql" % "postgresql" % "42.7.3",
      //"com.qrsof.core.storage" % "storage-digital-ocean" % "2.0.0-00-SNAPSHOT",
      "com.qrsof.core.storage" %% "storage-digitalocean" % "1.0.0-01-SNAPSHOT",
      "com.qrsof.core.storage" %% "storage-filesystem" % "1.0.0-01-SNAPSHOT",
      "com.github.jwt-scala" %% "jwt-play-json" % "10.0.1",
      "com.fasterxml.jackson.core" % "jackson-databind" % "2.17.1",
      "com.nimbusds" % "nimbus-jose-jwt" % "9.40" exclude ("com.fasterxml.jackson.core", "jackson-databind"),
      "com.sun.mail" % "jakarta.mail" % "2.0.1"
    ) ++ swaggerDependencies,
    commonSettings
  )

lazy val data = (project in file("data"))
  .dependsOn(business)
  .settings(
    libraryDependencies ++= Seq(
      "com.qrsof.core" %% "database-slick-3-5-1" % "2.0.0-01-SNAPSHOT",
      "com.qrsof.core" %% "database-evolution" % "5.0.0-01-SNAPSHOT",
      "com.qrsof.core" %% "database-slick-3-5-1" % "2.0.0-01-SNAPSHOT" % "test" classifier "tests"
    ),
    commonSettings
  )

lazy val root = (project in file("."))
  .enablePlugins(PlayScala)
  .settings(
    name := "empresalit-api",
    version := "1.0-SNAPSHOT",
    commonSettings,
    libraryDependencies ++= Seq(
      guice,
      "ch.qos.logback" % "logback-classic" % "1.5.6",
      "net.codingwell" %% "scala-guice" % "6.0.0"
    )
  )
  .dependsOn(business, data)
  .aggregate(business, data)
