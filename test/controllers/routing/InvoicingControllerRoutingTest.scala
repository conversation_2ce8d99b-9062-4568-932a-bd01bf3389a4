package controllers.routing

import controllers.AppBaseControllerTest

class InvoicingControllerRoutingTest extends AppBaseControllerTest {

//	"Invoices" can {
//		val path = "/companies/invoices"
//		"Generate invoice" in {
//			val Some(result) = route(app, FakeRequest(POST, path))
//			status(result) mustEqual OK
//			verify(invoiceController).generateInvoice()
//		}
//
//		"Retrive invoice metadata" in {
//			val Some(result) = route(app, FakeRequest(GET, s"$path/$uuidInvoice"))
//			status(result) mustEqual OK
//			verify(invoiceController).retreiveInvoiceMetadata(uuidInvoice)
//		}
//
//		"Request cancellation" in {
//			val deletePath = path + "/" + uuidInvoice
//			val Some(result) = route(app, FakeRequest(DELETE, deletePath))
//			status(result) mustEqual OK
//			verify(invoiceController).cancelInvoice(uuidInvoice)
//		}
//
//		"Request invoice status" in {
//			val requestPath = path + "/" + uuidInvoice + "/status"
//			val Some(result) = route(app, FakeRequest(GET, requestPath))
//			status(result) mustEqual OK
//			verify(invoiceController).getInvoiceStatus(uuidInvoice)
//		}

//	}

}
