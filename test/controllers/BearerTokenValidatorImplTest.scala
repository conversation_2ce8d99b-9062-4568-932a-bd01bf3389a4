package controllers

import com.qrsof.empresalit.controllers.BearerTokenValidatorImpl
import com.qrsof.empresalit.gateways.appmanager.{AppManager, AppUser}
import org.mockito.Mockito
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class BearerTokenValidatorImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers{

  "BearerTokenValidatorImpl" - {
    val token = "token"
    val appManager = mock[AppManager]
    val appUser = mock[AppUser]
    val userkey = "userkey"
    val bearerTokenValidatorImpl = new BearerTokenValidatorImpl(appManager)
    Mockito.when(appManager.getAppUser(token)) thenReturn appUser
    Mockito.when(appUser.userKey) thenReturn userkey
    "when:validate token" in {
      val principal = bearerTokenValidatorImpl.execute(token)
      principal shouldEqual userkey
    }
  }
}
