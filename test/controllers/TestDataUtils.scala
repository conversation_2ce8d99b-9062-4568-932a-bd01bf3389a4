package controllers

import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.figuratransporte.{DomicilioFiguraForm, PartesTransporteForm, TiposFiguraForm}
import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.mercancias.*
import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.ubicaciones.{DomicilioForm, UbicacionForm}
import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.{CartaPorteForm, FiguratransporteForm, MercanciasForm, UbicacionesForm}
import com.qrsof.empresalit.controllers.invoices.forms.complementos.nomina.*
import com.qrsof.empresalit.controllers.invoices.forms.complementos.pagos.{ImpuestosPForm, RetencionPForm, TrasladoPForm}
import com.qrsof.empresalit.controllers.invoices.forms.v40.complementos.ComplementosForm
import com.qrsof.empresalit.controllers.invoices.forms.v40.concepto.v20.*
import com.qrsof.empresalit.controllers.invoices.forms.v40.concepto.{ParteForm, v20}
import com.qrsof.empresalit.controllers.invoices.forms.v40.*
import com.qrsof.empresalit.controllers.invoices.forms.{ACuentaTercerosForm, InformacionAduaneraForm, v40}
import com.qrsof.empresalit.invoicing.domain.conceptos.{CuentaPredial, InformacionAduanera}
//import controllers.invoices.forms.complementos.cartaporte.figuratransporte.{DomicilioFiguraForm, PartesTransporteForm, TiposFiguraForm}
//import controllers.invoices.forms.complementos.cartaporte.mercancias._
//import controllers.invoices.forms.complementos.cartaporte.ubicaciones.{DomicilioForm, UbicacionForm}
//import controllers.invoices.forms.complementos.cartaporte.{CartaPorteForm, FiguratransporteForm, MercanciasForm, UbicacionesForm}
//import controllers.invoices.forms.complementos.nomina._
//import controllers.invoices.forms.complementos.pagos.{ImpuestosPForm, RetencionPForm, TrasladoPForm}
//import controllers.invoices.forms.v40.complementos.pagos.v20
//import controllers.invoices.forms.v40.complementos.pagos.v20.{ImpuestosDRForm, RetencionDRForm, RetencionesDRForm, TotalesForm, TrasladoDRForm, TrasladosDRForm}
//import controllers.invoices.forms.v40.concepto.ParteForm

object TestDataUtils {

  val detalle: DetalleMercanciaForm = DetalleMercanciaForm(
    unidadPesoMerc = "unidadPesoMerc",
    pesoBruto = "1",
    pesoNeto = "2",
    pesoTara = "3",
    numPiezas = Some(4)
  )
  val guia: GuiasIdentificacionForm = GuiasIdentificacionForm(
    numeroGuiaIdentificacion = "numeroGuiaIdentificacion",
    descripGuiaIdentificacion = "descripGuiaIdentificacion",
    pesoGuiaIdentificacion = "1"
  )
  val cantidad: CantidadTransportaForm = CantidadTransportaForm(
    cantidad = "1",
    idOrigen = "idOrigen",
    idDestino = "idDestino",
    cvesTransporte = Some("cvesTransporte")
  )
  val pedimento: PedimentoForm = PedimentoForm(
    pedimento = "pedimento"
  )
  val ubicacion: UbicacionForm = UbicacionForm(
    tipoUbicacion = "tipoUbicacion",
    idUbicacion = Some("idUbicacion"),
    rfcRemitenteDestinatario = "rfcRemitenteDestinatario",
    nombreRemitenteDestinatario = Some("nombreRemitenteDestinatario"),
    numRegIdTrib = Some("numRegIdTrib"),
    residenciaFiscal = Some("residenciaFiscal"),
    numEstacion = Some("numEstacion"),
    nombreEstacion = Some("nombreEstacion"),
    navegacionTrafico = Some("navegacionTrafico"),
    fechaHoraSalidaLlegada = "fechaHoraSalidaLlegada",
    tipoEstacion = Some("tipoEstacion"),
    distanciaRecorrida = Some("1"),
    domicilio = Some(
      DomicilioForm(
        calle = Some("calle"),
        numeroExterior = Some("numeroExterior"),
        numeroInterior = Some("numeroInterior"),
        colonia = Some("colonia"),
        localidad = Some("localidad"),
        referencia = Some("referencia"),
        municipio = Some("municipio"),
        estado = "estado",
        pais = "pais",
        codigoPostal = "codigoPostal"
      )
    )
  )
  val mercancia: MercanciaForm = MercanciaForm(
    bienesTransp = "bienesTransp",
    clavesSTCC = Some("clavesSTCC"),
    descripcion = "descripcion",
    cantidad = "1",
    claveUnidad = "claveUnidad",
    unidad = Some("unidad"),
    dimensiones = Some("dimensiones"),
    materialPeligroso = Some("materialPeligroso"),
    cveMaterialPeligroso = Some("cveMaterialPeligroso"),
    embalaje = Some("embalaje"),
    descripEmbalaje = Some("descripEmbalaje"),
    pesoEnKg = "2",
    valorMercancia = Some("3"),
    moneda = Some("moneda"),
    fraccionArancelaria = Some("fraccionArancelaria"),
    uuidComercioExt = Some("uuidComercioExt"),
    pedimentos = Some(Seq(pedimento)),
    guiasIdentificacion = Some(Seq(guia)),
    cantidadTransporta = Some(Seq(cantidad)),
    detalleMercancia = Some(Seq(detalle))
  )
  val mercancias: MercanciasForm = MercanciasForm(
    pesoBrutoTotal = "1",
    unidadPeso = "unidadPeso",
    pesoNetoTotal = Some("2"),
    numTotalMercancias = 3,
    cargoPorTasacion = Some("4"),
    mercancia = Seq(mercancia),
    autotransporte = Some(
      AutotransporteForm(
        permSCT = "permSCT",
        numPermisoSCT = "numPermisoSCT",
        identificacionVehicular = IdentificacionVehicularForm(
          configVehicular = "configVehicular",
          placaVM = "placaVM",
          anioModeloVM = 1
        ),
        seguros = SegurosForm(
          aseguraRespCivil = "aseguraRespCivil",
          polizaRespCivil = "polizaRespCivil",
          aseguraMedAmbiente = Some("aseguraMedAmbiente"),
          polizaMedAmbiente = Some("polizaMedAmbiente"),
          aseguraCarga = Some("aseguraCarga"),
          polizaCarga = Some("polizaCarga"),
          primaSeguro = Some("1")
        ),
        remolques = Option(
          RemolquesForm(
            List(
              RemolqueForm(
                subTipoRem = "subTipoRem",
                placa = "placa"
              )
            )
          )
        )
      )
    ),
    transporteMaritimo = Some(
      TransporteMaritimoForm(
        permSCT = Some("permSCT"),
        numPermisoSCT = Some("numPermisoSCT"),
        nombreAseg = Some("nombreAseg"),
        numPolizaSeguro = Some("numPolizaSeguro"),
        tipoEmbarcacion = "tipoEmbarcacion",
        matricula = "matricula",
        numeroOMI = "numeroOMI",
        anioEmbarcacion = Some(5),
        nombreEmbarc = Some("nombreEmbarc"),
        nacionalidadEmbarc = "nacionalidadEmbarc",
        unidadesDeArqBruto = "6",
        tipoCarga = "tipoCarga",
        numCertITC = "numCertITC",
        eslora = Some("7"),
        manga = Some("8"),
        calado = Some("9"),
        lineaNaviera = Some("lineaNaviera"),
        nombreAgenteNaviero = "nombreAgenteNaviero",
        numAutorizacionNaviero = "numAutorizacionNaviero",
        numViaje = Some("numViaje"),
        numConocEmbarc = Some("numConocEmbarc"),
        contenedor = Seq(
          ContenedorForm(
            matriculaContenedor = "matriculaContenedor",
            tipoContenedor = "tipoContenedor",
            numPrecinto = Some("numPrecinto")
          )
        )
      )
    ),
    transporteAereo = Some(
      TransporteAereoForm(
        permSCT = "permSCT",
        numPermisoSCT = "numPermisoSCT",
        matriculaAeronave = Some("matriculaAeronave"),
        nombreAseg = Some("nombreAseg"),
        numPolizaSeguro = Some("numPolizaSeguro"),
        numeroGuia = "numeroGuia",
        lugarContrato = Some("lugarContrato"),
        codigoTransportista = "codigoTransportista",
        rfcEmbarcador = Some("rfcEmbarcador"),
        numRegIdTribEmbarc = Some("numRegIdTribEmbarc"),
        residenciaFiscalEmbarc = Some("residenciaFiscalEmbarc"),
        nombreEmbarcador = Some("nombreEmbarcador")
      )
    ),
    transporteFerroviario = Some(
      TransporteFerroviarioForm(
        tipoServicio = "tipoServicio",
        tipoDeTrafico = "tipoDeTrafico",
        nombreAseg = Some("nombreAseg"),
        numPolizaSeguro = Some("numPolizaSeguro"),
        derechosDePaso = Seq(
          DerechosDePasoForm(
            tipoDerechoDePaso = "tipoDerechoDePaso",
            kilometrajePagado = "1"
          )
        ),
        carro = Seq(
          CarroForm(
            tipoCarro = "tipoCarro",
            matriculaCarro = "matriculaCarro",
            guiaCarro = "guiaCarro",
            toneladasNetasCarro = "1",
            contenedor = Seq(
              ContenedorCarroForm(
                tipoContenedor = "tipoContenedor",
                pesoContenedorVacio = "1",
                pesoNetoMercancia = "2"
              )
            )
          )
        )
      )
    )
  )
  val pago = v20.PagoForm(
    fechaPago = "2021-04-09T23:25:42",
    formaDePagoP = "formaDePagoP",
    monedaP = "monedaP",
    tipoCambioP = Some("1"),
    monto = "2",
    numOperacion = Some("numOperacion"),
    rfcEmisorCtaOrd = Some("rfcEmisorCtaOrd"),
    nomBancoOrdExt = Some("nomBancoOrdExt"),
    ctaOrdenante = Some("ctaOrdenante"),
    rfcEmisorCtaBen = Some("rfcEmisorCtaBen"),
    ctaBeneficiario = Some("ctaBeneficiario"),
    tipoCadPago = Some("tipoCadPago"),
    certPago = Some("certPago"),
    cadPago = Some("cadPago"),
    selloPago = Some("selloPago"),
    doctosRelacionados = Seq(
      v20.DoctoRelacionadoForm(
        idDocumento = "idDocumento",
        serie = Some("serie"),
        folio = Some("folio"),
        monedaDR = "monedaDR",
        equivalenciaDR = Some("1"),
        numParcialidad = 2,
        impSaldoAnt = "3",
        impPagado = "4",
        impSaldoInsoluto = "5",
        objetoImpDR = "objetoImpDR",
        impuestosDR = Some(
          ImpuestosDRForm(
            retencionesDR = Some(
              RetencionesDRForm(
                retenciones = Seq(
                  RetencionDRForm(
                    baseDR = "1",
                    impuestoDR = "2",
                    tipoFactorDR = "3",
                    tasaOCuotaDR = "4",
                    importeDR = "5"
                  )
                )
              )
            ),
            trasladosDR = Some(
              TrasladosDRForm(
                traslados = Seq(
                  TrasladoDRForm(
                    baseDR = "1",
                    impuestoDR = "2",
                    tipoFactorDR = "3",
                    tasaOCuotaDR = Some("4"),
                    importeDR = Some("5")
                  )
                )
              )
            )
          )
        )
      )
    ),
    impuestosP = Some(
      ImpuestosPForm(
        retencionesP = Some(
          Seq(
            RetencionPForm(
              impuestoP = "impuestoP",
              importeP = "1"
            )
          )
        ),
        trasladosP = Some(
          Seq(
            TrasladoPForm(
              baseP = "1",
              impuestoP = "impuestoP",
              tipoFactorP = "tipoFactorP",
              tasaOCuotaP = Some("2"),
              importeP = Some("3")
            )
          )
        )
      )
    )
  )
  val incapacidadForm: IncapacidadForm = IncapacidadForm(
    diasIncapacidad = 4,
    tipoIncapacidad = "TipoIncapacidad",
    importeMonetario = Some("345")
  )
  val incapacidades: IncapacidadesForm = IncapacidadesForm(
    incapacidad = Seq(incapacidadForm)
  )
  val compensacionSaldosAFavor: CompensacionSaldosFavorForm = CompensacionSaldosFavorForm(
    saldoAFavor = "345",
    anio = 2020.toShort,
    remanenteSalFav = "345"
  )
  val subsidioAlEmpleo: SubsidioAlEmpleoForm = SubsidioAlEmpleoForm(
    subsidioCausado = "24"
  )
  val otroPago: OtroPagoForm = OtroPagoForm(
    tipoOtroPago = "tipoOtroPago",
    clave = "clave",
    concepto = "concepto",
    importe = "34.87",
    subsidioAlEmpleo = Some(subsidioAlEmpleo),
    compensacionSaldosAFavor = Some(compensacionSaldosAFavor)
  )
  val otrospagos: OtrosPagosForm = OtrosPagosForm(
    otroPago = Seq(otroPago)
  )
  val deduccion: DeduccionForm = DeduccionForm(
    tipoDeduccion = "tipoDeduccion",
    clave = "clave",
    concepto = "concepto",
    importe = "34.87"
  )
  val deducciones: DeduccionesForm = DeduccionesForm(
    totalOtrasDeducciones = Some("34.87"),
    totalImpuestosRetenidos = Some("34.89"),
    deduccion = Seq(deduccion)
  )
  val separacionIndemnizacion: SeparacionIndemnizacionForm = SeparacionIndemnizacionForm(
    totalPagado = "34.87",
    numAniosServicio = 4,
    ultimoSueldoMensOrd = "34.88",
    ingresoAcumulable = "34.89",
    ingresoNoAcumulable = "34.90"
  )
  val jubilacionPensionRetiro: JubilacionPensionRetiroForm = JubilacionPensionRetiroForm(
    totalUnaExhibicion = Some("34.87"),
    totalParcialidad = Some("34.88"),
    montoDiario = Some("34.89"),
    ingresoAcumulable = "34.90",
    ingresoNoAcumulable = "34.91"
  )
  val horasExtra: HorasExtraForm = HorasExtraForm(
    dias = 23,
    tipoHoras = "tipoHoras",
    horasExtra = 43,
    importePagado = "45"
  )
  val accionesOTitulos: AccionesOTitulosForm = AccionesOTitulosForm(
    valorMercado = "23.21",
    precioAlOtorgarse = "32.65"
  )
  val percepcion: PercepcionForm = PercepcionForm(
    tipoPercepcion = "tipoPercepcion",
    clave = "clave",
    concepto = "concepto",
    importeGravado = "34",
    importeExento = "242",
    accionesOTitulos = Some(accionesOTitulos),
    horasExtra = Seq(horasExtra)
  )
  val percepciones: PercepcionesForm = PercepcionesForm(
    totalSueldos = Some("1"),
    totalSeparacionIndemnizacion = Some("2"),
    totalJubilacionPensionRetiro = Some("3"),
    totalGravado = "4",
    totalExento = "5",
    percepcion = Seq(percepcion),
    jubilacionPensionRetiro = Some(jubilacionPensionRetiro),
    separacionIndemnizacion = Some(separacionIndemnizacion)
  )
  val subContratacion: SubContratacionForm = SubContratacionForm(
    rfcLabora = "rfcLabora",
    porcentajeTiempo = "34.23"
  )
  val receptorNom: ReceptorNomForm = ReceptorNomForm(
    curp = "curp",
    numSeguridadSocial = Some("numSeguridadSocial"),
    fechaInicioRelLaboral = Some("fechaInicioRelLaboral"),
    antiguedad = Some("antiguedad"),
    tipoContrato = "tipoContrato",
    sindicalizado = Some("sindicalizado"),
    tipoJornada = Some("tipoJornada"),
    tipoRegimen = "tipoRegimen",
    numEmpleado = "numEmpleado",
    departamento = Some("departamento"),
    puesto = Some("puesto"),
    riesgoPuesto = Some("riesgoPuesto"),
    periodicidadPago = "periodicidadPago",
    banco = Some("banco"),
    cuentaBancaria = Some(0),
    salarioBaseCotApor = Some("1"),
    salarioDiarioIntegrado = Some("2"),
    claveEntFed = "claveEntFed",
    subContratacion = Seq(subContratacion)
  )
  val entidadSNCF: EntidadSNCFForm = EntidadSNCFForm(
    origenRecurso = "origenRecurso",
    montoRecursoPropio = Some("1")
  )
  val emisor: EmisorForm = EmisorForm(
    curp = Some("curp"),
    registroPatronal = Some("registroPatronal"),
    rfcPatronOrigen = Some("rfcPatronOrigen"),
    entidadSNCF = Some(entidadSNCF)
  )
  val nomina: NominaCompForm = NominaCompForm(
    version = "1.2",
    tipoNomina = "O",
    fechaPago = "fechaPago",
    fechaInicialPago = "fechaInicialPago",
    fechaFinalPago = "fechaFinalPago",
    numDiasPagados = "1",
    totalPercepciones = Some("2"),
    totalDeducciones = Some("3"),
    totalOtrosPagos = Some("4.0"),
    emisor = Some(emisor),
    receptorNom = receptorNom,
    percepciones = Some(percepciones),
    deducciones = Some(deducciones),
    otrosPagos = Some(otrospagos),
    incapacidades = Some(incapacidades)
  )

  val someCartaPorteForm: Some[CartaPorteForm] = Some(
    CartaPorteForm(
      transpInternac = "transpInternac",
      entradaSalidaMerc = Some("entradaSalidaMerc"),
      paisOrigenDestino = Some("paisOrigenDestino"),
      viaEntradaSalida = Some("viaEntradaSalida"),
      totalDistRec = Some("1"),
      ubicaciones = UbicacionesForm(Seq(ubicacion)),
      mercancias = mercancias,
      figuratransporte = Some(
        FiguratransporteForm(
          tiposFigura = Seq(
            TiposFiguraForm(
              tipoFigura = "tipoFigura",
              rfcFigura = Some("rfcFigura"),
              numLicencia = Some("numLicencia"),
              nombreFigura = Some("nombreFigura"),
              numRegIdTribFigura = Some("numRegIdTribFigura"),
              residenciaFiscalFigura = Some("residenciaFiscalFigura"),
              partesTransporte = Some(
                Seq(
                  PartesTransporteForm(
                    parteTransporte = "parteTransporte"
                  )
                )
              ),
              domicilio = Some(
                DomicilioFiguraForm(
                  calleFigura = Some("calle"),
                  numeroExteriorFigura = Some("numeroExterior"),
                  numeroInteriorFigura = Some("numeroInterior"),
                  coloniaFigura = Some("colonia"),
                  localidadFigura = Some("localidad"),
                  referenciaFigura = Some("referencia"),
                  municipioFigura = Some("municipio"),
                  estadoFigura = "estado",
                  paisFigura = "pais",
                  codigoPostalFigura = "codigoPostal"
                )
              )
            )
          )
        )
      )
    )
  )
  val generateInvoice40Form = GenerateInvoice40Form(
    serie = Some("serie"),
    folio = Some("folio"),
    fecha = Some("fecha"),
    formaPago = Some("formaPago"),
    condicionesDePago = Some("condicionesDePago"),
    subTotal = "1",
    descuento = Some("2"),
    moneda = "MXN",
    tipoCambio = Some("3"),
    total = "4",
    tipoComprobante = "tipoComprobante",
    exportacion = "exportacion",
    metodoPago = Some("metodoPago"),
    lugarExpedicion = "lugarExpedicion",
    confirmacion = Some("confirmacion"),
    informacionGlobal = Some(
      InformacionGlobalForm(
        periodicidad = "periodicidad",
        meses = "meses",
        anio = "1"
      )
    ),
    cfdiRelacionados = Some(List(CfdiRelacionadosForm(tipoRelacion = "tipoRelacion", cfdiRelacionados = List(CfdiRelacionadoForm("uuid"))))),
    receptor = Some(
      ReceptorComprobante40Form(
        rfc = "rfc",
        nombre = "nombre",
        domicilioFiscalReceptor = "domicilioFiscalReceptor",
        residenciaFiscal = Some("residenciaFiscal"),
        numRegIdTrib = Some("numRegIdTrib"),
        regimenFiscalReceptor = "regimenFiscalReceptor",
        usoCfdi = "usoCfdi"
      )
    ),
    conceptos = List(
      Concepto40Form(
        claveProductoServicio = "claveProductoServicio",
        noIdentificacion = Some("noIdentificacion"),
        cantidad = "1",
        claveUnidad = "claveUnidad",
        unidad = Some("unidad"),
        descripcion = "descripcion",
        valorUnitario = "2",
        importe = "3",
        descuento = Some("4"),
        objetoImp = "objetoImp",
        impuestos = Some(
          ConceptosImpuestos40Form(
            traslados = List(
              ConceptoTraslado40Form(
                base = "1",
                impuesto = "impuesto",
                tipoFactor = "tipoFactor",
                tasaOCuota = "2",
                importe = "3"
              )
            ),
            retenciones = List(
              ConceptoRetencion40Form(
                base = "4",
                impuesto = "impuesto",
                tipoFactor = "tipoFactor",
                tasaOCuota = "5",
                importe = "6"
              )
            )
          )
        ),
        aCuentaTerceros = Some(
          ACuentaTercerosForm(
            rfcACuentaTerceros = "rfcACuentaTerceros",
            nombreACuentaTerceros = "nombreACuentaTerceros",
            regimenFiscalACuentaTerceros = "regimenFiscalACuentaTerceros",
            domicilioFiscalACuentaTerceros = "domicilioFiscalACuentaTerceros"
          )
        ),
        informacionAduanera = Some(List(InformacionAduanera("numeroPedimento"))),
        cuentaPredial = Some(List(CuentaPredial("numero"))),
        parte = Some(
          List(
            ParteForm(
              claveProdServ = "claveProdServ",
              noIdentificacion = Some("noIdentificacion"),
              cantidad = "1",
              unidad = Some("unidad"),
              descripcion = "descripcion",
              valorUnitario = Some("2"),
              importe = Some("3"),
              informacionAduaneraForm = List(
                InformacionAduaneraForm(
                  numeroPedimento = "numeroPedimento"
                )
              )
            )
          )
        )
      )
    ),
    impuestos = Some(
      v40.ImpuestosForm(
        totalImpuestosRetenidos = Some("1"),
        totalImpuestosTrasladados = Some("2"),
        retenciones = Some(
          List(
            v40.RetencionForm(
              impuesto = "impuesto",
              importe = "3"
            )
          )
        ),
        traslados = Some(
          Seq(
            v40.TrasladoForm(
              base = "1",
              impuesto = "1",
              tipoFactor = "2",
              tasaOCuota = Some("3"),
              importe = Some("4")
            )
          )
        )
      )
    ),
    complementos = Some(
      ComplementosForm(
        recepcionPagos = Some(
          v20.RecepcionPagoForm(
            totales = TotalesForm(
              totalRetencionesIVA = Some("1"),
              totalRetencionesISR = Some("2"),
              totalRetencionesIEPS = Some("3"),
              totalTrasladosBaseIVA16 = Some("4"),
              totalTrasladosImpuestoIVA16 = Some("5"),
              totalTrasladosBaseIVA8 = Some("6"),
              totalTrasladosImpuestoIVA8 = Some("7"),
              totalTrasladosBaseIVA0 = Some("8"),
              totalTrasladosImpuestoIVA0 = Some("9"),
              totalTrasladosBaseIVAExento = Some("10"),
              montoTotalPagos = Some("11")
            ),
            pagos = Seq(pago)
          )
        ),
        cartaPorte = someCartaPorteForm,
        nomina = Some(
          nomina
        )
      )
    )
  )

}
