package controllers

import scala.concurrent.ExecutionContext

//class ProtectedActionMock()(implicit ec: ExecutionContext) extends ProtectedAction(new BodyParsers.Default(Helpers.stubPlayBodyParsers), null, null) {
//  var executed = false
//  var principal = "principal"
//
//  override def transform[A](request: Request[A]): Future[SignedRequest[A]] = {
//    executed = true
//    Future.successful {
//      SignedRequest(principal, request)
//    }
//  }
//}

class ProtectedActionMock()(implicit ec: ExecutionContext) {
  var executed = false
  var principal = "principal"

//  override def transform[A](request: Request[A]) = {
//    executed = true
//    Future.successful {
//     SignedRequest(principal, request)
//    }
//  }
}
