package controllers.invoices.forms.v40

import com.qrsof.empresalit.controllers.invoices.forms.v40.FormsMapperImpl
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.GenerateInvoiceRequest
import controllers.AppBaseTest
import controllers.TestDataUtils.*

class FormsMapperImplTest extends AppBaseTest {


	"build GenerateInvoiceRequest" - {


		val formsMapperImpl = new FormsMapperImpl

		trait Execution {
			val generateInvoiceRequest: GenerateInvoiceRequest = formsMapperImpl.toGenerateInvoiceRequest("companyKey", generateInvoice40Form)
		}


		"serie" in new Execution {
			generateInvoiceRequest.serie shouldEqual Some("serie")
		}
		"folio" in new Execution {
			generateInvoiceRequest.folio shouldEqual Some("folio")
		}
		"fecha" in new Execution {
			generateInvoiceRequest.fecha shouldEqual Some("fecha")
		}
		"formaPago" in new Execution {
			generateInvoiceRequest.formaPago shouldEqual Some("formaPago")
		}
		"condicionesDePago" in new Execution {
			generateInvoiceRequest.condicionesDePago shouldEqual Some("condicionesDePago")
		}
		"subTotal" in new Execution {
			generateInvoiceRequest.subTotal.toString() shouldEqual "1"
		}
		"descuento" in new Execution {
			generateInvoiceRequest.descuento.get.toString() shouldEqual "2"
		}
		"moneda" in new Execution {
			generateInvoiceRequest.moneda shouldEqual "MXN"
		}
		"tipoCambio" in new Execution {
			generateInvoiceRequest.tipoCambio.get.toString() shouldEqual "3"
		}
		"total" in new Execution {
			generateInvoiceRequest.total.toString() shouldEqual "4"
		}
		"tipoComprobante" in new Execution {
			generateInvoiceRequest.tipoComprobante shouldEqual "tipoComprobante"
		}
		"exportacion" in new Execution {
			generateInvoiceRequest.exportacion shouldEqual "exportacion"
		}
		"metodoPago" in new Execution {
			generateInvoiceRequest.metodoPago.get shouldEqual "metodoPago"
		}
		"lugarExpedicion" in new Execution {
			generateInvoiceRequest.lugarExpedicion shouldEqual "lugarExpedicion"
		}
		"confirmacion" in new Execution {
			generateInvoiceRequest.confirmacion.get shouldEqual "confirmacion"
		}
		"informacionGlobal" - {
			"periodicidad" in new Execution {
				generateInvoiceRequest.informacionGlobal.get.periodicidad shouldEqual "periodicidad"
			}
			"meses" in new Execution {
				generateInvoiceRequest.informacionGlobal.get.meses shouldEqual "meses"
			}
			"anio" in new Execution {
				generateInvoiceRequest.informacionGlobal.get.anio shouldEqual 1
			}
		}

		"cfdiRelacionados" - {
			"tipoRelacion" in new Execution {
				generateInvoiceRequest.cfdiRelacionados.get.head.tipoRelacion shouldEqual "tipoRelacion"
			}
			"cfdiRelacionados" - {
				"uuid" in new Execution {
					generateInvoiceRequest.cfdiRelacionados.get.head.cfdiRelacionado.head.uuid shouldEqual "uuid"
				}
			}
		}

		"receptor" - {
			"rfc" in new Execution {
				generateInvoiceRequest.receptor.get.rfc shouldEqual "rfc"
			}
			"nombre" in new Execution {
				generateInvoiceRequest.receptor.get.nombre shouldEqual "nombre"
			}
			"domicilioFiscalReceptor" in new Execution {
				generateInvoiceRequest.receptor.get.domicilioFiscalReceptor shouldEqual "domicilioFiscalReceptor"
			}
			"residenciaFiscal" in new Execution {
				generateInvoiceRequest.receptor.get.residenciaFiscal.get shouldEqual "residenciaFiscal"
			}
			"numRegIdTrib" in new Execution {
				generateInvoiceRequest.receptor.get.numRegIdTrib.get shouldEqual "numRegIdTrib"
			}
			"regimenFiscalReceptor" in new Execution {
				generateInvoiceRequest.receptor.get.regimenFiscalReceptor shouldEqual "regimenFiscalReceptor"
			}
			"usoCfdi" in new Execution {
				generateInvoiceRequest.receptor.get.usoCfdi shouldEqual "usoCfdi"
			}
		}

		"conceptos" - {
			"claveProductoServicio" in new Execution {
				generateInvoiceRequest.conceptos.head.claveProductoServicio shouldEqual "claveProductoServicio"
			}
			"noIdentificacion" in new Execution {
				generateInvoiceRequest.conceptos.head.noIdentificacion.get shouldEqual "noIdentificacion"
			}
			"cantidad" in new Execution {
				generateInvoiceRequest.conceptos.head.cantidad.toString() shouldEqual "1"
			}
			"claveUnidad" in new Execution {
				generateInvoiceRequest.conceptos.head.claveUnidad shouldEqual "claveUnidad"
			}
			"unidad" in new Execution {
				generateInvoiceRequest.conceptos.head.unidad.get shouldEqual "unidad"
			}
			"descripcion" in new Execution {
				generateInvoiceRequest.conceptos.head.descripcion shouldEqual "descripcion"
			}
			"valorUnitario" in new Execution {
				generateInvoiceRequest.conceptos.head.valorUnitario.toString() shouldEqual "2"
			}
			"importe" in new Execution {
				generateInvoiceRequest.conceptos.head.importe.toString() shouldEqual "3"
			}
			"descuento" in new Execution {
				generateInvoiceRequest.conceptos.head.descuento.get.toString() shouldEqual "4"
			}
			"objetoImp" in new Execution {
				generateInvoiceRequest.conceptos.head.objetoImp shouldEqual "objetoImp"
			}
			"impuestos" - {
				"traslados" - {
					"base" in new Execution {
						generateInvoiceRequest.conceptos.head.impuestos.get.traslados.head.base.toString() shouldEqual "1"
					}
					"impuesto" in new Execution {
						generateInvoiceRequest.conceptos.head.impuestos.get.traslados.head.impuesto shouldEqual "impuesto"
					}
					"tipoFactor" in new Execution {
						generateInvoiceRequest.conceptos.head.impuestos.get.traslados.head.tipoFactor shouldEqual "tipoFactor"
					}
					"tasaCuota" in new Execution {
						generateInvoiceRequest.conceptos.head.impuestos.get.traslados.head.tasaCuota.toString() shouldEqual "2"
					}
					"importe" in new Execution {
						generateInvoiceRequest.conceptos.head.impuestos.get.traslados.head.importe.toString() shouldEqual "3"
					}
				}
				"retenciones" - {
					"base" in new Execution {
						generateInvoiceRequest.conceptos.head.impuestos.get.retenciones.head.base.toString() shouldEqual "4"
					}
					"impuesto" in new Execution {
						generateInvoiceRequest.conceptos.head.impuestos.get.retenciones.head.impuesto shouldEqual "impuesto"
					}
					"tipoFactor" in new Execution {
						generateInvoiceRequest.conceptos.head.impuestos.get.retenciones.head.tipoFactor shouldEqual "tipoFactor"
					}
					"tasaCuota" in new Execution {
						generateInvoiceRequest.conceptos.head.impuestos.get.retenciones.head.tasaCuota.toString() shouldEqual "5"
					}
					"importe" in new Execution {
						generateInvoiceRequest.conceptos.head.impuestos.get.retenciones.head.importe.toString() shouldEqual "6"
					}
				}
			}
		}

		"impuestos" - {
			"totalImpuestosRetenidos" in new Execution {
				generateInvoiceRequest.impuestos.get.totalImpuestosRetenidos.get.toString() shouldEqual "1"
			}
			"totalImpuestosTrasladados" in new Execution {
				generateInvoiceRequest.impuestos.get.totalImpuestosTrasladados.get.toString() shouldEqual "2"
			}
			"retenciones" - {
				"impuesto" in new Execution {
					generateInvoiceRequest.impuestos.get.retenciones.get.head.impuesto shouldEqual "impuesto"
				}
				"importe" in new Execution {
					generateInvoiceRequest.impuestos.get.retenciones.get.head.importe.toString() shouldEqual "3"
				}
			}
		}

		"complementos" - {
			"recepcionPagos20" - {
				"version" in new Execution {
					generateInvoiceRequest.complementos.get.recepcionPagos20.get.version shouldEqual "2.0"
				}
				"totales" - {
					"totalRetencionesIVA" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.totalRetencionesIVA.get.toString() shouldEqual "1"
					}
					"totalRetencionesISR" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.totalRetencionesISR.get.toString() shouldEqual "2"
					}
					"totalRetencionesIEPS" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.totalRetencionesIEPS.get.toString() shouldEqual "3"
					}
					"totalTrasladosBaseIVA16" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.totalTrasladosBaseIVA16.get.toString() shouldEqual "4"
					}
					"totalTrasladosImpuestoIVA16" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.totalTrasladosImpuestoIVA16.get.toString() shouldEqual "5"
					}
					"totalTrasladosBaseIVA8" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.totalTrasladosBaseIVA8.get.toString() shouldEqual "6"
					}
					"totalTrasladosImpuestoIVA8" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.totalTrasladosImpuestoIVA8.get.toString() shouldEqual "7"
					}
					"totalTrasladosBaseIVA0" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.totalTrasladosBaseIVA0.get.toString() shouldEqual "8"
					}
					"totalTrasladosImpuestoIVA0" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.totalTrasladosImpuestoIVA0.get.toString() shouldEqual "9"
					}
					"totalTrasladosBaseIVAExento" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.totalTrasladosBaseIVAExento.get.toString() shouldEqual "10"
					}
					"montoTotalPagos" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.totales.montoTotalPagos.get.toString() shouldEqual "11"
					}

				}
				"pagos" - {
					"fechaPago" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.fechaPago shouldEqual "2021-04-09T23:25:42"
					}
					"formaDePagoP" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.formaDePagoP shouldEqual "formaDePagoP"
					}
					"monedaP" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.monedaP shouldEqual "monedaP"
					}
					"tipoCambioP" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.tipoCambioP.get.toString shouldEqual "1"
					}
					"monto" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.monto.toString shouldEqual "2"
					}
					"numOperacion" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.numOperacion.get shouldEqual "numOperacion"
					}
					"rfcEmisorCtaOrd" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.rfcEmisorCtaOrd.get shouldEqual "rfcEmisorCtaOrd"
					}
					"nomBancoOrdExt" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.nomBancoOrdExt.get shouldEqual "nomBancoOrdExt"
					}
					"ctaOrdenante" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.ctaOrdenante.get shouldEqual "ctaOrdenante"
					}
					"rfcEmisorCtaBen" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.rfcEmisorCtaBen.get shouldEqual "rfcEmisorCtaBen"
					}
					"ctaBeneficiario" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.ctaBeneficiario.get shouldEqual "ctaBeneficiario"
					}
					"tipoCadPago" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.tipoCadPago.get shouldEqual "tipoCadPago"
					}
					"certPago" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.certPago.get shouldEqual "certPago"
					}
					"cadPago" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.cadPago.get shouldEqual "cadPago"
					}
					"selloPago" in new Execution {
						generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.selloPago.get shouldEqual "selloPago"
					}
					"doctosRelacionesdos" - {

						"idDocumento" in new Execution {
							generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.idDocumento shouldEqual "idDocumento"
						}
						"serie" in new Execution {
							generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.serie.get shouldEqual "serie"
						}
						"folio" in new Execution {
							generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.folio.get shouldEqual "folio"
						}
						"monedaDR" in new Execution {
							generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.monedaDR shouldEqual "monedaDR"
						}
						"equivalenciaDR" in new Execution {
							generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.equivalenciaDR.get.toString shouldEqual "1"
						}
						"numParcialidad" in new Execution {
							generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.numParcialidad.toString() shouldEqual "2"
						}
						"impSaldoAnt" in new Execution {
							generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impSaldoAnt.toString() shouldEqual "3"
						}
						"impPagado" in new Execution {
							generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impPagado.toString() shouldEqual "4"
						}
						"impSaldoInsoluto" in new Execution {
							generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impSaldoInsoluto.toString() shouldEqual "5"
						}
						"objetoImpDR" in new Execution {
							generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.objetoImpDR shouldEqual "objetoImpDR"
						}
						"impuestosDR" - {
							"retencionesDR" - {
								"retencionDR" - {
									"baseDR" in new Execution {
										generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impuestosDR.get.retencionesDR.get.retenciones.head.baseDR.toString() shouldEqual "1"
									}
									"impuestoDR" in new Execution {
										generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impuestosDR.get.retencionesDR.get.retenciones.head.impuestoDR shouldEqual "2"
									}
									"tipoFactorDR" in new Execution {
										generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impuestosDR.get.retencionesDR.get.retenciones.head.tipoFactorDR shouldEqual "3"
									}
									"tasaOCuotaDR" in new Execution {
										generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impuestosDR.get.retencionesDR.get.retenciones.head.tasaOCuotaDR.toString() shouldEqual "4"
									}
									"importeDR" in new Execution {
										generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impuestosDR.get.retencionesDR.get.retenciones.head.importeDR.toString() shouldEqual "5"
									}
								}
							}
							"trasladosDR" - {
								"trasladoDR" - {
									"baseDR" in new Execution {
										generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impuestosDR.get.trasladosDR.get.traslados.head.baseDR.toString() shouldEqual "1"
									}
									"impuestoDR" in new Execution {
										generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impuestosDR.get.trasladosDR.get.traslados.head.impuestoDR shouldEqual "2"
									}
									"tipoFactorDR" in new Execution {
										generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impuestosDR.get.trasladosDR.get.traslados.head.tipoFactorDR shouldEqual "3"
									}
									"tasaOCuotaDR" in new Execution {
										generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impuestosDR.get.trasladosDR.get.traslados.head.tasaOCuotaDR.get.toString() shouldEqual "4"
									}
									"importeDR" in new Execution {
										generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.doctosRelacionesdos.head.impuestosDR.get.trasladosDR.get.traslados.head.importeDR.get.toString() shouldEqual "5"
									}
								}
							}
						}
					}
					"impuestosP" - {
						"retencionesP" - {
							"impuestoP" in new Execution {
								generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.impuestosP.get.retencionesP.get.head.impuestoP shouldEqual "impuestoP"
							}
							"importeP" in new Execution {
								generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.impuestosP.get.retencionesP.get.head.importeP.toString() shouldEqual "1"
							}
						}
						"trasladosP" - {
							"baseP" in new Execution {
								generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.impuestosP.get.trasladosP.get.head.baseP.toString() shouldEqual "1"
							}
							"impuestoP" in new Execution {
								generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.impuestosP.get.trasladosP.get.head.impuestoP shouldEqual "impuestoP"
							}
							"tipoFactorP" in new Execution {
								generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.impuestosP.get.trasladosP.get.head.tipoFactorP shouldEqual "tipoFactorP"
							}
							"tasaOCuotaP" in new Execution {
								generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.impuestosP.get.trasladosP.get.head.tasaOCuotaP.get.toString() shouldEqual "2"
							}
							"importeP" in new Execution {
								generateInvoiceRequest.complementos.get.recepcionPagos20.get.pagos.head.impuestosP.get.trasladosP.get.head.importeP.get.toString() shouldEqual "3"
							}
						}
					}
				}
			}
			"cartaPorte20" - {
				"version" in new Execution {
					generateInvoiceRequest.complementos.get.cartaPorte20.get.version shouldEqual "2.0"
				}
				"TranspInternac" in new Execution {
					generateInvoiceRequest.complementos.get.cartaPorte20.get.transpInternac shouldEqual "transpInternac"
				}
				"EntradaSalidaMerc" in new Execution {
					generateInvoiceRequest.complementos.get.cartaPorte20.get.entradaSalidaMerc.get shouldEqual "entradaSalidaMerc"
				}
				"PaisOrigenDestino" in new Execution {
					generateInvoiceRequest.complementos.get.cartaPorte20.get.paisOrigenDestino.get shouldEqual "paisOrigenDestino"
				}
				"ViaEntradaSalida" in new Execution {
					generateInvoiceRequest.complementos.get.cartaPorte20.get.viaEntradaSalida.get shouldEqual "viaEntradaSalida"
				}
				"TotalDistRec" in new Execution {
					generateInvoiceRequest.complementos.get.cartaPorte20.get.totalDistRec.get.toString() shouldEqual "1"
				}
				"Ubicaciones" - {
					"TipoUbicacion" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.tipoUbicacion shouldEqual "tipoUbicacion"
					}
					"IDUbicacion" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.idUbicacion.get shouldEqual "idUbicacion"
					}
					"RFCRemitenteDestinatario" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.rfcRemitenteDestinatario shouldEqual "rfcRemitenteDestinatario"
					}
					"NombreRemitenteDestinatario" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.nombreRemitenteDestinatario.get shouldEqual "nombreRemitenteDestinatario"
					}
					"NumRegIdTrib" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.numRegIdTrib.get shouldEqual "numRegIdTrib"
					}
					"ResidenciaFiscal" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.residenciaFiscal.get shouldEqual "residenciaFiscal"
					}
					"NumEstacion" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.numEstacion.get shouldEqual "numEstacion"
					}
					"NombreEstacion" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.nombreEstacion.get shouldEqual "nombreEstacion"
					}
					"NavegacionTrafico" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.navegacionTrafico.get shouldEqual "navegacionTrafico"
					}
					"FechaHoraSalidaLlegada" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.fechaHoraSalidaLlegada shouldEqual "fechaHoraSalidaLlegada"
					}
					"TipoEstacion" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.tipoEstacion.get shouldEqual "tipoEstacion"
					}
					"DistanciaRecorrida" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.distanciaRecorrida.get.toString() shouldEqual "1"
					}
					"domicilio" - {
						"Calle" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.domicilio.get.calle.get shouldEqual "calle"
						}
						"numeroExterior" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.domicilio.get.numeroExterior.get shouldEqual "numeroExterior"
						}
						"numeroInterior" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.domicilio.get.numeroInterior.get shouldEqual "numeroInterior"
						}
						"colonia" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.domicilio.get.colonia.get shouldEqual "colonia"
						}
						"localidad" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.domicilio.get.localidad.get shouldEqual "localidad"
						}
						"referencia" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.domicilio.get.referencia.get shouldEqual "referencia"
						}
						"municipio" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.domicilio.get.municipio.get shouldEqual "municipio"
						}
						"estado" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.domicilio.get.estado shouldEqual "estado"
						}
						"pais" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.domicilio.get.pais shouldEqual "pais"
						}
						"codigoPostal" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.ubicaciones.ubicacion.head.domicilio.get.codigoPostal shouldEqual "codigoPostal"
						}


					}
				}
				"Mercancias" - {
					"PesoBrutoTotal" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.pesoBrutoTotal.toString() shouldEqual "1"
					}
					"UnidadPeso" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.unidadPeso shouldEqual "unidadPeso"
					}
					"PesoNetoTotal" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.pesoNetoTotal.get.toString() shouldEqual "2"
					}
					"NumTotalMercancias" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.numTotalMercancias.toString shouldEqual "3"
					}
					"CargoPorTasacion" in new Execution {
						generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.cargoPorTasacion.get.toString shouldEqual "4"
					}
					"mercancia" - {
						"BienesTransp" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.bienesTransp shouldEqual "bienesTransp"
						}
						"ClaveSTCC" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.clavesSTCC.get shouldEqual "clavesSTCC"
						}
						"Descripcion" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.descripcion shouldEqual "descripcion"
						}
						"Cantidad" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.cantidad.toString() shouldEqual "1"
						}
						"ClaveUnidad" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.claveUnidad shouldEqual "claveUnidad"
						}
						"Unidad" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.unidad.get shouldEqual "unidad"
						}
						"Dimensiones" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.dimensiones.get shouldEqual "dimensiones"
						}
						"MaterialPeligroso" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.materialPeligroso.get shouldEqual "materialPeligroso"
						}
						"CveMaterialPeligroso" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.cveMaterialPeligroso.get shouldEqual "cveMaterialPeligroso"
						}
						"Embalaje" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.embalaje.get shouldEqual "embalaje"
						}
						"DescripEmbalaje" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.descripEmbalaje.get shouldEqual "descripEmbalaje"
						}
						"PesoEnKg" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.pesoEnKg.toString() shouldEqual "2"
						}
						"ValorMercancia" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.valorMercancia.get.toString() shouldEqual "3"
						}
						"Moneda" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.moneda.get shouldEqual "moneda"
						}
						"FraccionArancelaria" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.fraccionArancelaria.get shouldEqual "fraccionArancelaria"
						}
						"UUIDComercioExt" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.uuidComercioExt.get shouldEqual "uuidComercioExt"
						}
						"Pedimentos" - {
							"Pedimento" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.pedimentos.head.pedimento shouldEqual "pedimento"
							}
						}
						"GuiasIdentificacion" - {
							"NumeroGuiaIdentificacion" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.guiasIdentificacion.head.numeroGuiaIdentificacion shouldEqual "numeroGuiaIdentificacion"
							}
							"DescripGuiaIdentificacion" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.guiasIdentificacion.head.descripGuiaIdentificacion shouldEqual "descripGuiaIdentificacion"
							}
							"PesoGuiaIdentificacion" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.guiasIdentificacion.head.pesoGuiaIdentificacion.toString() shouldEqual "1"
							}
						}
						"CantidadTransporta" - {
							"Cantidad" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.cantidadTransporta.head.cantidad.toString() shouldEqual "1"
							}
							"IDOrigen" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.cantidadTransporta.head.idOrigen shouldEqual "idOrigen"
							}
							"IDDestino" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.cantidadTransporta.head.idDestino shouldEqual "idDestino"
							}
							"CvesTransporte" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.cantidadTransporta.head.cvesTransporte.get shouldEqual "cvesTransporte"
							}
						}
						"DetalleMercancia" - {
							"UnidadPesoMerc" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.detalleMercancia.head.unidadPesoMerc shouldEqual "unidadPesoMerc"
							}
							"PesoBruto" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.detalleMercancia.head.pesoBruto.toString() shouldEqual "1"
							}
							"PesoNeto" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.detalleMercancia.head.pesoNeto.toString() shouldEqual "2"
							}
							"PesoTara" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.detalleMercancia.head.pesoTara.toString() shouldEqual "3"
							}
							"NumPiezas" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.mercancia.head.detalleMercancia.head.numPiezas.get shouldEqual 4
							}
						}
					}
					"Autotransporte" - {
						"PermSCT" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.permSCT shouldEqual "permSCT"
						}
						"NumPermisoSCT" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.numPermisoSCT shouldEqual "numPermisoSCT"
						}
						"IdentificacionVehicular" - {
							"ConfigVehicular" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.identificacionVehicular.configVehicular shouldEqual "configVehicular"
							}
							"PlacaVM" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.identificacionVehicular.placaVM shouldEqual "placaVM"
							}
							"AnioModeloVM" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.identificacionVehicular.anioModeloVM.toString shouldEqual "1"
							}
						}
						"Seguros" - {
							"AseguraRespCivil" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.seguros.aseguraRespCivil shouldEqual "aseguraRespCivil"
							}
							"PolizaRespCivil" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.seguros.polizaRespCivil shouldEqual "polizaRespCivil"
							}
							"AseguraMedAmbiente" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.seguros.aseguraMedAmbiente.get shouldEqual "aseguraMedAmbiente"
							}
							"PolizaMedAmbiente" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.seguros.polizaMedAmbiente.get shouldEqual "polizaMedAmbiente"
							}
							"AseguraCarga" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.seguros.aseguraCarga.get shouldEqual "aseguraCarga"
							}
							"PolizaCarga" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.seguros.polizaCarga.get shouldEqual "polizaCarga"
							}
							"PrimaSeguro" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.seguros.primaSeguro.get.toString() shouldEqual "1"
							}
						}
						"Remolques" - {
							"SubTipoRem" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.remolques.get.remolque.head.subTipoRem shouldEqual "subTipoRem"
							}
							"Placa" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.autotransporte.get.remolques.get.remolque.head.placa shouldEqual "placa"
							}
						}
					}
					"TransporteMaritimo" - {
						"PermSCT" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.permSCT.get shouldEqual "permSCT"
						}
						"NumPermisoSCT" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.numPermisoSCT.get shouldEqual "numPermisoSCT"
						}
						"NombreAseg" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.nombreAseg.get shouldEqual "nombreAseg"
						}
						"NumPolizaSeguro" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.numPolizaSeguro.get shouldEqual "numPolizaSeguro"
						}
						"TipoEmbarcacion" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.tipoEmbarcacion shouldEqual "tipoEmbarcacion"
						}
						"Matricula" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.matricula shouldEqual "matricula"
						}
						"AnioEmbarcacion" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.anioEmbarcacion.get shouldEqual 5
						}
						"NombreEmbarc" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.nombreEmbarc.get shouldEqual "nombreEmbarc"
						}
						"NacionalidadEmbarc" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.nacionalidadEmbarc shouldEqual "nacionalidadEmbarc"
						}
						"UnidadesDeArqBruto" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.unidadesDeArqBruto.toString() shouldEqual "6"
						}
						"TipoCarga" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.tipoCarga shouldEqual "tipoCarga"
						}
						"NumCertITC" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.numCertITC shouldEqual "numCertITC"
						}
						"Eslora" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.eslora.get.toString() shouldEqual "7"
						}
						"Manga" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.manga.get.toString() shouldEqual "8"
						}
						"Calado" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.calado.get.toString() shouldEqual "9"
						}
						"LineaNaviera" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.lineaNaviera.get shouldEqual "lineaNaviera"
						}
						"NombreAgenteNaviero" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.nombreAgenteNaviero shouldEqual "nombreAgenteNaviero"
						}
						"NumAutorizacionNaviero" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.numAutorizacionNaviero shouldEqual "numAutorizacionNaviero"
						}
						"NumViaje" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.numViaje.get shouldEqual "numViaje"
						}
						"NumConocEmbarc" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.numConocEmbarc.get shouldEqual "numConocEmbarc"
						}
						"Contenedor" - {
							"MatriculaContenedor" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.contenedor.head.matriculaContenedor shouldEqual "matriculaContenedor"
							}
							"TipoContenedor" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.contenedor.head.tipoContenedor shouldEqual "tipoContenedor"
							}
							"NumPrecinto" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteMaritimo.get.contenedor.head.numPrecinto.get shouldEqual "numPrecinto"
							}
						}
					}
					"TransporteAereo" - {
						"PermSCT" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.permSCT shouldEqual "permSCT"
						}
						"NumPermisoSCT" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.numPermisoSCT shouldEqual "numPermisoSCT"
						}
						"MatriculaAeronave" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.matriculaAeronave.get shouldEqual "matriculaAeronave"
						}
						"NombreAseg" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.nombreAseg.get shouldEqual "nombreAseg"
						}
						"NumPolizaSeguro" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.numPolizaSeguro.get shouldEqual "numPolizaSeguro"
						}
						"NumeroGuia" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.numeroGuia shouldEqual "numeroGuia"
						}
						"LugarContrato" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.lugarContrato.get shouldEqual "lugarContrato"
						}
						"CodigoTransportista" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.codigoTransportista shouldEqual "codigoTransportista"
						}
						"RFCEmbarcador" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.rfcEmbarcador.get shouldEqual "rfcEmbarcador"
						}
						"NumRegIdTribEmbarc" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.numRegIdTribEmbarc.get shouldEqual "numRegIdTribEmbarc"
						}
						"ResidenciaFiscalEmbarc" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.residenciaFiscalEmbarc.get shouldEqual "residenciaFiscalEmbarc"
						}
						"NombreEmbarcador" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteAereo.get.nombreEmbarcador.get shouldEqual "nombreEmbarcador"
						}
					}
					"TransporteFerroviario" - {
						"TipoDeServicio" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.tipoServicio shouldEqual "tipoServicio"
						}
						"TipoDeTrafico" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.tipoDeTrafico shouldEqual "tipoDeTrafico"
						}
						"NombreAseg" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.nombreAseg.get shouldEqual "nombreAseg"
						}
						"NumPolizaSeguro" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.numPolizaSeguro.get shouldEqual "numPolizaSeguro"
						}
						"DerechosDePaso" - {
							"TipoDerechoDePaso" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.derechosDePaso.head.tipoDerechoDePaso shouldEqual "tipoDerechoDePaso"
							}
							"KilometrajePagado" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.derechosDePaso.head.kilometrajePagado.toString() shouldEqual "1"
							}
						}
						"Carro" - {
							"TipoCarro" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.carro.head.tipoCarro shouldEqual "tipoCarro"
							}
							"MatriculaCarro" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.carro.head.matriculaCarro shouldEqual "matriculaCarro"
							}
							"GuiaCarro" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.carro.head.guiaCarro shouldEqual "guiaCarro"
							}
							"ToneladasNetasCarro" in new Execution {
								generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.carro.head.toneladasNetasCarro.toString() shouldEqual "1"
							}
							"Contenedor" - {
								"TipoContenedor" in new Execution {
									generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.carro.head.contenedor.head.tipoContenedor shouldEqual "tipoContenedor"
								}
								"PesoContenedorVacio" in new Execution {
									generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.carro.head.contenedor.head.pesoContenedorVacio.toString() shouldEqual "1"
								}
								"PesoNetoMercancia" in new Execution {
									generateInvoiceRequest.complementos.get.cartaPorte20.get.mercancias.transporteFerroviario.get.carro.head.contenedor.head.pesoNetoMercancia.toString() shouldEqual "2"
								}
							}
						}
					}
				}
				"FiguraTransporte" - {
					"TipoFigura" - {
						"TipoFigura" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.figuratransporte.get.tiposFigura.head.tipoFigura shouldEqual "tipoFigura"
						}
						"RFCFigura" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.figuratransporte.get.tiposFigura.head.rfcFigura.get shouldEqual "rfcFigura"
						}
						"NumLicencia" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.figuratransporte.get.tiposFigura.head.numLicencia.get shouldEqual "numLicencia"
						}
						"NombreFigura" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.figuratransporte.get.tiposFigura.head.nombreFigura.get shouldEqual "nombreFigura"
						}
						"NumRegIdTribFigura" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.figuratransporte.get.tiposFigura.head.numRegIdTribFigura.get shouldEqual "numRegIdTribFigura"
						}
						"ResidenciaFiscalFigura" in new Execution {
							generateInvoiceRequest.complementos.get.cartaPorte20.get.figuratransporte.get.tiposFigura.head.residenciaFiscalFigura.get shouldEqual "residenciaFiscalFigura"
						}
					}
				}
			}
			"nomina12" - {
				"Version" in new Execution {
					generateInvoiceRequest.complementos.get.nomina12.get.version shouldEqual "1.2"
				}
				"tipoNomina" in new Execution {
					generateInvoiceRequest.complementos.get.nomina12.get.tipoNomina shouldEqual "O"
				}
				"FechaPago" in new Execution {
					generateInvoiceRequest.complementos.get.nomina12.get.fechaPago shouldEqual "fechaPago"
				}
				"FechaInicialPago" in new Execution {
					generateInvoiceRequest.complementos.get.nomina12.get.fechaInicialPago shouldEqual "fechaInicialPago"
				}
				"FechaFinalPago" in new Execution {
					generateInvoiceRequest.complementos.get.nomina12.get.fechaFinalPago shouldEqual "fechaFinalPago"
				}
				"NumDiasPagados" in new Execution {
					generateInvoiceRequest.complementos.get.nomina12.get.numDiasPagados.toString() shouldEqual "1"
				}
				"TotalPercepciones" in new Execution {
					generateInvoiceRequest.complementos.get.nomina12.get.totalPercepciones.get.toString() shouldEqual "2"
				}
				"TotalDeducciones" in new Execution {
					generateInvoiceRequest.complementos.get.nomina12.get.totalDeducciones.get.toString() shouldEqual "3"
				}
				"TotalOtrosPagos" in new Execution {
					generateInvoiceRequest.complementos.get.nomina12.get.totalOtrosPagos.get.toString() shouldEqual "4.0"
				}
				"Emisor" - {
					"Curp" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.emisor.get.curp.get shouldEqual "curp"
					}
					"RegistroPatronal" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.emisor.get.registroPatronal.get shouldEqual "registroPatronal"
					}
					"RfcPatronOrigen" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.emisor.get.rfcPatronOrigen.get shouldEqual "rfcPatronOrigen"
					}
					"EntidadSNCF" - {
						"OrigenRecurso" in new Execution {
							generateInvoiceRequest.complementos.get.nomina12.get.emisor.get.entidadSNCF.get.origenRecurso shouldEqual "origenRecurso"
						}
						"MontoRecursoPropio" in new Execution {
							generateInvoiceRequest.complementos.get.nomina12.get.emisor.get.entidadSNCF.get.montoRecursoPropio.get.toString() shouldEqual "1"
						}
					}
				}
				"Receptor" - {
					"Curp" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.curp shouldEqual "curp"
					}
					"NumSeguridadSocial" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.numSeguridadSocial.get shouldEqual "numSeguridadSocial"
					}
					"FechaInicioRelLaboral" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.fechaInicioRelLaboral.get shouldEqual "fechaInicioRelLaboral"
					}
					"Antigüedad" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.antiguedad.get shouldEqual "antiguedad"
					}
					"TipoContrato" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.tipoContrato shouldEqual "tipoContrato"
					}
					"Sindicalizado" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.sindicalizado.get shouldEqual "sindicalizado"
					}
					"TipoJornada" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.tipoJornada.get shouldEqual "tipoJornada"
					}
					"TipoRegimen" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.tipoRegimen shouldEqual "tipoRegimen"
					}
					"NumEmpleado" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.numEmpleado shouldEqual "numEmpleado"
					}
					"Departamento" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.departamento.get shouldEqual "departamento"
					}
					"Puesto" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.puesto.get shouldEqual "puesto"
					}
					"RiesgoPuesto" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.riesgoPuesto.get shouldEqual "riesgoPuesto"
					}
					"PeriodicidadPago" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.periodicidadPago shouldEqual "periodicidadPago"
					}
					"Banco" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.banco.get shouldEqual "banco"
					}
					"CuentaBancaria" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.cuentaBancaria.get shouldEqual 0
					}
					"SalarioBaseCotApor" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.salarioBaseCotApor.get.toString() shouldEqual "1"
					}
					"SalarioDiarioIntegrado" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.salarioDiarioIntegrado.get.toString() shouldEqual "2"
					}
					"ClaveEntFed" in new Execution {
						generateInvoiceRequest.complementos.get.nomina12.get.receptor.claveEntFed shouldEqual "claveEntFed"
					}
					"SubContratacion" - {
						"RfcLabora" in new Execution {
							generateInvoiceRequest.complementos.get.nomina12.get.receptor.subContratacion.head.rfcLabora shouldEqual "rfcLabora"
						}
						"PorcentajeTiempo" in new Execution {
							generateInvoiceRequest.complementos.get.nomina12.get.receptor.subContratacion.head.porcentajeTiempo.toString() shouldEqual "34.23"
						}
					}
					"Percepciones" - {
						"TotalSueldos" in new Execution {
							generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.totalSueldos.get.toString() shouldEqual "1"
						}
						"TotalSeparacionIndemnizacion" in new Execution {
							generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.totalSeparacionIndemnizacion.get.toString() shouldEqual "2"
						}
						"TotalJubilacionPensionRetiro" in new Execution {
							generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.totalJubilacionPensionRetiro.get.toString() shouldEqual "3"
						}
						"TotalGravado" in new Execution {
							generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.totalGravado.toString() shouldEqual "4"
						}
						"TotalExento" in new Execution {
							generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.totalExento.toString() shouldEqual "5"
						}
						"Percepcion" - {
							"TipoPercepcion" in new Execution {
								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.tipoPercepcion shouldEqual "tipoPercepcion"
							}
							"Clave" in new Execution {
								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.clave shouldEqual "clave"
							}
							"Concepto" in new Execution {
								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.concepto shouldEqual "concepto"
							}
							"ImporteGravado" in new Execution {
								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.importeGravado.toString() shouldEqual "34"
							}
							"ImporteExento" in new Execution {
								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.importeExento.toString() shouldEqual "242"
							}
							"AccionesOTitulos" - {
								"ValorMercado" in new Execution {
									generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.accionesOTitulos.get.valorMercado.toString() shouldEqual "23.21"
								}
								"PrecioAlOtorgarse" in new Execution {
									generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.accionesOTitulos.get.precioAlOtorgarse.toString() shouldEqual "32.65"
								}
							}
							"HorasExtra" - {
								"Dias" in new Execution {
									generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.horasExtra.head.dias shouldEqual 23
								}
								"TipoHoras" in new Execution {
									generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.horasExtra.head.tipoHoras shouldEqual "tipoHoras"
								}
								"HorasExtra" in new Execution {
									generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.horasExtra.head.horasExtra shouldEqual 43
								}
								"ImportePagado" in new Execution {
									generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.percepcion.head.horasExtra.head.importePagado.toString() shouldEqual "45"
								}
							}
						}
						"JubilacionPensionRetiro" - {
							"TotalUnaExhibicion" in new Execution {
								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.jubilacionPensionRetiro.get.totalUnaExhibicion.get.toString() shouldEqual "34.87"
							}
							"TotalParcialidad" in new Execution {
								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.jubilacionPensionRetiro.get.totalParcialidad.get.toString() shouldEqual "34.88"
							}
							"MontoDiario" in new Execution {
								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.jubilacionPensionRetiro.get.montoDiario.get.toString() shouldEqual "34.89"
							}
							"IngresoAcumulable" in new Execution {
								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.jubilacionPensionRetiro.get.ingresoAcumulable.toString() shouldEqual "34.90"
							}
							"IngresoNoAcumulable" in new Execution {
								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.jubilacionPensionRetiro.get.ingresoNoAcumulable.toString() shouldEqual "34.91"
							}
						}
//						"SeparacionIndemnizacion" - {
//							"TotalPagado" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.separacionIndemnizacion.get.totalPagado.toString() shouldEqual separacionIndemnizacion.totalPagado
//							}
//							"NumAñosServicio" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.separacionIndemnizacion.get.numAniosServicio shouldEqual separacionIndemnizacion.numAniosServicio
//							}
//							"UltimoSueldoMensOrd" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.separacionIndemnizacion.get.ultimoSueldoMensOrd.toString() shouldEqual separacionIndemnizacion.ultimoSueldoMensOrd
//							}
//							"IngresoAcumulable" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.separacionIndemnizacion.get.ingresoAcumulable.toString() shouldEqual separacionIndemnizacion.ingresoAcumulable
//							}
//							"IngresoNoAcumulable" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.percepciones.get.separacionIndemnizacion.get.ingresoNoAcumulable.toString() shouldEqual separacionIndemnizacion.ingresoNoAcumulable
//							}
//						}
					}
//					"Deducciones" - {
//						"TotalOtrasDeducciones" in new Execution {
//							generateInvoiceRequest.complementos.get.nomina12.get.deducciones.get.totalOtrasDeducciones.get.toString() shouldEqual deducciones.totalOtrasDeducciones.get
//						}
//						"TotalImpuestosRetenidos" in new Execution {
//							generateInvoiceRequest.complementos.get.nomina12.get.deducciones.get.totalImpuestosRetenidos.get.toString() shouldEqual deducciones.totalImpuestosRetenidos.get
//						}
//						"Deduccion" - {
//							"TipoDeduccion" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.deducciones.get.deduccion.head.tipoDeduccion shouldEqual deducciones.deduccion.head.tipoDeduccion
//							}
//							"Clave" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.deducciones.get.deduccion.head.clave shouldEqual deducciones.deduccion.head.clave
//							}
//							"Concepto" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.deducciones.get.deduccion.head.concepto shouldEqual deducciones.deduccion.head.concepto
//							}
//							"Importe" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.deducciones.get.deduccion.head.importe.toString() shouldEqual deducciones.deduccion.head.importe
//							}
//						}
//
//					}
//					"OtrosPagos" - {
//						"OtroPago" - {
//							"TipoOtroPago" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.otrosPagos.get.otroPago.head.tipoOtroPago shouldEqual otroPago.tipoOtroPago
//							}
//							"Clave" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.otrosPagos.get.otroPago.head.clave shouldEqual otroPago.clave
//							}
//							"Concepto" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.otrosPagos.get.otroPago.head.concepto shouldEqual otroPago.concepto
//							}
//							"Importe" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.otrosPagos.get.otroPago.head.importe.toString() shouldEqual otroPago.importe
//							}
//							"SubsidioAlEmpleo" - {
//								"SubsidioCausado" in new Execution {
//									generateInvoiceRequest.complementos.get.nomina12.get.otrosPagos.get.otroPago.head.subsidioAlEmpleo.get.subsidioCausado.toString() shouldEqual otroPago.subsidioAlEmpleo.get.subsidioCausado
//								}
//							}
//							"CompensacionSaldosAFavor" - {
//								"SaldoAFavor" in new Execution {
//									generateInvoiceRequest.complementos.get.nomina12.get.otrosPagos.get.otroPago.head.compensacionSaldosAFavor.get.saldoAFavor.toString() shouldEqual otroPago.compensacionSaldosAFavor.get.saldoAFavor
//								}
//								"Año" in new Execution {
//									generateInvoiceRequest.complementos.get.nomina12.get.otrosPagos.get.otroPago.head.compensacionSaldosAFavor.get.anio shouldEqual otroPago.compensacionSaldosAFavor.get.anio
//								}
//								"RemanenteSalFav" in new Execution {
//									generateInvoiceRequest.complementos.get.nomina12.get.otrosPagos.get.otroPago.head.compensacionSaldosAFavor.get.remanenteSalFav.toString() shouldEqual otroPago.compensacionSaldosAFavor.get.remanenteSalFav
//								}
//							}
//						}
//					}
//					"Incapacidades" - {
//						"Incapacidad" - {
//							"DiasIncapacidad" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.incapacidades.get.incapacidad.head.diasIncapacidad shouldEqual incapacidadForm.diasIncapacidad
//							}
//							"TipoIncapacidad" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.incapacidades.get.incapacidad.head.tipoIncapacidad shouldEqual incapacidadForm.tipoIncapacidad
//							}
//							"ImporteMonetario" in new Execution {
//								generateInvoiceRequest.complementos.get.nomina12.get.incapacidades.get.incapacidad.head.importeMonetario.get.toString() shouldEqual incapacidadForm.importeMonetario.get
//							}
//						}
//					}
				}
			}
		}
	}
}

