package controllers.invoices

import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.*
import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.figuratransporte.*
import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.mercancias.*
import com.qrsof.empresalit.controllers.invoices.forms.complementos.cartaporte.ubicaciones.*
import com.qrsof.empresalit.controllers.invoices.forms.complementos.nomina.*
import com.qrsof.empresalit.controllers.invoices.forms.complementos.pagos.*
import com.qrsof.empresalit.controllers.invoices.forms.getinvoicestatus.GetInvoiceStatusForm
import com.qrsof.empresalit.controllers.invoices.forms.v40.*
import com.qrsof.empresalit.controllers.invoices.forms.v40.concepto.ParteForm
import com.qrsof.empresalit.controllers.invoices.forms.v40.concepto.v20.*
import com.qrsof.empresalit.controllers.invoices.forms.{
  ACuentaTercerosForm,
  ConceptoForm,
  ConceptoRetencionForm,
  ConceptoTrasladoForm,
  ConceptosImpuestosForm,
  InformacionAduaneraForm,
  InvoiceCancelationForm,
  v40
}
import com.qrsof.empresalit.invoicing.domain.Receptor
import com.qrsof.empresalit.invoicing.domain.complementos.pagos.DoctoRelacionado
import com.qrsof.empresalit.invoicing.domain.conceptos.{CuentaPredial, InformacionAduanera}
import com.qrsof.empresalit.invoicing.generateinvoice.ReceptorInvoice
import play.api.libs.json.{Json, OWrites}

trait GenerateInvoiceWritters {

  implicit val subsidioAlEmpleoFormWritter: OWrites[SubsidioAlEmpleoForm] = Json.writes[SubsidioAlEmpleoForm]
  implicit val subContratacionFormWritter: OWrites[SubContratacionForm] = Json.writes[SubContratacionForm]
  implicit val separacionIndemnizacionFormWritter: OWrites[SeparacionIndemnizacionForm] = Json.writes[SeparacionIndemnizacionForm]
  implicit val horasExtraFormWritter: OWrites[HorasExtraForm] = Json.writes[HorasExtraForm]
  implicit val accionesOTitulosFormWritter: OWrites[AccionesOTitulosForm] = Json.writes[AccionesOTitulosForm]
  implicit val percepcionFormWritter: OWrites[PercepcionForm] = Json.writes[PercepcionForm]
  implicit val compensacionSaldosFavorFormWritter: OWrites[CompensacionSaldosFavorForm] = Json.writes[CompensacionSaldosFavorForm]
  implicit val otroPagoFormWritter: OWrites[OtroPagoForm] = Json.writes[OtroPagoForm]
  implicit val jubilacionPensionRetiroFormWritter: OWrites[JubilacionPensionRetiroForm] = Json.writes[JubilacionPensionRetiroForm]
  implicit val incapacidadFormWritter: OWrites[IncapacidadForm] = Json.writes[IncapacidadForm]
  implicit val entidadSNCFFormWritter: OWrites[EntidadSNCFForm] = Json.writes[EntidadSNCFForm]
  implicit val deduccionFormWritter: OWrites[DeduccionForm] = Json.writes[DeduccionForm]
  implicit val incapacidadesFormWritter: OWrites[IncapacidadesForm] = Json.writes[IncapacidadesForm]
  implicit val otrosPagosFormWritter: OWrites[OtrosPagosForm] = Json.writes[OtrosPagosForm]
  implicit val deduccionesFormWritter: OWrites[DeduccionesForm] = Json.writes[DeduccionesForm]
  implicit val percepcionesFormWritter: OWrites[PercepcionesForm] = Json.writes[PercepcionesForm]
  implicit val receptorNomFormWritter: OWrites[ReceptorNomForm] = Json.writes[ReceptorNomForm]
  implicit val emisorFormWritter: OWrites[EmisorForm] = Json.writes[EmisorForm]
  implicit val nominaCompFormWritter: OWrites[NominaCompForm] = Json.writes[NominaCompForm]
  implicit val domicilioFiguraWritter: OWrites[DomicilioFiguraForm] = Json.writes[DomicilioFiguraForm]
  implicit val partesTransporteWritter: OWrites[PartesTransporteForm] = Json.writes[PartesTransporteForm]
  implicit val tiposFiguraWritter: OWrites[TiposFiguraForm] = Json.writes[TiposFiguraForm]
  implicit val cantidadTransportaWritter: OWrites[CantidadTransportaForm] = Json.writes[CantidadTransportaForm]
  implicit val contenedorCarroWritter: OWrites[ContenedorCarroForm] = Json.writes[ContenedorCarroForm]
  implicit val carroWritter: OWrites[CarroForm] = Json.writes[CarroForm]
  implicit val contenedorWritter: OWrites[ContenedorForm] = Json.writes[ContenedorForm]
  implicit val derechodePasoWritter: OWrites[DerechosDePasoForm] = Json.writes[DerechosDePasoForm]
  implicit val detalleMercanciaWritter: OWrites[DetalleMercanciaForm] = Json.writes[DetalleMercanciaForm]
  implicit val guiasIdentificacionWritter: OWrites[GuiasIdentificacionForm] = Json.writes[GuiasIdentificacionForm]
  implicit val identificacionVehicularWritter: OWrites[IdentificacionVehicularForm] = Json.writes[IdentificacionVehicularForm]
  implicit val pedimentoWritter: OWrites[PedimentoForm] = Json.writes[PedimentoForm]
  implicit val mercanciaWritter: OWrites[MercanciaForm] = Json.writes[MercanciaForm]
  implicit val remolqueWritter: OWrites[RemolqueForm] = Json.writes[RemolqueForm]
  implicit val remolquesWritter: OWrites[RemolquesForm] = Json.writes[RemolquesForm]
  implicit val segurosWritter: OWrites[SegurosForm] = Json.writes[SegurosForm]
  implicit val transporteAereoWriter: OWrites[TransporteAereoForm] = Json.writes[TransporteAereoForm]
  implicit val autotransporteWriter: OWrites[AutotransporteForm] = Json.writes[AutotransporteForm]
  implicit val transporteFerroviarioWriter: OWrites[TransporteFerroviarioForm] = Json.writes[TransporteFerroviarioForm]
  implicit val transporteMaritimoWriter: OWrites[TransporteMaritimoForm] = Json.writes[TransporteMaritimoForm]
  implicit val domicilioWritter: OWrites[DomicilioForm] = Json.writes[DomicilioForm]
  implicit val ubicacionWritter: OWrites[UbicacionForm] = Json.writes[UbicacionForm]
  implicit val mercanciasWritter: OWrites[MercanciasForm] = Json.writes[MercanciasForm]
  implicit val ubicacionesWritter: OWrites[UbicacionesForm] = Json.writes[UbicacionesForm]
  implicit val figuraTransporteWritter: OWrites[FiguratransporteForm] = Json.writes[FiguratransporteForm]
  implicit val cartaPorteWritter: OWrites[CartaPorteForm] = Json.writes[CartaPorteForm]
  implicit val doctoRelacionadoWritter: OWrites[DoctoRelacionado] = Json.writes[DoctoRelacionado]
  implicit val impuestoTrasladoWritter: OWrites[TrasladoForm] = Json.writes[TrasladoForm]
  implicit val impuestoRetencionWritter: OWrites[RetencionForm] = Json.writes[RetencionForm]
//	implicit val impuestosWritter: OWrites[ImpuestosForm] = Json.writes[ImpuestosForm]
//	implicit val doctoRelacionadoFormWritter: OWrites[DoctoRelacionadoForm] = Json.writes[DoctoRelacionadoForm]
//	implicit val pagoWritter: OWrites[PagoForm] = Json.writes[PagoForm]
//	implicit val recepcionPagosWritter: OWrites[RecepcionPagoForm] = Json.writes[RecepcionPagoForm]
//	implicit val complementosWritter: OWrites[ComplementosForm] = Json.writes[ComplementosForm]
  implicit val inlineConceptosImpuestosRetencionWritter: OWrites[ConceptoRetencionForm] = Json.writes[ConceptoRetencionForm]
  implicit val inlineConceptosImpuestosTrasladoWritter: OWrites[ConceptoTrasladoForm] = Json.writes[ConceptoTrasladoForm]
  implicit val inlineConceptosCuentaPredialWritter: OWrites[CuentaPredial] = Json.writes[CuentaPredial]
  implicit val inlineConceptosInformacionAduaneraWritter: OWrites[InformacionAduanera] = Json.writes[InformacionAduanera]
  implicit val inlineConceptosImpuestosWritter: OWrites[ConceptosImpuestosForm] = Json.writes[ConceptosImpuestosForm]
  implicit val inlineConceptoWritter: OWrites[ConceptoForm] = Json.writes[ConceptoForm]
  implicit val inlineReceptorInvoiceWritter: OWrites[ReceptorInvoice] = Json.writes[ReceptorInvoice]
  implicit val inlineReceptorWritter: OWrites[Receptor] = Json.writes[Receptor]
//	implicit val generateInvoiceFormWritter: OWrites[GenerateInvoiceForm] = Json.writes[GenerateInvoiceForm]
  implicit val getInvoiceStatusFormWritter: OWrites[GetInvoiceStatusForm] = Json.writes[GetInvoiceStatusForm]
  implicit val invoiceCancelationFormWritter: OWrites[InvoiceCancelationForm] = Json.writes[InvoiceCancelationForm]

  implicit val trasladoPFormWritter: OWrites[TrasladoPForm] = Json.writes[TrasladoPForm]
  implicit val retencionPFormWritter: OWrites[RetencionPForm] = Json.writes[RetencionPForm]
  implicit val impuestosPFormWritter: OWrites[ImpuestosPForm] = Json.writes[ImpuestosPForm]
  implicit val v20TrasladoDRForm: OWrites[TrasladoDRForm] = Json.writes[TrasladoDRForm]
  implicit val v20TrasladosDRForm: OWrites[TrasladosDRForm] = Json.writes[TrasladosDRForm]
//	implicit val v20RetencionDRForm: OWrites[v20.RetencionDRForm] = Json.writes[v20.RetencionDRForm]
//	implicit val v20DRetencionesDRForm: OWrites[v20.RetencionesDRForm] = Json.writes[v20.RetencionesDRForm]
//	implicit val v20DImpuestosDRForm: OWrites[v20.ImpuestosDRForm] = Json.writes[v20.ImpuestosDRForm]
//	implicit val v20DoctoRelacionadoFormWritter: OWrites[v20.DoctoRelacionadoForm] = Json.writes[v20.DoctoRelacionadoForm]
//	implicit val v20PagoFormWritter: OWrites[v20.PagoForm] = Json.writes[v20.PagoForm]
//	implicit val v20TotalesFormWritter: OWrites[v20.TotalesForm] = Json.writes[v20.TotalesForm]
//	implicit val v20RecepcionPagoFormWritter: OWrites[v20.RecepcionPagoForm] = Json.writes[v20.RecepcionPagoForm]
//	implicit val v40ComplementosComplementosFormWritter: OWrites[v40.complementos.ComplementosForm] = Json.writes[v40.complementos.ComplementosForm]

  implicit val v40RetencionFormWritter: OWrites[v40.RetencionForm] = Json.writes[v40.RetencionForm]
  implicit val v40TrasladoFormWritter: OWrites[v40.TrasladoForm] = Json.writes[v40.TrasladoForm]
//	implicit val v40ImpuestosFormWritter: OWrites[v40.ImpuestosForm] = Json.writes[v40.ImpuestosForm]
  implicit val informacionAduaneraFormWritter: OWrites[InformacionAduaneraForm] = Json.writes[InformacionAduaneraForm]
  implicit val parteFormWritter: OWrites[ParteForm] = Json.writes[ParteForm]
  implicit val aCuentaTercerosFormWritter: OWrites[ACuentaTercerosForm] = Json.writes[ACuentaTercerosForm]
//	implicit val concepto40FormWritter: OWrites[Concepto40Form] = Json.writes[Concepto40Form]

  implicit val receptorComprobante40FormWritter: OWrites[ReceptorComprobante40Form] = Json.writes[ReceptorComprobante40Form]
  implicit val cfdiRelacionadoFormWritter: OWrites[CfdiRelacionadoForm] = Json.writes[CfdiRelacionadoForm]
  implicit val cfdiRelacionadosFormWritter: OWrites[CfdiRelacionadosForm] = Json.writes[CfdiRelacionadosForm]
  implicit val informacionGlobalFormWritter: OWrites[InformacionGlobalForm] = Json.writes[InformacionGlobalForm]
//	implicit val generateInvoice40FormWritter: OWrites[GenerateInvoice40Form] = Json.writes[GenerateInvoice40Form]

}
