name := """empresalit-application"""
organization := "com.qrsof.empresalit"

scalaVersion := "2.13.8"

val AkkaVersion = "2.6.19"
val AkkaHttpVersion = "10.2.9"
val jacksonVersion = "2.13.3"

lazy val application = (project in file("."))
        .configs(IntegrationTest)
        .settings(
          Defaults.itSettings
        )
// Build dependencies
libraryDependencies ++= Seq(
//  guice,
  "com.qrsof.empresalit" % "invoice-xml" % "15-4.0-1-SNAPSHOT",
  "com.qrsof.empresalit" % "stamp-sifei-client" % "1.11.1",
  "com.qrsof.empresalit" % "cancel-sifei-client" % "1.11.1",
  "ch.qos.logback" % "logback-classic" % "1.2.3",
  "org.bouncycastle" % "bcprov-jdk15on" % "1.66",
  "org.bouncycastle" % "bcpkix-jdk15on" % "1.66",
  "org.apache.commons" % "commons-compress" % "1.20",
  "com.qrsof" %% "database-slick-scalatest" % "*******" exclude("ch.qos.logback", "logback-classic"),
  "net.codingwell" %% "scala-guice" % "4.2.11",
  "org.liquibase" % "liquibase-core" % "3.8.2",
  "org.yaml" % "snakeyaml" % "1.25",
//  "com.qrsof" %% "qr-http" % "2.0.54",
//  "com.qrsof" %% "qr-certificates" % "2.0.2",
  "com.nimbusds" % "nimbus-jose-jwt" % "9.4.1",
  "com.qrsof" %% "qr-storage-s3" % "2.0.2" exclude("ch.qos.logback", "logback-classic"),
  "com.qrsof" %% "evolution-scalatest" % "1.0.3" exclude("ch.qos.logback", "logback-classic"),
  "com.ximpleware" % "vtd-xml" % "2.13.4",
  "com.google.zxing" % "core" % "3.4.1",
  "com.google.zxing" % "javase" % "3.4.1",
  "com.fasterxml.jackson.core" % "jackson-databind" % "2.13.3",
  "javax.inject" % "javax.inject" % "1",
  "io.scalaland" %% "chimney" % "0.6.1",
  "pl.iterators" %% "kebs-spray-json" % "1.9.4",
  "com.beachape" %% "enumeratum" % "1.7.0",
  "org.scalactic" %% "scalactic" % "3.2.12",
  "com.typesafe.akka" %% "akka-actor-typed" % AkkaVersion,
  "com.typesafe.akka" %% "akka-stream" % AkkaVersion,
  "com.typesafe.akka" %% "akka-http" % AkkaHttpVersion,
  "com.typesafe.akka" %% "akka-http-spray-json" % AkkaHttpVersion
)

// Test dependencies
libraryDependencies ++= Seq(
  "org.scalatest" %% "scalatest" % "3.2.0" % "it,test",
  "org.scalatestplus" %% "scalatestplus-mockito" % "1.0.0-M2" % "it,test",
  "com.qrsof" %% "database-slick-scalatest" % "*******" % "it,test" classifier "tests",
  "com.vladsch.flexmark" % "flexmark-all" % "0.35.10" % "test"
)

resolvers += "Nexus Releases" at "https://nexus-ci.qrsof.com/repository/maven-public"

credentials += Credentials("Sonatype Nexus Repository Manager", "nexus-ci.qrsof.com", "deployment", "?6<;GsK=")

coverageExcludedPackages := "<empty>;Reverse.*;Module;.*EmpresalitDbMigrator.*;router.*;com.qrsof.empresalit.gateways.appmanager.*;.*SifeiPacGateway.*"

coverageFailOnMinimum := true

coverageMinimumStmtTotal := 90

//coverageMinimumBranchTotal := 90

//coverageMinimumStmtPerPackage := 90

//coverageMinimumBranchPerPackage := 85

//coverageMinimumStmtPerFile := 85

//coverageMinimumBranchPerFile := 80

releaseVersionBump := sbtrelease.Version.Bump.Next

Test / testOptions ++= Seq(Tests.Argument(TestFrameworks.ScalaTest, "-o"), Tests.Argument(TestFrameworks.ScalaTest, "-h", "target/test-reports"))
