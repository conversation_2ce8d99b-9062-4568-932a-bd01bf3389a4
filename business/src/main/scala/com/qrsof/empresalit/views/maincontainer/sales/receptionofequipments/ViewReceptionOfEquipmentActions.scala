package com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments

import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.pojos.{ClientByReceptionOfEquipment, ReceptionOfEquipmentFilter, ReceptionOfEquipmentPageable, ReceptionOfEquipmentRequest}

trait ViewReceptionOfEquipmentActions {
	def getClientsByCompany(companyKey: String): Seq[ClientByReceptionOfEquipment]
	def newReceptionOfEquipments(companyKey: String, request: ReceptionOfEquipmentRequest): String
	def getReceptionOfEquipment(companyKey: String,receptionOfEquipmentFilter: ReceptionOfEquipmentFilter, pageNumber: Int, pageSize: Int): ReceptionOfEquipmentPageable
}
