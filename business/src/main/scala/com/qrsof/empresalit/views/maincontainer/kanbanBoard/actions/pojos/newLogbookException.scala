package com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.newLogbookException.UnknowErrorCode

sealed abstract class newLogbookException(override val appError: AppError) extends ApplicationException(appError)

case class ErrorException(logbook: String) extends newLogbookException(UnknowErrorCode(logbook))

object newLogbookException {
  case class UnknowErrorCode(logbook: String) extends AppError {
    override val code: String = "NL01"
    override val error: Option[String] = Some("Unexpected error")
    override val detail: Option[String] = Some(logbook)
  }
}
