package com.qrsof.empresalit.views.maincontainer.employee.actions.pojos

import com.qrsof.empresalit.controllers.oauth.RegisterActionRequest
import org.apache.pekko.http.scaladsl.common.StrictForm

import java.util.Date

case class EmployeeDataUpdateRequest(
																							employeeKey: String,
																							employeeNumber: String,
																							name: String,
																							lastname: String,
																							motherLastname: String,
																							email: String,
																							birthDate: Date,
																							entryDate: Date,
																							areaKey: Option[String],
																							jobKey: String,
																							accountNumber: Option[String],
																							amount: BigDecimal,
																							bonus: BigDecimal,
																							files: Seq[StrictForm.FileData],
																							filesToDelete: Seq[String] = Seq.empty,
																							credentials: Option[RegisterActionRequest])
