package com.qrsof.empresalit.views.maincontainer.employee.actions

import com.qrsof.empresalit.domain.employee_resources.pojos.EmployeeResource

import java.util.Date

case class EmployeeDataUpdate(
																		 employeeKey: String,
																		 employeeNumber: String,
																		 fullname: String,
																		 name: String,
																		 lastname: String,
																		 motherLastname: String,
																		 email: String,
																		 entryDate: Date,
																		 birthDate: Date,
																		 areaKey: Option[String],
																		 jobKey: String,
																		 accountNumber: Option[String],
																		 amount: BigDecimal,
																		 bonus: BigDecimal,
																		 files: Seq[EmployeeResource] = Seq.empty)
