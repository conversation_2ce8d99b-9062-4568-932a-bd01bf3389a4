package com.qrsof.empresalit.views.maincontainer.kanbanBoard

import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.*

trait ViewKanbanBoardGateway {
  def getAllTasksAndStatus(company_key: String): KanbanTasksResponse

  def getKanbanTasksFiltered(filters: FiltersKanbanActionRequest): KanbanTasksResponse

  def getLogbookAndAttachmentsData(companyKey: String, taskKey: String): LogbookAndAttachmentData

  def assignTaskGateway(companyKey: String, updatedTask: Task): Either[TaskException, Unit]

  def getAllTasksAndStatusWithFilters(filters: FiltersKanbanActionRequest): KanbanTasksResponse
}
