package com.qrsof.empresalit.views.maincontainer.quote.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.quote.actions.QuoteErrorCodeUpdate.QuoteNotFoundErrorCodeUpdate

sealed abstract class QuoteUpdateException(override val appError: AppError) extends ApplicationException(appError)

case class QuoteNotFoundExceptionUpdate(quote_key: String) extends QuoteUpdateException(QuoteNotFoundErrorCodeUpdate(quote_key))

object QuoteErrorCodeUpdate {
  case class QuoteNotFoundErrorCodeUpdate(quote_key: String) extends AppError {
    override val code: String = "UQO01"
    override val error: Option[String] = Some("Quote id doesn't exist")
    override val detail: Option[String] = Some(quote_key)
  }
}
