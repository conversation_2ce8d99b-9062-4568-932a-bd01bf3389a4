package com.qrsof.empresalit.views.maincontainer.presenteeism.pojos

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.presenteeism.pojos.PresenteeismErrorCodes.{EmployeeNotFoundErrorCode, PresenteeismErrorExceptionCode, PresenteeismNotFoundErrorCode}

sealed abstract class PresenteeismException(override val appError: AppError) extends ApplicationException(appError)

case class EmployeeNotFoundException(key: String) extends PresenteeismException(EmployeeNotFoundErrorCode(key))

case class PresenteeismNotFound(key: String) extends PresenteeismException(PresenteeismNotFoundErrorCode(key))

case class PresenteeismErrorException(key: String) extends PresenteeismException(PresenteeismErrorExceptionCode(key))

object PresenteeismErrorCodes {
  case class EmployeeNotFoundErrorCode(key: String) extends AppError {
    override val code: String = "PE01"
    override val error: Option[String] = Some("Employee Not Found")
    override val detail: Option[String] = Some(key)
  }

  case class PresenteeismNotFoundErrorCode(key: String) extends AppError {
    override val code: String = "PE02"
    override val error: Option[String] = Some("Presenteeism Not Found")
    override val detail: Option[String] = Some(key)
  }

  case class PresenteeismErrorExceptionCode(key: String) extends AppError {
    override val code: String = "PE03"
    override val error: Option[String] = Some("Presenteeism Error")
    override val detail: Option[String] = Some(key)
  }
}
