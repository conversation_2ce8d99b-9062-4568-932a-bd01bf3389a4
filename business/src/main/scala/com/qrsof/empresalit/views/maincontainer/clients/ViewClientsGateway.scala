package com.qrsof.empresalit.views.maincontainer.clients

import com.qrsof.empresalit.views.maincontainer.clients.actions.*

trait ViewClientsGateway {
  def newClient(client: Client): ClientResponse

  def findClientByRfc(rfc: String): Option[ClientEnti]

  def findClientByKey(clientKey: String): Option[ClientEnti]

  def deleteClient(clientKey: String): ClientResponse

  def getClients(filters: FiltersClientsActionRequest): ClientsResponse

  def getAllClients(company_key: Option[String]): ClientsResponse
}
