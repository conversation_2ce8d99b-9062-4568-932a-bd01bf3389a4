package com.qrsof.empresalit.views.maincontainer.debtsToPay.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.DebtsErrorCodes.DebtToPayNotFoundErrorCode

sealed abstract class DebtToPayException(override val appError: AppError) extends ApplicationException(appError)

case class DebtToPayNotFoundExeption(debt_to_pay_key: String) extends DebtToPayException(DebtToPayNotFoundErrorCode(debt_to_pay_key))

object DebtsErrorCodes {
  case class DebtToPayNotFoundErrorCode(debt_to_pay_key: String) extends AppError {
    override val code: String = "DTPE01"
    override val error: Option[String] = Some("Debt doesnt exist")
    override val detail: Option[String] = Some(debt_to_pay_key)
  }
}
