package com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions

import com.qrsof.empresalit.views.maincontainer.debtsReceivables.ViewDebtsReceivableGateway
import io.scalaland.chimney.dsl._

import jakarta.inject.{Inject, Singleton}
import scala.concurrent.ExecutionContext

@Singleton
class ViewDebtsReceivableActionsImpl @Inject()
(viewDebtsReceivableGateway: ViewDebtsReceivableGateway)
(implicit ec: ExecutionContext)
  extends ViewDebtsReceivableActions {
  override def newDebtReceivable(debtReceivableActionRequest: DebtReceivableActionRequest): DebtReceivableResponse = {
    viewDebtsReceivableGateway.newDebtReceivable(
      debtReceivableActionRequest
        .into[DebtReceivable]
        .transform
    )
  }

  override def getDebtsReceivable(filtersActionRequest: FiltersActionRequest): DebtsReceivableWithTotalResponse = {
    val filters = filtersActionRequest.into[Filters].transform
    viewDebtsReceivableGateway.getDebtsReceivable(filters).into[DebtsReceivableWithTotalResponse].transform
  }

  override def deleteDebtReceivable(debt_key: String): Either[DebtException, DebtReceivableResponse] = {
    val debtExist = viewDebtsReceivableGateway.findDebt(debt_key)
    if (debtExist.isEmpty) {
      Left(DebtNotFoundException(debt_key))
    } else {
      val debtKey = viewDebtsReceivableGateway.deleteDebt(debt_key)
      Right(debtKey)
    }
  }

  override def markAsPaid(debt_key: String): Either[DebtException, DebtReceivableResponse] = {
    val debtExist = viewDebtsReceivableGateway.findDebt(debt_key)
    if (debtExist.isEmpty) {
      Left(DebtNotFoundException(debt_key))
    } else {
      val response = viewDebtsReceivableGateway.markAsPaid(debtExist.get)
      Right(response.into[DebtReceivableResponse].transform)
    }
  }
}
