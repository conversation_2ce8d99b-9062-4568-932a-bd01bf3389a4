package com.qrsof.empresalit.views.maincontainer.presenteeism

import com.qrsof.empresalit.domain.employees.EmployeesGateway
import com.qrsof.empresalit.domain.holidays.actions.HoliDaysActions
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import com.qrsof.empresalit.views.maincontainer.companies.ViewCompaniesGateway
import com.qrsof.empresalit.views.maincontainer.presenteeism.pojos.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.util.Date

@Singleton
class ViewPresenteeismActionsImpl @Inject() (
    empresalitUtils: EmpresalitUtils,
    viewPresenteeismGateway: ViewPresenteeismGateway,
    holiDaysActions: HoliDaysActions,
    employeesGateway: EmployeesGateway,
    companiesGateway: ViewCompaniesGateway
) extends ViewPresenteeismActions {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewPresenteeismActionsImpl])
  override def savePresenteeism(newPresenteeismRequest: NewPresenteeismRequest, userKey: String): String = {
    validDate(empresalitUtils.getDate, newPresenteeismRequest.startDate)
    validDate(newPresenteeismRequest.startDate, newPresenteeismRequest.endDate)
    var request = PresenteeismRequest(
      empresalitUtils.generateKey(),
      newPresenteeismRequest.presenteeismType,
      newPresenteeismRequest.days,
      newPresenteeismRequest.startDate,
      newPresenteeismRequest.endDate,
      empresalitUtils.getDate,
      empresalitUtils.getDate,
      newPresenteeismRequest.employeeKey,
      userKey
    )

    viewPresenteeismGateway.savePresenteeism(request)
    request.presenteeismKey
  }

  override def savePresenteeisms(newPresenteeismsRequest: NewPresenteeismsRequest): Either[PresenteeismException, Unit] = {

    try {
      val date = empresalitUtils.getDate
      val requests = newPresenteeismsRequest.presenteeisms.map(presenteeism => {
        //   validDate(empresalitUtils.getDate, presenteeism.startDate)
        validDate(presenteeism.startDate, presenteeism.endDate)
        var request = PresenteeismRequest(
          empresalitUtils.generateKey(),
          presenteeism.presenteeismType,
          presenteeism.days,
          presenteeism.startDate,
          presenteeism.endDate,
          date,
          date,
          presenteeism.employeeKey,
          newPresenteeismsRequest.userKey
        )
        request
      })
      viewPresenteeismGateway.savePresenteeisms(requests)
      Right(())
    } catch case e: Throwable => Left(PresenteeismErrorException(e.getMessage))
  }

  override def updatePresenteeism(presenteeismKey: String, newPresenteeismRequest: NewPresenteeismRequest): String = {
    viewPresenteeismGateway.updatePresenteeism(
      PresenteeismRequest(
        presenteeismKey,
        newPresenteeismRequest.presenteeismType,
        newPresenteeismRequest.days,
        newPresenteeismRequest.startDate,
        newPresenteeismRequest.endDate,
        null,
        empresalitUtils.getDate,
        newPresenteeismRequest.employeeKey,
        null
      )
    )
    presenteeismKey
  }

  override def getEmployeePresenteeismByCompanyKey(companyKey: String, presenteeismFilter: PresenteeismFilter, pageNumber: Int, pageSize: Int): PresenteeismPageableDTO = {
    viewPresenteeismGateway.getEmployeePresenteeismByCompanyKey(companyKey, presenteeismFilter, pageNumber, pageSize)
  }

  override def getEmployeePresenteeismByEmployeeKey(employeeKey: String, presenteeismFilter: PresenteeismFilter): EmployeePresenteeismAndHolidaysResponse = {
    val employeePresenteeism = viewPresenteeismGateway.getEmployeePresenteeismByEmployeeKey(employeeKey, presenteeismFilter)
    val holiDays = holiDaysActions.getHoliDays
    EmployeePresenteeismAndHolidaysResponse(employeePresenteeism, holiDays)
  }

  override def getPresenteeismByKey(presenteeismKey: String): PresenteeismDetailDTO = {
    viewPresenteeismGateway.getPresenteeismByKey(presenteeismKey).getOrElse(throw new ClassNotFoundException("Licencia no encontrada"))
  }

  def validDate(date: Date, nowDate: Date): Boolean = {
    if (nowDate.before(date)) {
      throw new Exception("Fecha incorrecta")
    }
    true
  }

  override def getEmployeesWithPresenteeismByCompanyKey(companyKey: String, presenteeismFilter: PresenteeismFilter, pageNumber: Int, pageSize: Int): EmployeeWithPresenteeismPageableDTO = {
    viewPresenteeismGateway.getEmployeesWithPresenteeismByCompanyKey(companyKey, presenteeismFilter, pageNumber, pageSize)
  }

  override def getEmployeesAndPresenteeismByCompanyKey(companyKey: String, filters: EmployeeAndPresenteeismFilters): Either[Exception, EmployeesAndPresenteeismDTO] = {
    val company = companiesGateway.getCompanies(companyKey)
    if (company.total == 0) {
      Left(new Exception("Empresa no encontrada"))
    } else {
      val response = viewPresenteeismGateway.getEmployeesAndPresenteeismByCompanyKey(companyKey, filters)
      Right(EmployeesAndPresenteeismDTO(response.events))
    }
  }

  override def deletePresenteeismAction(presenteeismKey: String): Either[PresenteeismException, Unit] = {
    try {
      viewPresenteeismGateway.deletePresenteeismGateway(presenteeismKey)
      Right(())
    } catch case e: Throwable => Left(PresenteeismErrorException(e.getMessage))
  }

  override def validateRangeDatesExcludingHolidays(validateRangeDatesRequest: ValidateRangeDatesRequest): ValidateRangeDatesResponse = {

    val start = validateRangeDatesRequest.startDate.toInstant.atZone(ZoneId.systemDefault()).toLocalDate
    val end = validateRangeDatesRequest.endDate.toInstant.atZone(ZoneId.systemDefault()).toLocalDate
    val datesInRange = (0L to ChronoUnit.DAYS.between(start, end)).map(start.plusDays(_))
    val holidaysFiltered = holiDaysActions.getHoliDaysFiltered(validateRangeDatesRequest.startDate, validateRangeDatesRequest.endDate)

    if (datesInRange.size != validateRangeDatesRequest.totalDays) {
      logger.error("El rango de fechas no coincide con el total de días")
      throw new Exception("El rango de fechas no coincide con el total de días")
    }

    val weekends = datesInRange.count(d => d.getDayOfWeek.getValue == 6 || d.getDayOfWeek.getValue == 7)
    val totalDaysLessHolidaysAndWeekends = datesInRange.size - holidaysFiltered.size - weekends

    ValidateRangeDatesResponse(
      startDate = validateRangeDatesRequest.startDate,
      endDate = validateRangeDatesRequest.endDate,
      totalDays = validateRangeDatesRequest.totalDays,
      totalDaysValidated = totalDaysLessHolidaysAndWeekends
    )
  }
}
