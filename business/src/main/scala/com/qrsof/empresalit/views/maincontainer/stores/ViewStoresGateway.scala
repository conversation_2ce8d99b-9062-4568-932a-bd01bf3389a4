package com.qrsof.empresalit.views.maincontainer.stores

import com.qrsof.empresalit.views.maincontainer.stores.actions.*

trait ViewStoresGateway {
  def addStore(newStoreData: NewStoreData): NewStoreResponse

  def getStore(filtersStore: FiltersStore): Stores

  def findStore(store_key: String): Option[StoreEnt]

  def updateStore(generalDto: GeneralDtoInStoreActionRequest): Either[StoreUpdateException, SuccessUpdateStore]

  def deleteStore(store_key: String): StoreKey
}
