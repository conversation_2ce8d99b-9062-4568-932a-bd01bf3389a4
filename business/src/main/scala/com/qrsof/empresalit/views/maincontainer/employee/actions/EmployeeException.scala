package com.qrsof.empresalit.views.maincontainer.employee.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.employee.actions.EmployeeErrorCode.EmployeeNotFoundErrorCode

sealed abstract class EmployeeException(override val appError: AppError) extends ApplicationException(appError)

case class EmployeeNotFoundException(employee_key: String) extends EmployeeException(EmployeeNotFoundErrorCode(employee_key))

object EmployeeErrorCode {
  case class EmployeeNotFoundErrorCode(employee_key: String) extends AppError {
    override val code: String = "EMP01"
    override val error: Option[String] = Some("Employee doesnt exist")
    override val detail: Option[String] = Some(employee_key)
  }
}
