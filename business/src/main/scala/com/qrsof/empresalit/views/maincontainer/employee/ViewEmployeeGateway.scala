package com.qrsof.empresalit.views.maincontainer.employee

import com.qrsof.empresalit.views.maincontainer.employee.actions.*
import com.qrsof.empresalit.domain.employee_resources.pojos.EmployeeResource
import com.qrsof.empresalit.views.maincontainer.employee.actions.pojos.EmployeeData

trait ViewEmployeeGateway {
	def addEmployee(newEmployeeData: NewEmployeeData, employeeResources: Seq[EmployeeResource]): NewEmployeeResponse

	def getEmployee(filtersEmployee: FiltersEmployee): Employees

	def findEmployee(employee_key: String): Option[EmployeeEnt]

	def getEmployeeByKey(employeeKey: String): Option[EmployeeData]

	def deleteEmployee(employee_key: String): Employee<PERSON><PERSON>

	def updateEmployee(generalData: EmployeeDataUpdate): Either[EmployeeException, SuccessUpdateResponse]
}
