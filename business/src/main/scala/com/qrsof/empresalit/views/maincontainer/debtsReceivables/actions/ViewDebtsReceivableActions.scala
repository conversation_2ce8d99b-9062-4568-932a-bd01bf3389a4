package com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions

trait ViewDebtsReceivableActions {
  def newDebtReceivable(debtReceivableActionRequest: DebtReceivableActionRequest): DebtReceivableResponse

  def getDebtsReceivable(filtersActionRequest: FiltersActionRequest): DebtsReceivableWithTotalResponse

  def deleteDebtReceivable(debt_key: String): Either[DebtException, DebtReceivableResponse]

  def markAsPaid(debt_key: String): Either[DebtException, DebtReceivableResponse]
}
