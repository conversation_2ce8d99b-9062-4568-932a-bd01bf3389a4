package com.qrsof.empresalit.views.onboarding.actions

import com.qrsof.empresalit.domain.company.actions.CompanyErrorCodes.*
import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException

sealed abstract class newCompanyException(override val appError: AppError) extends ApplicationException(appError)

case class UnknowErrorView(userResource: String) extends newCompanyException(UnknowErrorCode(userResource))

object newCompanyErrorCodes {
  case class UnknowErrorCode(userResource: String) extends AppError {
    override val code: String = "EC01"
    override val error: Option[String] = Some("Unexpected error")
    override val detail: Option[String] = Some(userResource)
  }
}
