package com.qrsof.empresalit.views.maincontainer.stores.actions

import com.qrsof.empresalit.views.maincontainer.stores.actions.StoreErrorCode.StoreNotFoundErrorCode
import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException


sealed abstract class StoreDeleteException(override val appError: AppError) extends ApplicationException(appError)

case class StoreNotFoundException(store_key: String) extends StoreDeleteException(StoreNotFoundErrorCode(store_key))

object StoreErrorCode {
  case class StoreNotFoundErrorCode(store_key: String) extends AppError {
    override val code: String = "EMP01"
    override val error: Option[String] = Some("Store does not exist")
    override val detail: Option[String] = Some(store_key)
  }
}
