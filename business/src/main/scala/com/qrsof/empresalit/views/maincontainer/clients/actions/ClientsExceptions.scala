package com.qrsof.empresalit.views.maincontainer.clients.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.clients.actions.ClientsErrorCodes.{ClientAlredyExistErrorCode, ClientNotFoundErrorCode}

sealed abstract class ClientsExceptions(override val appError: AppError) extends ApplicationException(appError)

case class ClientNotFoundException(key: String) extends ClientsExceptions(ClientNotFoundErrorCode(key))

case class ClientAlreadyExist(key: String) extends ClientsExceptions(ClientAlredyExistErrorCode(key))

object ClientsErrorCodes {

  case class ClientNotFoundErrorCode(key: String) extends AppError {
    override val code: String = "CE01"
    override val error: Option[String] = Some("Client Not Found")
    override val detail: Option[String] = Some(key)
  }

  case class ClientAlredyExistErrorCode(key: String) extends AppError {
    override val code: String = "CE02"
    override val error: Option[String] = Some("This client alredy exist")
    override val detail: Option[String] = Some(key)
  }
}
