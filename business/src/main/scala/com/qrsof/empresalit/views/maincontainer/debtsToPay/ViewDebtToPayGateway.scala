package com.qrsof.empresalit.views.maincontainer.debtsToPay

import com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.*

trait ViewDebtToPayGateway {
  def getDebtsToPay(listDebtToPayActionRequest: ListDebtToPayActionRequest): DebtsToPay

  def addDebtToPay(newDebtToPayData: NewDebtToPayData): NewDebtToPayResponse

  def findDebtToPay(debtToPayKey: String): Option[DebtsToPayEnt]

  def deleteDebtToPay(debtToPayKey: String): DebtToPayKey

  def markAsPaid(debtToPay: DebtsToPayEnt): DebtToPayKey
}
