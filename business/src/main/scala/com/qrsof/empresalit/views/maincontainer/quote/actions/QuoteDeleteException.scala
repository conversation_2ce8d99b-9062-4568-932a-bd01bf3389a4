package com.qrsof.empresalit.views.maincontainer.quote.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.quote.actions.QuoteErrorCodeDelete.QuoteNotFoundErrorCodeDelete

sealed abstract class QuoteDeleteException(override val appError: AppError) extends ApplicationException(appError)

case class QuoteNotFoundExceptionDelete(quote_key: String) extends QuoteDeleteException(QuoteNotFoundErrorCodeDelete(quote_key))

object QuoteErrorCodeDelete {
  case class QuoteNotFoundErrorCodeDelete(quote_key: String) extends AppError {
    override val code: String = "DQO01"
    override val error: Option[String] = Some("Quote id isn't equals quote_key")
    override val detail: Option[String] = Some(quote_key)
  }
}
