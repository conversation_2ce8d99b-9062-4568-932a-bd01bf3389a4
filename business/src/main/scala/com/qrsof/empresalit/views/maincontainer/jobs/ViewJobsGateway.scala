package com.qrsof.empresalit.views.maincontainer.jobs

import com.qrsof.empresalit.views.maincontainer.jobs.actions.*

trait ViewJobsGateway {
  def getJobs(listJobsActionRequest: ListJobsActionRequest): JobsWithTotal

  def addJobs(jobData: JobsData): JobResponse

  def updateJobs(jobsEnt: JobsEnt): Job<PERSON>ey

  def findJobs(jobsKey: String): Option[JobsEnt]

  def deleteJobs(job_key: String): JobKey
}
