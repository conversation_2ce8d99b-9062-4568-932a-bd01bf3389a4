package com.qrsof.empresalit.views.maincontainer.jobs.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.jobs.actions.newJobsException.UnknowErrorCode

sealed abstract class newJobsException(override val appError: AppError) extends ApplicationException(appError)

case class UnknowErrorView(userResource: String) extends newJobsException(UnknowErrorCode(userResource))

object newJobsException {
  case class UnknowErrorCode(userResource: String) extends AppError {
    override val code: String = "JB01"
    override val error: Option[String] = Some("Unexpected error")
    override val detail: Option[String] = Some(userResource)
  }
}
