package com.qrsof.empresalit.views.maincontainer.stores.actions

import com.qrsof.empresalit.views.maincontainer.stores.actions.StoreException.UnknownErrorCode
import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException

sealed abstract class StoreException(override val appError: AppError) extends ApplicationException(appError)

case class UnknownErrorView(userResource: String) extends StoreException(UnknownErrorCode(userResource))

object StoreException {
  case class UnknownErrorCode(userResource: String) extends AppError {
    override val code: String = "EC01"
    override val error: Option[String] = Some("Unexpected Error")
    override val detail: Option[String] = Some(userResource)
  }
}
