package com.qrsof.empresalit.views.maincontainer.employee.actions.pojos

import com.qrsof.empresalit.domain.employee_resources.pojos.EmployeeResourceData

import java.util.Date

case class EmployeeData(
												employeeKey: String,
												employeeNumber: String,
												fullname: String,
												name: String,
												lastname: String,
												motherLastname: String,
												email: Option[String],
												entryDate: Date,
												birthDate: Date,
												area: Option[String],
												jobKey: String,
												nameJob: String,
												accountNumber: Option[String],
												amount: BigDecimal,
												bonus: BigDecimal,
												accessUserKey: Option[String],
												resources: Seq[EmployeeResourceData] = Seq.empty)
