package com.qrsof.empresalit.views.maincontainer.stores.actions

trait ViewStoreActions {
  def addStore(newStoreAction: NewStoreAction): Either[StoreException, NewStoreResponse]

  def getStore(filtersStoreActionRequest: FiltersStoreActionRequest): Stores

  def updateStore(generalDto: GeneralDtoInStoreActionRequest): Either[StoreUpdateException, SuccessUpdateStore]

  def deleteStore(store_key: String): Either[StoreDeleteException, NewStoreResponse]
}
