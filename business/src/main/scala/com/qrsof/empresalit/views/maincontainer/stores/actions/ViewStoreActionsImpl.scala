package com.qrsof.empresalit.views.maincontainer.stores.actions

import com.qrsof.empresalit.views.maincontainer.actions.{UserCompaniesView, ViewMainContainerActions}
import com.qrsof.empresalit.views.maincontainer.stores.ViewStoresGateway
import io.scalaland.chimney.dsl._

import jakarta.inject.{Inject, Singleton}
import scala.concurrent.ExecutionContext

@Singleton
class ViewStoreActionsImpl @Inject()
(viewStoresGateway: ViewStoresGateway,
 viewMainContainerActions: ViewMainContainerActions)
(implicit ec: ExecutionContext) extends ViewStoreActions {

  override def addStore(newStoreAction: NewStoreAction): Either[StoreException, NewStoreResponse] = {
    try {
      val companies: Seq[UserCompaniesView] = viewMainContainerActions.getUserCompanies(newStoreAction.user_key)
      val store: NewStoreData =
        newStoreAction
          .into[NewStoreData]
          .withFieldConst(_.company_key, companies.head.key)
          .transform

      val newStoreResponse = viewStoresGateway.addStore(store): NewStoreResponse
      Right(newStoreResponse)
    } catch {
      case e: Throwable =>
        Left(UnknownErrorView(e.getMessage))
    }
  }

  override def getStore(filtersStoreActionRequest: FiltersStoreActionRequest): Stores = {
    val filters = filtersStoreActionRequest.into[FiltersStore].transform
    viewStoresGateway.getStore(filters).into[Stores].transform
  }

  override def updateStore(generalDto: GeneralDtoInStoreActionRequest): Either[StoreUpdateException, SuccessUpdateStore] = {
    viewStoresGateway.updateStore(generalDto)
  }

  override def deleteStore(store_key: String): Either[StoreDeleteException, NewStoreResponse] = {
    val store = viewStoresGateway.findStore(store_key);
    if (store.isEmpty) {
      Left(StoreNotFoundException(store_key))
    } else {
      val a = viewStoresGateway.deleteStore(store_key);
      Right(a.into[NewStoreResponse].transform)
    }
  }

}
