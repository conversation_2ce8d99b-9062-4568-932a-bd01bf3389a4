package com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.TaskErrorCode.TaskErrorCodeNotFound

sealed abstract class TaskException(override val appError: AppError) extends ApplicationException(appError)

case class TaskExceptionError(taskKey: String) extends TaskException(TaskErrorCodeNotFound(taskKey))

object TaskErrorCode {
  case class TaskErrorCodeNotFound(taskKey: String) extends AppError {
    override val code: String = " Tk01"
    override val error: Option[String] = Some("Task doesn't exist")
    override val detail: Option[String] = Some(taskKey)
  }
}
