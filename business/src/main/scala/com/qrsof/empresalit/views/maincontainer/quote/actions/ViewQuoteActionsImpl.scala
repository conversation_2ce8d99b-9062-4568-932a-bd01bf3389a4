package com.qrsof.empresalit.views.maincontainer.quote.actions

import com.qrsof.empresalit.views.maincontainer.actions.ViewMainContainerActions
import com.qrsof.empresalit.views.maincontainer.clients.actions.ViewClientsActions
import com.qrsof.empresalit.views.maincontainer.quote.ViewQuoteGateway
import com.qrsof.empresalit.views.maincontainer.stores.actions.ViewStoreActions
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}

import scala.concurrent.ExecutionContext

@Singleton
class ViewQuoteActionsImpl @Inject() (
    viewQuoteGateway: ViewQuoteGateway,
    viewMainContainerActions: ViewMainContainerActions,
    viewStoreActions: ViewStoreActions,
    viewClientsActions: ViewClientsActions
)(implicit ec: ExecutionContext)
    extends ViewQuoteActions {
  override def addQuote(newQuoteAction: NewQuoteAction): Either[QuoteException, NewQuoteResponse] = {
    try {
      viewMainContainerActions.getUserCompanies(newQuoteAction.user_key)
      val quote: NewQuoteData =
        newQuoteAction
          .into[NewQuoteData]
          .withFieldConst(_.store_key, newQuoteAction.store_key)
          .withFieldConst(_.client_key, newQuoteAction.client_key)
          .transform

      val newQuoteResponse = viewQuoteGateway.addQuote(quote): NewQuoteResponse
      Right(newQuoteResponse)
    } catch {
      case e: Throwable =>
        Left(UnknownErrorView(e.getMessage))
    }
  }

  override def getQuote(filterDefaultQuote: FilterDefaultQuote): Quotations = {
    val filter = filterDefaultQuote
      .into[FiltersQuote]
      .transform
    viewQuoteGateway.getQuote(filter).into[Quotations].transform
  }

  override def getAllQuote(company_key: Option[String]): Quotations = {
    viewQuoteGateway.getAllQuoteByUser(company_key).into[Quotations].transform
  }

  override def updateQuote(quoteDto: GeneralDtoInQuoteActionRequest): Either[QuoteUpdateException, SuccessUpdateQuote] = {
    viewQuoteGateway.updateQuote(quoteDto)
  }

  override def deleteQuote(quote_key: String): Either[QuoteDeleteException, DeleteQuoteResponse] = {
    val quote = viewQuoteGateway.findQuote(quote_key)
    if (quote.isEmpty) {
      Left(QuoteNotFoundExceptionDelete(quote_key))
    } else {
      val success = viewQuoteGateway.deleteQuote(quote_key)
      Right(
        success
          .into[DeleteQuoteResponse]
          .transform
      )
    }
  }

  override def markAsFacture(quoteKey: String): Either[QuoteNotFoundExceptionUpdate, NewQuoteResponse] = {
    val quote = viewQuoteGateway.findQuote(quoteKey)
    if (quote.isEmpty) {
      Left(QuoteNotFoundExceptionUpdate(quoteKey))
    } else {
      val quotations = viewQuoteGateway.markAsFacture(quote.get)
      Right(
        quotations
          .into[NewQuoteResponse]
          .transform
      )
    }
  }

  override def markAsReject(quoteKey: String): Either[QuoteNotFoundExceptionUpdate, NewQuoteResponse] = {
    val quote = viewQuoteGateway.findQuote(quoteKey)
    if (quote.isEmpty) {
      Left(QuoteNotFoundExceptionUpdate(quoteKey))
    } else {
      val quotations = viewQuoteGateway.markAsReject(quote.get)
      Right(
        quotations
          .into[NewQuoteResponse]
          .transform
      )
    }
  }

  override def markAsPending(quoteKey: String): Either[QuoteNotFoundExceptionUpdate, NewQuoteResponse] = {
    val quote = viewQuoteGateway.findQuote(quoteKey)
    if (quote.isEmpty) {
      Left(QuoteNotFoundExceptionUpdate(quoteKey))
    } else {
      val quotations = viewQuoteGateway.markAsPending(quote.get)
      Right(
        quotations
          .into[NewQuoteResponse]
          .transform
      )
    }
  }
}
