package com.qrsof.empresalit.views.maincontainer.jobs.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.jobs.actions.JobsErrorCodes.JobsNotFountErrorCode

sealed abstract class JobsException(override val appError: AppError) extends ApplicationException(appError)

case class JobsNotFountException(job_key: String) extends JobsException(JobsNotFountErrorCode(job_key))

object JobsErrorCodes {
  case class JobsNotFountErrorCode(job_key: String) extends AppError {
    override val code: String = "JOB01"
    override val error: Option[String] = Some("Job doesnt exist")
    override val detail: Option[String] = Some(job_key)
  }
}
