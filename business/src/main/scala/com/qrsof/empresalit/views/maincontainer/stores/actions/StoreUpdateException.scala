package com.qrsof.empresalit.views.maincontainer.stores.actions

import com.qrsof.empresalit.views.maincontainer.stores.actions.StoreErrorCodeUpdate.StoreNotFoundErrorCodeUpdate
import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException

sealed abstract class StoreUpdateException(override val appError: AppError) extends ApplicationException(appError)

case class StoreNotFoundExceptionUpdate(store_key: String) extends StoreUpdateException(StoreNotFoundErrorCodeUpdate(store_key))

object StoreErrorCodeUpdate {
  case class StoreNotFoundErrorCodeUpdate(store_key: String) extends AppError {
    override val code: String = "STU01"
    override val error: Option[String] = Some("Store id does not exist")
    override val detail: Option[String] = Some(store_key)
  }
}
