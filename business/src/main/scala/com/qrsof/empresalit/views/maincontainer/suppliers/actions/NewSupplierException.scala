package com.qrsof.empresalit.views.maincontainer.suppliers.actions

import com.qrsof.empresalit.views.maincontainer.suppliers.actions.NewSupplierException.UnknowErrorCode
import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException

abstract class NewSupplierException(override val appError: AppError) extends ApplicationException(appError)

case class UnknowErrorViewSupp(userResource: String) extends NewSupplierException(UnknowErrorCode(userResource))

object NewSupplierException {
  case class UnknowErrorCode(userResource: String) extends AppError {
    override val code: String = "EC01"
    override val error: Option[String] = Some("Unexpected error")
    override val detail: Option[String] = Some(userResource)
  }
}
