package com.qrsof.empresalit.views.maincontainer.companies.action

import com.qrsof.empresalit.views.maincontainer.companies.ViewCompaniesGateway
import io.scalaland.chimney.dsl._

import jakarta.inject.{Inject, Singleton}
import scala.concurrent.ExecutionContext

@Singleton
class ViewCompaniesActionImpl @Inject()
(viewCompaniesGateway: ViewCompaniesGateway)
(implicit ec: ExecutionContext)
  extends ViewCompaniesAction {
  override def getCompanies(company_key: String): CompanyWithTotal = {
    viewCompaniesGateway.getCompanies(company_key).into[CompanyWithTotal].transform
  }
}
