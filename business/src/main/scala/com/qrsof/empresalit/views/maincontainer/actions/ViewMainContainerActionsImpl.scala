package com.qrsof.empresalit.views.maincontainer.actions

import com.qrsof.empresalit.views.maincontainer.ViewMainContainerGateway

import jakarta.inject.{Inject, Singleton}
import scala.concurrent.ExecutionContext

@Singleton
class ViewMainContainerActionsImpl @Inject()
(viewMainContainerGateway: ViewMainContainerGateway)
(implicit ec: ExecutionContext) extends ViewMainContainerActions {

  override def getUserCompanies(userKey: String): Seq[UserCompaniesView] = {
    viewMainContainerGateway
      .getUserCompanies(userKey)
  }

}
