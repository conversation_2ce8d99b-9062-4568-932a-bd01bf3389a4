package com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.DebtErrorCodes.DebtNotFoundErrorCode

sealed abstract class DebtException(override val appError: AppError) extends ApplicationException(appError)

case class DebtNotFoundException(key: String) extends DebtException(DebtNotFoundErrorCode(key))

object DebtErrorCodes {
  case class DebtNotFoundErrorCode(key: String) extends AppError {
    override val code: String = "DTPE01"
    override val error: Option[String] = Some("Debt doesnt exist")
    override val detail: Option[String] = Some(key)
  }
}
