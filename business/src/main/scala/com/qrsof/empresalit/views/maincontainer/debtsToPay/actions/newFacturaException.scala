package com.qrsof.empresalit.views.maincontainer.debtsToPay.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.newFacturaException.UnknowErrorCode

sealed abstract class newFacturaException(override val appError: AppError) extends ApplicationException(appError)

case class UnknowErrorViewA(userResource: String) extends newFacturaException(UnknowErrorCode(userResource))

object newFacturaException {

  case class UnknowErrorCode(userResource: String) extends AppError {
    override val code: String = "EC01"
    override val error: Option[String] = Some("Unexpected error")
    override val detail: Option[String] = Some(userResource)
  }

}
