package com.qrsof.empresalit.views.maincontainer.debtsToPay.actions

import com.qrsof.empresalit.views.maincontainer.ViewMainContainerGateway
import com.qrsof.empresalit.views.maincontainer.actions.UserCompaniesView
import com.qrsof.empresalit.views.maincontainer.debtsToPay.ViewDebtToPayGateway
import com.qrsof.empresalit.views.onboarding.ViewOnboardingGateway
import io.scalaland.chimney.dsl._

import jakarta.inject.{Inject, Singleton}
import scala.concurrent.ExecutionContext

@Singleton
class ViewDebtToPayActionsImpl @Inject()
(viewDebtsToPayGateway: ViewDebtToPayGateway,
 viewOnboardingGateway: ViewOnboardingGateway,
 viewMainContainerGateway: ViewMainContainerGateway)
(implicit ec: ExecutionContext) extends ViewDebtToPayActions {

  override def getDebtsToPay(filters: ListDebtToPayActionRequest): DebtsToPayResponse = {

    val facturas = viewDebtsToPayGateway
      .getDebtsToPay(filters)

    facturas
      .into[DebtsToPayResponse]
      .transform
  }

  override def addDebtToPay(newDebtToPay: NewDebtToPayAction): Either[newFacturaException, NewDebtToPayResponse] = {
    try {
      val companies: Seq[UserCompaniesView] = viewMainContainerGateway.getUserCompanies(newDebtToPay.userKey)
      val newFacturaData: NewDebtToPayData =
        newDebtToPay
          .into[NewDebtToPayData]
          .withFieldConst(_.company_key, companies.head.key)
          .transform

      val addNewFacturaResponse = viewDebtsToPayGateway.addDebtToPay(newFacturaData): NewDebtToPayResponse
      Right(addNewFacturaResponse)
    } catch {
      case e: Throwable => Left(UnknowErrorViewA(e.getMessage))
    }
  }

  override def deleteDebtToPay(debtToPayKey: String): Either[DebtToPayException, DeleteDebtToPayResponse] = {
    val debt = viewDebtsToPayGateway.findDebtToPay(debtToPayKey);
    if (debt.isEmpty) {
      Left(DebtToPayNotFoundExeption(debtToPayKey))
    } else {
      val a = viewDebtsToPayGateway.deleteDebtToPay(debtToPayKey);
      Right(a.into[DeleteDebtToPayResponse].transform)
    }

  }

  override def markAsPaid(debtToPayKey: String): Either[DebtToPayException, NewDebtToPayResponse] = {
    val debt = viewDebtsToPayGateway.findDebtToPay(debtToPayKey)
    if (debt.isEmpty) {
      Left(DebtToPayNotFoundExeption(debtToPayKey))
    } else {
      val debtToPay = viewDebtsToPayGateway.markAsPaid(debt.get)
      Right(debtToPay.into[NewDebtToPayResponse].transform)
    }
  }
}
