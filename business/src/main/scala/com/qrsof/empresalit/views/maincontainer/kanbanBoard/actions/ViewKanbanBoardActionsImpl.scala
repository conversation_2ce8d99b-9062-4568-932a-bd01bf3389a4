package com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions

import com.qrsof.empresalit.domain.logbook.LogbookService
import com.qrsof.empresalit.domain.logbook.models.{LogbookRequest, LogbookTypeAndPayload, LogbookTypes}
import com.qrsof.empresalit.domain.resources.ResourcesService
import com.qrsof.empresalit.domain.task_resources.TaskResourcesGateway
import com.qrsof.empresalit.domain.users.UsersGateway
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import com.qrsof.empresalit.views.maincontainer.employee.ViewEmployeeGateway
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.ViewKanbanBoardGateway
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.*
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.pojos.TaskResource
import io.scalaland.chimney.dsl.*
import io.scalaland.chimney.syntax.transformIntoPartial
import jakarta.inject.{Inject, Singleton}
import org.apache.pekko.http.scaladsl.common.StrictForm
import org.slf4j.{Logger, LoggerFactory}

@Singleton
class ViewKanbanBoardActionsImpl @Inject() (
    viewKanbanBoardGateway: ViewKanbanBoardGateway,
    usersGateway: UsersGateway,
    logbookService: LogbookService,
    taskResourcesGateway: TaskResourcesGateway,
    employeeGateway: ViewEmployeeGateway,
    resourcesService: ResourcesService,
    empresalitUtils: EmpresalitUtils
) extends ViewKanbanBoardActions {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewKanbanBoardActionsImpl])

  override def getKanbanTasksAndStatus(company_key: String): KanbanTasksResponse = {
    val response = viewKanbanBoardGateway.getAllTasksAndStatus(company_key)
    response
  }

  override def getKanbanFilterTasks(filters: FiltersKanbanActionRequest): KanbanTasksResponse = {
    val tasksFiltered = viewKanbanBoardGateway.getKanbanTasksFiltered(filters)
    tasksFiltered
  }

  override def getLogbookAndAttachmentsAction(companyKey: String, taskKey: String): GetLogbookApiResponse = {
    val logbooks: LogbookAndAttachmentData = viewKanbanBoardGateway.getLogbookAndAttachmentsData(companyKey, taskKey)
    GetLogbookApiResponse(logbooks)
  }

  override def newLogbook(companyKey: String, newLogbookRequest: NewLogbookRequest, userKey: String): Either[newLogbookException, Unit] = {
    try {
      val user = usersGateway.getUserByKey(userKey).get
      val attachmentsRequest: Seq[TaskAttachmentsRequest] = buildAttachmentRequest(taskKey = newLogbookRequest.taskKey, attachments = newLogbookRequest.attachments)
      val saveAttachments: Seq[TaskResource] = saveAttachment(companyKey = companyKey, taskKey = newLogbookRequest.taskKey, resources = attachmentsRequest)

      val logbookTask: LogbookRequest = LogbookRequest(
        author = user.username,
        logbookType = newLogbookRequest.logbookType,
        taskKey = newLogbookRequest.taskKey,
        payload = newLogbookRequest.payload,
        taskAttachments = saveAttachments.filter(_.reference.isDefined)
      )
      taskResourcesGateway.saveResourcesByTaskKey(saveAttachments)
      logbookService.createNewLogbook(companyKey, logbookTask)
      Right(())
    } catch {
      case e: Throwable => {
        Left(ErrorException(e.getMessage))
      }
    }

  }

  private def saveAttachment(companyKey: String, taskKey: String, resources: Seq[TaskAttachmentsRequest]): Seq[TaskResource] = {
    resources.map(resourceFile => {
      val resource = resourcesService.saveResource(
        companyKey = companyKey,
        path = s"$companyKey/${taskKey}",
        name = resourceFile.name.getOrElse(resourceFile.taskKey),
        resource = resourceFile.resource
      )
      TaskResource(empresalitUtils.generateKey(), resourceFile.taskKey, Some(resource))
    })
  }

  private def buildAttachmentRequest(taskKey: String, attachments: Seq[StrictForm.FileData]): Seq[TaskAttachmentsRequest] = {
    attachments.map(attachment => {
      TaskAttachmentsRequest(taskKey = taskKey, name = attachment.filename, resource = attachment.entity.data.toArray)
    })
  }

  override def assignTaskAction(companyKey: String, updatedTask: TaskActionRequest, userKey: String): Either[TaskException, Unit] = {

    try {
      val user = usersGateway.getUserByKey(userKey).get
      val employee = employeeGateway.findEmployee(updatedTask.responsible).get
      val logbookData: LogbookTypeAndPayload = logbookService.generateTypeAndPayload(LogbookTypes.Assignation, userName = user.username, assignedUser = Some(employee.fullname))
      val createNewLogbook =
        LogbookRequest(author = user.username, taskKey = updatedTask.taskKey, logbookType = logbookData.logbookType, payload = logbookData.payload, taskAttachments = Seq.empty)
      logbookService.createNewLogbook(updatedTask.taskKey, createNewLogbook)
      viewKanbanBoardGateway.assignTaskGateway(companyKey = companyKey, updatedTask = updatedTask.into[Task].transform)
      Right(())
    } catch
      case e =>
        Left(TaskExceptionError(updatedTask.taskKey))
  }

  override def getKanbanTasksAndStatusByFiltersKanbanActionRequest(kanbanTaskFilterRequest: FiltersKanbanActionRequest): KanbanTasksResponse = {
    viewKanbanBoardGateway.getAllTasksAndStatusWithFilters(kanbanTaskFilterRequest)

  }

}
