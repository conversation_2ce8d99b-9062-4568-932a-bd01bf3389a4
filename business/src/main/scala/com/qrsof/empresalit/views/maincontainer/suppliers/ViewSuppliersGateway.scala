package com.qrsof.empresalit.views.maincontainer.suppliers

import com.qrsof.empresalit.views.maincontainer.suppliers.actions.{FiltersActionRequest, NewSupplierData, NewSupplierResponseAction, SuppliersWithFilters}

trait ViewSuppliersGateway {
  def getSuppliers(filters: FiltersActionRequest): SuppliersWithFilters

  def getAllSuppliers(company_key: Option[String]): SuppliersWithFilters

  def addNewSupplier(newSupplierFormDataAction: NewSupplierData): NewSupplierResponseAction
}
