package com.qrsof.empresalit.views.maincontainer.employee.actions

import com.qrsof.empresalit.domain.employee_resources.pojos.EmployeeResourceData
import com.qrsof.empresalit.views.maincontainer.employee.actions.pojos.{EmployeeData, EmployeeDataUpdateRequest}

trait ViewEmployeeActions {
	def addEmployee(newEmployee: NewEmployeeAction): Either[newEmployeeException, NewEmployeeResponse]

	def getEmployee(filtersEmployeeActionRequest: FiltersEmployeeActionRequest): Employees

	def deleteEmployee(employeeKey: String): Either[EmployeeException, NewEmployeeResponse]

	def updateEmployee(employeeDataUpdateRequest: EmployeeDataUpdateRequest): Either[EmployeeException, SuccessUpdateResponse]

	def getEmployeeResources(companyKey: String, employeeKey: String): Seq[EmployeeResourceData]

	def getEmployeeData(companyKey: String, employeeKey: String): EmployeeData
}
