package com.qrsof.empresalit.views.maincontainer.debtsToPay.actions

trait ViewDebtToPayActions {
  def deleteDebtToPay(debtToPayKey: String): Either[DebtToPayException, DeleteDebtToPayResponse]

  def getDebtsToPay(filters: ListDebtToPayActionRequest): DebtsToPayResponse

  def addDebtToPay(newDebtToPay: NewDebtToPayAction): Either[newFacturaException, NewDebtToPayResponse]

  def markAsPaid(debtToPayKey: String): Either[DebtToPayException, NewDebtToPayResponse]
}
