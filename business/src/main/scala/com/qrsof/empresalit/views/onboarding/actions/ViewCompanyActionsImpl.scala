package com.qrsof.empresalit.views.onboarding.actions

import com.qrsof.empresalit.companies.SaveNewCompanyCertificateRequest
import com.qrsof.empresalit.companies.actions.SaveNewCertificateAction
import com.qrsof.empresalit.domain.brand_company.actions.BrandCompanyActions
import com.qrsof.empresalit.domain.brand_company.models.BrandCompanyActionRequest
import com.qrsof.empresalit.domain.users.{User, UsersGateway}
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import com.qrsof.empresalit.views.onboarding.ViewOnboardingGateway
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

@Singleton
class ViewCompanyActionsImpl @Inject() (
    viewOnboardingGateway: ViewOnboardingGateway,
    brandCompanyActions: BrandCompanyActions,
    userGateway: UsersGateway,
    empresalitUtils: EmpresalitUtils,
    saveNewCertificateAction: SaveNewCertificateAction
)(implicit ec: ExecutionContext)
    extends ViewCompanyActions {

  val logger: Logger = LoggerFactory.getLogger(classOf[ViewCompanyActionsImpl])
  override def addCompanyInTable(dataCompany: BasicDataCompany): CreateCompanyView = {
    val createCompany = viewOnboardingGateway.createCompany(dataCompany)
    createCompany.transformInto[CreateCompanyView]
  }

  override def addNewCompany(newCompanyActionRequest: NewCompanyActionRequestView): Either[newCompanyException, CreateCompanyViewResponse] = {

    try {
      val address_key = viewOnboardingGateway.createNewAddress(newCompanyActionRequest.address)

      val companyKey = addCompanyInTable(
        newCompanyActionRequest.dataCompany
          .into[BasicDataCompany]
          .withFieldConst(_.address_key, address_key.address_key)
          .transform
      )

      val publicKey = newCompanyActionRequest.certificates.public_key.entity.data.toArray;
      val privateKey = newCompanyActionRequest.certificates.private_key.entity.data.toArray
      val password = newCompanyActionRequest.certificates.password

      val newCompanyCertificates: SaveNewCompanyCertificateRequestView =
        newCompanyActionRequest
          .into[SaveNewCompanyCertificateRequestView]
          .withFieldConst(_.companyKey, companyKey.key)
          .withFieldConst(_.publicKey, publicKey)
          .withFieldConst(_.privateKey, privateKey)
          .withFieldConst(_.password, password)
          .transform
      saveNewCertificateAction.execute(newCompanyCertificates.transformInto[SaveNewCompanyCertificateRequest])

      userGateway.addNewUser(User(newCompanyActionRequest.userKey, newCompanyActionRequest.dataCompany.name, Some(newCompanyActionRequest.dataCompany.name)))
      val newCompanyView: NewCompanyView =
        NewCompanyView
          .into[NewCompanyView]
          .withFieldConst(_.companyKey, companyKey.key)
          .withFieldConst(_.address, newCompanyActionRequest.address)
          .withFieldConst(_.userKey, newCompanyActionRequest.userKey)
          .transform

      val createDataGeneralCompany = viewOnboardingGateway.addUserCompanie(newCompanyView)
      brandCompanyActions.addBrandCompany(
        newCompanyActionRequest.brandCompany
          .into[BrandCompanyActionRequest]
          .withFieldConst(_.logoCompany, newCompanyActionRequest.logoCompany)
          .withFieldConst(_.companyKey, companyKey.key)
          .transform
      )

      Right(createDataGeneralCompany.transformInto[CreateCompanyViewResponse])
    } catch {
      case e: Throwable => Left(UnknowErrorView(e.getMessage))
    }
  }
}
