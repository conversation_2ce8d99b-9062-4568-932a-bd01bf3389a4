package com.qrsof.empresalit.views.maincontainer.debtsReceivables

import com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.*

trait ViewDebtsReceivableGateway {
  def newDebtReceivable(debtReceivable: DebtReceivable): DebtReceivableResponse

  def getDebtsReceivable(filters: Filters): DebtsReceivableWithTotal

  def findDebt(debt_key: String): Option[DebtReceivableEntRes]

  def deleteDebt(debt_key: String): DebtReceivableResponse

  def markAsPaid(debt: DebtReceivableEntRes): DebtReceivableResponse
}
