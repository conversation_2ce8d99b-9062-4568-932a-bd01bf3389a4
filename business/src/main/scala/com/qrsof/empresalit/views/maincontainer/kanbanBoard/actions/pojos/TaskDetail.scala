package com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos

import com.qrsof.empresalit.domain.resources.pojos.ResourceRequest

import java.util.Date

case class TaskDetail(
																		 taskKey: String,
																		 title: String,
																		 quantity: Int,
																		 observations: String,
																		 entryDate: Date,
																		 outputDate: Option[Date],
																		 responsible: String,
																		 groupTaskKey: String,
																		 companyKey: String,
																		 folio: String,
																		 client: String,
																		 statusKey: String,
																		 status: String,
																		 resources: Seq[ResourceRequest]
										 
																		)
