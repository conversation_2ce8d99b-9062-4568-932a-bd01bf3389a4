package com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions

import com.qrsof.empresalit.domain.resources.pojos.ResourceRequest
import com.qrsof.empresalit.domain.status.StatusGateway
import com.qrsof.empresalit.domain.task_resources.TaskResourcesGateway
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.{StatusListResponse, TaskDetail, TaskDetailForConfirmation, TaskForConfirmationRequest}
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.ViewTaskConfirmationGateway
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}


@Singleton
class ViewTaskConfirmationActionsImpl @Inject()(viewTaskConfirmationGateway: ViewTaskConfirmationGateway, taskResourcesGateway: TaskResourcesGateway, statusGateway: StatusGateway) extends ViewTaskConfirmationActions {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewTaskConfirmationActionsImpl])

  override def getTaskByConfirmation(companyKey: String, taskKey: String): TaskDetailForConfirmation = {
		logger.info("getTaskByConfirmation::taskKey: {}", taskKey)
		val taskDetailOption: Option[TaskDetail] = viewTaskConfirmationGateway.getTaskDetail(taskKey)
		logger.info("getTaskByConfirmation::taskDetailOption: {}", taskDetailOption)
		var taskDetail: TaskDetail = taskDetailOption.getOrElse(null)

		val resources: Seq[ResourceRequest] = taskResourcesGateway.getResourcesByTaskKey(taskKey)
		val status: Seq[StatusListResponse] = statusGateway.getStatusListByCompanyKey(taskDetail.companyKey)
		taskDetail = taskDetail.copy(resources = resources)
		TaskDetailForConfirmation(taskDetail, status)
	}

  override def saveTaskConfirmation(companyKey: String, taskForConfirmationRequest: TaskForConfirmationRequest): Unit = {
		viewTaskConfirmationGateway.getTaskDetail(taskForConfirmationRequest.taskKey)
		viewTaskConfirmationGateway.saveTaskConfirmation(taskForConfirmationRequest)
	}
}
