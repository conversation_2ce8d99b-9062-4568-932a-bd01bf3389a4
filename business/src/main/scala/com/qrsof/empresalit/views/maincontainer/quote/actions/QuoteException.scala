package com.qrsof.empresalit.views.maincontainer.quote.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.views.maincontainer.quote.actions.QuoteException.UnknownErrorCode

sealed abstract class QuoteException(override val appError: AppError) extends ApplicationException(appError)

case class UnknownErrorView(userResource: String) extends QuoteException(UnknownErrorCode(userResource))

object QuoteException {
  case class UnknownErrorCode(userResource: String) extends AppError {
    override val code: String = "QUO01"
    override val error: Option[String] = Some("Unexpected Error")
    override val detail: Option[String] = Some(userResource)
  }
}
