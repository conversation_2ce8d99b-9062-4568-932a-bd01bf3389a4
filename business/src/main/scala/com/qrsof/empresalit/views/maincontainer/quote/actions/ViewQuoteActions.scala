package com.qrsof.empresalit.views.maincontainer.quote.actions

trait ViewQuoteActions {
  def addQuote(newQuoteAction: NewQuoteAction): Either[QuoteException, NewQuoteResponse]

  def getQuote(filterDefaultQuote: FilterDefaultQuote): Quotations

  def getAllQuote(company_key: Option[String]): Quotations

  def updateQuote(quoteDto: GeneralDtoInQuoteActionRequest): Either[QuoteUpdateException, SuccessUpdateQuote]

  def deleteQuote(quote_key: String): Either[QuoteDeleteException, DeleteQuoteResponse]

  def markAsFacture(quoteKey: String): Either[QuoteNotFoundExceptionUpdate, NewQuoteResponse]

  def markAsReject(quoteKey: String): Either[QuoteNotFoundExceptionUpdate, NewQuoteResponse]

  def markAsPending(quoteKey: String): Either[QuoteNotFoundExceptionUpdate, NewQuoteResponse]
}
