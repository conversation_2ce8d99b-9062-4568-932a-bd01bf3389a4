package com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions

import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.{TaskDetailForConfirmation, TaskForConfirmationRequest}

trait ViewTaskConfirmationActions {
	def getTaskByConfirmation(companyKey: String, taskKey: String): TaskDetailForConfirmation

	def saveTaskConfirmation(companyKey: String, taskForConfirmationRequest: TaskForConfirmationRequest): Unit
}
