package com.qrsof.empresalit.views.maincontainer.jobs.actions

import com.qrsof.empresalit.views.maincontainer.ViewMainContainerGateway
import com.qrsof.empresalit.views.maincontainer.actions.UserCompaniesView
import com.qrsof.empresalit.views.maincontainer.jobs.ViewJobsGateway
import io.scalaland.chimney.dsl._

import jakarta.inject.{Inject, Singleton}
import scala.concurrent.ExecutionContext

@Singleton
class ViewJobsActionsImpl @Inject()
(viewJobsGateway: ViewJobsGateway,
 viewMainContainerGateway: ViewMainContainerGateway)
(implicit ec: ExecutionContext) extends ViewJobsActions {

  override def addJobs(newJobsAction: NewJobsAction): Either[newJobsException, JobResponse] = {
    try {
      val companies: Seq[UserCompaniesView] = viewMainContainerGateway.getUserCompanies(newJobsAction.userKey)
      val newJobData: JobsData =
        newJobsAction
          .into[JobsData]
          .withFieldConst(_.company_key, companies.head.key)
          .transform

      val addNewJobResponse = viewJobsGateway.addJobs(newJobData): JobResponse
      Right(addNewJobResponse)
    } catch {
      case e: Throwable => Left(UnknowErrorView(e.getMessage))
    }
  }

  override def getJobs(companyKey: String): JobsWithTotal = {
    val jobs = viewJobsGateway
      .getJobs(
        ListJobsActionRequest(
          companyKey
        )
      )
    jobs.into[JobsWithTotal].transform
  }

  override def deleteJobs(job_key: String): Either[JobsException, JobResponse] = {
    val job = viewJobsGateway.findJobs(job_key);
    if (job.isEmpty) {
      Left(JobsNotFountException(job_key))
    } else {
      val deleteJob = viewJobsGateway.deleteJobs(job_key);
      Right(deleteJob.into[JobResponse].transform)
    }
  }

  override def updateJobs(job_key: String): Either[JobsException, JobResponse] = {
    val job = viewJobsGateway.findJobs(job_key);
    if (job.isEmpty) {
      Left(JobsNotFountException(job_key))
    } else {
      val jobToName = viewJobsGateway.updateJobs(job.get)
      Right(jobToName.into[JobResponse].transform)
    }
  }
}
