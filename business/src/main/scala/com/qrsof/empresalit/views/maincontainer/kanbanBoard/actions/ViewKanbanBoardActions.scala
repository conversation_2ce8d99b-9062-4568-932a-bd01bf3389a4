package com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions

import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.*

trait ViewKanbanBoardActions {
  def getKanbanTasksAndStatus(company_key: String): KanbanTasksResponse

  def getKanbanFilterTasks(filters: FiltersKanbanActionRequest): KanbanTasksResponse
  def newLogbook(companyKey: String, newLogbookRequest: NewLogbookRequest, userKey: String): Either[newLogbookException, Unit]
  def getLogbookAndAttachmentsAction(companyKey: String, taskKey: String): GetLogbookApiResponse

  def assignTaskAction(companyKey: String, updatedTask: TaskActionRequest, userKey: String): Either[TaskException, Unit]

  def getKanbanTasksAndStatusByFiltersKanbanActionRequest(kanbanTaskFilterRequest: FiltersKanbanActionRequest): KanbanTasksResponse
}
