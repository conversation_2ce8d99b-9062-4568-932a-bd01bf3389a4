package com.qrsof.empresalit.views.maincontainer.clients.actions

trait ViewClientsActions {
  def getClients(filters: FiltersClientsActionRequest): ClientsResponse

  def getClientsAll(company_key: Option[String]): ClientsResponse

  def newClient(clientActionRequest: ClientActionRequest): Either[ClientsExceptions, ClientResponse]

  def deleteClient(clientKey: String): Either[ClientsExceptions, ClientResponse]
}
