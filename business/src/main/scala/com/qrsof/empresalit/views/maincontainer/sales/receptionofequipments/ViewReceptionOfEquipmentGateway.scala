package com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments

import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.pojos.{ClientByReceptionOfEquipment, GroupTaskRequest, ReceptionOfEquipment, ReceptionOfEquipmentFilter}

trait ViewReceptionOfEquipmentGateway {
	def getClientsByCompanyKey(companyKey: String): Seq[ClientByReceptionOfEquipment]

	def newReceptionOfEquipment(equipmentGroup: GroupTaskRequest): Unit

	def getTotalReceptionOfEquipment(companyKey: String, receptionOfEquipmentFilter: ReceptionOfEquipmentFilter): Int

	def getReceptionOfEquipment(companyKey: String, receptionOfEquipmentFilter: ReceptionOfEquipmentFilter, pageNumber: Int, pageSize: Int): Seq[ReceptionOfEquipment]
}
