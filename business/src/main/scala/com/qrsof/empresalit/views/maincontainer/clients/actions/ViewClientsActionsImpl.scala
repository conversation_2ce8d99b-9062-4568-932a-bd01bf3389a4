package com.qrsof.empresalit.views.maincontainer.clients.actions

import com.qrsof.empresalit.domain.addressess.AddressGateway
import com.qrsof.empresalit.views.maincontainer.clients.ViewClientsGateway
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

@Singleton
class ViewClientsActionsImpl @Inject() (addressGateway: AddressGateway, viewClientsGateway: ViewClientsGateway) extends ViewClientsActions {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewClientsActionsImpl])

  override def newClient(clientActionRequest: ClientActionRequest): Either[ClientsExceptions, ClientResponse] = {
    logger.info(s"Client action Request:->{}", clientActionRequest)
    val clientExist = viewClientsGateway.findClientByRfc(clientActionRequest.clientData.rfc)
    if (clientExist.isEmpty) {
      val address = addressGateway.newAddress(clientActionRequest.addressData)
      val client = viewClientsGateway.newClient(
        clientActionRequest
          .into[Client]
          .withFieldConst(_.address_key, address.key)
          .transform
      )
      Right(client)
    } else {
      Left(ClientAlreadyExist(clientExist.get.client_key))
    }
  }

  override def deleteClient(clientKey: String): Either[ClientsExceptions, ClientResponse] = {
    val clientExist = viewClientsGateway.findClientByKey(clientKey)
    if (clientExist.isEmpty) {
      val response = viewClientsGateway.deleteClient(clientKey)
      Right(response)
    } else {
      Left(ClientNotFoundException(clientKey))
    }
  }

  override def getClients(filters: FiltersClientsActionRequest): ClientsResponse = {
    viewClientsGateway.getClients(filters)
  }

  override def getClientsAll(company_key: Option[String]): ClientsResponse = {
    viewClientsGateway.getAllClients(company_key)
  }
}
