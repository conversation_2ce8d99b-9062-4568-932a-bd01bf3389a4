package com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments

import com.qrsof.empresalit.domain.client.ClientGateway
import com.qrsof.empresalit.domain.logbook.LogbookService
import com.qrsof.empresalit.domain.logbook.models.{LogbookRequest, LogbookTypeAndPayload, LogbookTypes}
import com.qrsof.empresalit.domain.resources.ResourcesService
import com.qrsof.empresalit.domain.sendemail.pojos.EmailRequest
import com.qrsof.empresalit.domain.sendemail.{SendEmailBusiness, SendEmailUtils}
import com.qrsof.empresalit.domain.users.UsersGateway
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.pojos.*
import jakarta.inject.{Inject, Singleton}
import org.apache.pekko.http.scaladsl.common.StrictForm
import org.slf4j.{Logger, LoggerFactory}

import java.util.Base64

@Singleton
class ViewReceptionOfEquipmentActionsImpl @Inject() (
    empresalitUtils: EmpresalitUtils,
    clientGateway: ClientGateway,
    logbookService: LogbookService,
    usersGateway: UsersGateway,
    viewReceptionOfEquipmentGateway: ViewReceptionOfEquipmentGateway,
    resourcesService: ResourcesService,
    sendEmailBusiness: SendEmailBusiness,
    sendEmailUtils: SendEmailUtils
) extends ViewReceptionOfEquipmentActions {
  val logger: Logger = LoggerFactory.getLogger(classOf[ViewReceptionOfEquipmentActionsImpl])

  override def getClientsByCompany(companyKey: String): Seq[ClientByReceptionOfEquipment] = {
    viewReceptionOfEquipmentGateway.getClientsByCompanyKey(companyKey)
  }

  override def newReceptionOfEquipments(companyKey: String, request: ReceptionOfEquipmentRequest): String = {
    var equipmentGroupRequest: GroupTaskRequest = GroupTaskRequest(
      empresalitUtils.generateKey(),
      empresalitUtils.generateStringWithUUID(8),
      empresalitUtils.getDate,
      request.clientKey,
      Seq.empty,
      companyKey,
      Seq.empty,
      Seq.empty
    )

    var equipmentEvidencesRequest: Seq[EquipmentEvidencesRequest] = Seq.empty
    val equipments: Seq[Task] = request.equipments.map { equipment =>
      val taskKey = empresalitUtils.generateKey()
      equipmentEvidencesRequest = equipmentEvidencesRequest.:++(buildEquipmentEvidenceRequest(taskKey, equipment.resources))
      val status = Some("550e8400-e29b-41d4-a716-************")
      Task(
        taskKey = taskKey,
        statusKey = status,
        name = equipment.name,
        quantity = equipment.quantity,
        observations = equipment.observations,
        groupTaskKey = equipmentGroupRequest.groupTaskKey,
        entryDate = empresalitUtils.getDate,
        responsible = request.carrier
      )
    }
    val equipmentEvidences: Seq[TaskResource] = saveEquipmentEvidence(companyKey = companyKey, groupTaskKey = equipmentGroupRequest.groupTaskKey, resources = equipmentEvidencesRequest)
    equipmentGroupRequest = equipmentGroupRequest.copy(tasks = equipments, taskResources = equipmentEvidences)
    if (request.signature != null && request.signature.contains("base64,")) {
      val signature: Array[Byte] = Base64.getDecoder().decode(request.signature.split("base64,").apply(1))
      val resourceKey = resourcesService.saveResource(companyKey, s"$companyKey/${equipmentGroupRequest.groupTaskKey}", "firma_".concat(equipmentGroupRequest.folio).concat(".png"), signature)
      equipmentGroupRequest = equipmentGroupRequest.copy(groupTaskResources = Seq(GroupTaskResource(empresalitUtils.generateKey(), equipmentGroupRequest.groupTaskKey, Some(resourceKey))))
    }
    viewReceptionOfEquipmentGateway.newReceptionOfEquipment(equipmentGroupRequest)
    sendNotification(request.clientKey, equipmentGroupRequest, None);
    createLogbookForEquipments(equipmentGroupRequest, userKey = request.seller)

    equipmentGroupRequest.folio
  }

  private def saveEquipmentEvidence(companyKey: String, groupTaskKey: String, resources: Seq[EquipmentEvidencesRequest]): Seq[TaskResource] = {
    resources.map(resourceFile => {
      val resource = resourcesService.saveResource(companyKey, s"$companyKey/$groupTaskKey/${resourceFile.taskKey}", resourceFile.name.get, resourceFile.resource)
      TaskResource(empresalitUtils.generateKey(), resourceFile.taskKey, Some(resource))
    })
  }

  private def buildEquipmentEvidenceRequest(taskKey: String, evidences: Seq[StrictForm.FileData]): Seq[EquipmentEvidencesRequest] = {
    evidences.map(evidence => {
      EquipmentEvidencesRequest(taskKey = taskKey, name = evidence.filename, resource = evidence.entity.data.toArray)
    })
  }

  private def sendNotification(clientKey: String, equipmentGroupRequest: GroupTaskRequest, host: Option[String]): Unit = {
    try {
      val client = clientGateway.getClientByKey(clientKey)
      sendNewNotification(client.get.email.get, equipmentGroupRequest, None)
    } catch case exception: Exception => {}
  }

  private def sendNewNotification(email: String, equipmentGroupRequest: GroupTaskRequest, host: Option[String]): Unit = {
    try {
      var message: String = ""
      message = message
        .concat("<p style=\"font-size: 18px;\"><b>¡Gracias por realizar tu proceso con nosotros!</b></p><p style=\"font-size: 18px;\"> El registró de items se realizó con el folio <b>")
        .concat(equipmentGroupRequest.folio)
        .concat("</b>.")
      message = sendEmailUtils.buildMessage(message, None)
      logger.info("sendNewNotification::message: {}", message)
      sendEmailBusiness.sendEmailWithHtml(EmailRequest(Some("Recepción de equipo con folio ".concat(equipmentGroupRequest.folio)), message, email, Seq.empty))
    } catch
      case ex: Exception => {
        logger.error("ViewReceptionOfEquipmentActionsImpl::sendNewNotification: {}", ex.getMessage)
        throw ex
      }
  }

  override def getReceptionOfEquipment(companyKey: String, receptionOfEquipmentFilter: ReceptionOfEquipmentFilter, pageNumber: Int, pageSize: Int): ReceptionOfEquipmentPageable = {
    val total = viewReceptionOfEquipmentGateway.getTotalReceptionOfEquipment(companyKey, receptionOfEquipmentFilter)
    val result = viewReceptionOfEquipmentGateway.getReceptionOfEquipment(companyKey, receptionOfEquipmentFilter, pageNumber, pageSize)
    ReceptionOfEquipmentPageable(total, result)
  }

  private def createLogbookForEquipments(groupTask: GroupTaskRequest, userKey: String): Unit = {
    val registerLogbookTasks = groupTask.tasks.map(task => {

      val userData = usersGateway.getUserByKey(userKey).get
      val logbookData: LogbookTypeAndPayload = logbookService.generateTypeAndPayload(LogbookTypes.Creation, userName = userData.username)

      var logbookAttachmentsKeys: Seq[TaskResource] = Seq.empty
      logbookAttachmentsKeys = groupTask.taskResources.filter(taskResources => taskResources.taskKey == task.taskKey)
      LogbookRequest(
        author = userData.username,
        taskKey = task.taskKey,
        logbookType = logbookData.logbookType,
        payload = logbookData.payload,
        taskAttachments = logbookAttachmentsKeys
      )
    })

    logbookService.createLogbookForNewInputs(registerLogbookTasks)
  }
}
