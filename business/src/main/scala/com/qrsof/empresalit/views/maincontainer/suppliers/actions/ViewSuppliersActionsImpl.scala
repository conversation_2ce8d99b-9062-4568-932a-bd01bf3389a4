package com.qrsof.empresalit.views.maincontainer.suppliers.actions

import com.qrsof.empresalit.views.maincontainer.ViewMainContainerGateway
import com.qrsof.empresalit.views.maincontainer.debtsToPay.ViewDebtToPayGateway
import com.qrsof.empresalit.views.maincontainer.suppliers.ViewSuppliersGateway
import com.qrsof.empresalit.views.onboarding.ViewOnboardingGateway
import com.qrsof.empresalit.views.onboarding.actions.CompanyAddressView
import io.scalaland.chimney.dsl._

import jakarta.inject.{Inject, Singleton}

@Singleton
class ViewSuppliersActionsImpl @Inject()
(viewFacturasGateway: ViewDebtToPayGateway,
 viewOnboardingGateway: ViewOnboardingGateway,
 viewMainContainerGateway: ViewMainContainerGateway,
 viewSuppliersGateway: ViewSuppliersGateway)
() extends ViewSuppliersActions {
  override def getSuppliers(filters: FiltersActionRequest): SuppliersWithFiltersResponse = {
    val suppliersWithFilters = viewSuppliersGateway
      .getSuppliers(filters)

    suppliersWithFilters
      .into[SuppliersWithFiltersResponse]
      .transform
  }

  override def getAllSuppliers(company_key: Option[String]): SuppliersWithFiltersResponse = {
    val suppliersActions = viewSuppliersGateway
      .getAllSuppliers(company_key)

    suppliersActions
      .into[SuppliersWithFiltersResponse]
      .transform
  }


  override def addNewSupplier(newSupplierFormDataAction: NewSupplierActionRequest): Either[NewSupplierException, NewSupplierResponseAction] = {
    try {
      val addressKey = viewOnboardingGateway.createNewAddress(
        newSupplierFormDataAction.addressData
          .into[CompanyAddressView]
          .transform
      )

      val newSupplierData: NewSupplierData =
        newSupplierFormDataAction
          .into[NewSupplierData]
          .withFieldConst(_.address_key, addressKey.address_key)
          .withFieldConst(_.company_key, newSupplierFormDataAction.companyKey)
          .transform

      val supplierResponseAction = viewSuppliersGateway.addNewSupplier(newSupplierData)

      Right(supplierResponseAction)
    } catch {
      case e: Throwable => Left(UnknowErrorView(e.getMessage))
    }
  }
}
