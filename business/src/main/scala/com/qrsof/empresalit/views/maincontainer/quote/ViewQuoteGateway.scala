package com.qrsof.empresalit.views.maincontainer.quote

import com.qrsof.empresalit.views.maincontainer.quote.actions.*

trait ViewQuoteGateway {

  def addQuote(newQuoteData: NewQuoteData): NewQuoteResponse

  def getQuote(filterQuote: FiltersQuote): Quotations

  def getAllQuoteByUser(company_key: Option[String]): Quotations

  def findQuote(quote_key: String): Option[QuoteEnt]

  def updateQuote(quoteDto: GeneralDtoInQuoteActionRequest): Either[QuoteUpdateException, SuccessUpdateQuote]

  def deleteQuote(quote_key: String): <PERSON>uo<PERSON><PERSON><PERSON>

  def markAsFacture(quoteEnt: QuoteEnt): <PERSON>uote<PERSON><PERSON>

  def markAsReject(quoteEnt: QuoteEnt): Quote<PERSON><PERSON>

  def markAsPending(quoteEnt: QuoteEnt): QuoteKey
}
