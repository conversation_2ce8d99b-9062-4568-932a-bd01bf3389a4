package com.qrsof.empresalit.views.maincontainer.employee.actions

import com.qrsof.empresalit.domain.employee_resources.EmployeeResourcesGateway
import com.qrsof.empresalit.domain.employee_resources.pojos.{EmployeeResource, EmployeeResourceData}
import com.qrsof.empresalit.domain.employees.EmployeesGateway
import com.qrsof.empresalit.domain.employees.pojos.EmployeeDto
import com.qrsof.empresalit.domain.resources.ResourcesService
import com.qrsof.empresalit.domain.user_oauth.UserOAuthService
import com.qrsof.empresalit.domain.users.UsersGateway
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import com.qrsof.empresalit.views.maincontainer.ViewMainContainerGateway
import com.qrsof.empresalit.views.maincontainer.actions.UserCompaniesView
import com.qrsof.empresalit.views.maincontainer.companies.ViewCompaniesGateway
import com.qrsof.empresalit.views.maincontainer.employee.ViewEmployeeGateway
import com.qrsof.empresalit.views.maincontainer.employee.actions.pojos.{EmployeeData, EmployeeDataUpdateRequest, EmployeeResourceRequest}
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.apache.pekko.http.scaladsl.common.StrictForm
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

@Singleton
class ViewEmployeeActionsImpl @Inject() (
    empresalitUtils: EmpresalitUtils,
    viewEmployeeGateway: ViewEmployeeGateway,
    usersGateway: UsersGateway,
    viewCompaniesGateway: ViewCompaniesGateway,
    viewMainContainerGateway: ViewMainContainerGateway,
    userOAuthService: UserOAuthService,
    resourcesService: ResourcesService,
    employeeResourcesGateway: EmployeeResourcesGateway,
    employeesGateway: EmployeesGateway
)(implicit
    ec: ExecutionContext
) extends ViewEmployeeActions {

  val logger: Logger = LoggerFactory.getLogger(classOf[ViewEmployeeActionsImpl])

  override def addEmployee(newEmployeeAction: NewEmployeeAction): Either[newEmployeeException, NewEmployeeResponse] = {
    try {
      val companies: Seq[UserCompaniesView] = viewMainContainerGateway.getUserCompanies(newEmployeeAction.userKey)
      val newEmployee: NewEmployeeData = newEmployeeAction
        .into[NewEmployeeData]
        .withFieldConst(_.companyKey, companies.head.key)
        .withFieldConst(_.employeeKey, empresalitUtils.generateKey())
        .withFieldConst(_.fullname, buildFullname(newEmployeeAction.name, newEmployeeAction.lastname, newEmployeeAction.motherLastname, ""))
        .transform;
      val files: Seq[EmployeeResource] = saveFiles(newEmployee.employeeKey, newEmployeeAction.companyKey, newEmployeeAction.files)
      val addNewEmployeeResponse = viewEmployeeGateway.addEmployee(newEmployee, files): NewEmployeeResponse

      if (newEmployeeAction.credentials.isDefined) {
        registrySession(addNewEmployeeResponse.key, newEmployeeAction.credentials.get.username, newEmployeeAction.credentials.get.password, Some(newEmployee.name))
      }

      Right(addNewEmployeeResponse)
    } catch {
      case e: Throwable =>
        Left(UnknowErrorViewA(e.getMessage))
    }
  }

  private def saveFiles(employeeKey: String, companyKey: String, filesRequest: Seq[StrictForm.FileData] /*, filesOptional:Seq[StrictForm.FileData]*/ ): Seq[EmployeeResource] = {
    var files: Seq[EmployeeResourceRequest] = buildEmployeeFiles(employeeKey, true, filesRequest)
    // files.:++=(buildEmployeeFiles(employeeKey, false, filesOptional))
    saveEmployeeResources(companyKey, employeeKey, files)

  }

  private def buildEmployeeFiles(employeeKey: String, required: Boolean, files: Seq[StrictForm.FileData]): Seq[EmployeeResourceRequest] = {
    files.map(file => {
      EmployeeResourceRequest(required = required, name = file.filename, file = file.entity.data.toArray)
    })
  }

  private def saveEmployeeResources(companyKey: String, employeeKey: String, resources: Seq[EmployeeResourceRequest]): Seq[EmployeeResource] = {
    resources.map(resourceFile => {
      val resource = resourcesService.saveResource(companyKey, s"$companyKey/employee/${employeeKey}/documents/", resourceFile.name.get, resourceFile.file)
      EmployeeResource(empresalitUtils.generateKey(), resourceFile.required, employeeKey, resource)
    })
  }

  private def registrySession(employeeKey: String, username: String, password: String, name: Option[String]): Unit = {
    try {
//			val user = userOAuthService.registryUser(RegisterActionRequest(username = username, password = password), name)
//			employeesGateway.putAccessUserKey(employeeKey = employeeKey, accessUserKey = user.key)
    } catch
      case exception: Exception => {
        logger.error("registrySession::error: {}", exception.getMessage)
      }
  }

  override def getEmployee(filtersEmployeeActionRequest: FiltersEmployeeActionRequest): Employees = {
    val filters = filtersEmployeeActionRequest.into[FiltersEmployee].transform
    viewEmployeeGateway.getEmployee(filters).into[Employees].transform
  }

  override def deleteEmployee(employeeKey: String): Either[EmployeeException, NewEmployeeResponse] = {
    val employee = viewEmployeeGateway.findEmployee(employeeKey);
    if (employee.isEmpty) {
      Left(EmployeeNotFoundException(employeeKey))
    } else {
      val a = viewEmployeeGateway.deleteEmployee(employeeKey);
      Right(a.into[NewEmployeeResponse].transform)
    }
  }

  override def updateEmployee(updateRequest: EmployeeDataUpdateRequest): Either[EmployeeException, SuccessUpdateResponse] = {
    val employee: EmployeeDto = employeesGateway.getEmployeeByKey(updateRequest.employeeKey).getOrElse(throw new Exception("Empleado no encontrado"))
    val files: Seq[EmployeeResource] = saveFiles(updateRequest.employeeKey, employee.companyKey, updateRequest.files)
    if (updateRequest.credentials.isDefined && employee.accessUserKey.isEmpty) {
      registrySession(employee.employeeKey, updateRequest.credentials.get.username, updateRequest.credentials.get.password, Some(updateRequest.name))
    }
    deleteFiles(updateRequest.filesToDelete)
    viewEmployeeGateway.updateEmployee(
      EmployeeDataUpdate(
        employeeKey = updateRequest.employeeKey,
        fullname = buildFullname(updateRequest.name, updateRequest.lastname, updateRequest.motherLastname, ""),
        employeeNumber = updateRequest.employeeNumber.toUpperCase,
        name = updateRequest.name,
        lastname = updateRequest.lastname,
        motherLastname = updateRequest.motherLastname,
        email = updateRequest.email,
        entryDate = updateRequest.entryDate,
        birthDate = updateRequest.birthDate,
        areaKey = updateRequest.areaKey,
        accountNumber = updateRequest.accountNumber,
        jobKey = updateRequest.jobKey,
        amount = updateRequest.amount,
        bonus = updateRequest.bonus,
        files = files
      )
    )
  }

  override def getEmployeeResources(companyKey: String, employeeKey: String): Seq[EmployeeResourceData] = {
    employeeResourcesGateway.getResourcesDataByEmployeeKey(employeeKey)
  }

  override def getEmployeeData(companyKey: String, employeeKey: String): EmployeeData = {
    val employeeData: Option[EmployeeData] = viewEmployeeGateway.getEmployeeByKey(employeeKey)
    if (employeeData.isDefined) {
      employeeData.get.copy(resources = getEmployeeResources(companyKey, employeeKey))
    } else {
      employeeData.getOrElse(throw new Exception("Empleado no encontrado"))
    }
  }

  private def buildFullname(name: String, lastname: String, motherLastname: String, typeFullname: String): String = {
    val fullname = lastname.trim.concat(" ").concat(motherLastname)
    if (typeFullname == "REVERSE") {
      fullname.concat(" ").concat(name)
    } else {
      name.concat(" ").concat(fullname)
    }
  }

  private def deleteFiles(filesToDelete: Seq[String]): Unit = {
    if (filesToDelete.nonEmpty) {
      resourcesService.deleteResources(filesToDelete)
    }
  }
}
