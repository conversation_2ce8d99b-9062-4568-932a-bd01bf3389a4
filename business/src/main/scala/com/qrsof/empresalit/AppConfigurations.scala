package com.qrsof.empresalit

import com.qrsof.empresalit.domain.storage.digitalocean.DigitalOceanConfigs


trait AppConfigurations {
  def environment: AppEnvironment

  def appPort: String

  def appHost: String

  def listenAddresses: String

  def oauthConfigs: OauthConfigs

  def dbSchemaName: String

  def digitalOceanConfigs: DigitalOceanConfigs

  def apptackKey: String

  def apptackSecret: String

  def clientUrl: String
}
