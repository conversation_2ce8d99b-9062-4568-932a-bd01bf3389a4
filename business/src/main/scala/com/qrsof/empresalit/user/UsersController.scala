package com.qrsof.empresalit.user

import com.qrsof.core.api.ApiErrorResponse
import com.qrsof.empresalit.controllers.oauth.UserActionRequest
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.{Operation, Parameter}
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.{GET, Path, Produces}
import play.api.mvc.{Action, AnyContent}

trait UsersController {
  @Path("/empresalit/{user_key}/validator_user")
  @GET
  @Produces(Array(MediaType.APPLICATION_JSON))
  @Operation(
    summary = "Get user verified by user key",
    tags = Array("View verified"),
    parameters = Array(
      new Parameter(
        name = "user_key",
        in = ParameterIn.PATH,
        required = true,
        description = "User key",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[String]))
        )
      )
    ),
    security = Array(
      new SecurityRequirement(
        name = "Bearer"
      )
    ),
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        content = Array(
          new Content(
            schema = new Schema(
              implementation = classOf[UserActionRequest]
            )
          )
        )
      ),
      new ApiResponse(
        responseCode = "404",
        content = Array(
          new Content(
            schema = new Schema(implementation = classOf[ApiErrorResponse])
          )
        )
      )
    )
  )
  def getDataUser(@Parameter(hidden = true) userKey: String): Action[AnyContent]
}
