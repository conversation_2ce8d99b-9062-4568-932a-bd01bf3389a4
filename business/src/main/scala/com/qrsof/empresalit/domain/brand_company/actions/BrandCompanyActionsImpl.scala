package com.qrsof.empresalit.domain.brand_company.actions

import com.google.inject.{Inject, Singleton}
import com.qrsof.empresalit.domain.brand_company.BrandCompanyGateway
import com.qrsof.empresalit.domain.brand_company.models.{BrandCompanyActionRequest, BrandCompanyGatewayRequest}
import com.qrsof.empresalit.domain.resources.ResourcesService
import io.scalaland.chimney.dsl.into
import org.slf4j.{Logger, LoggerFactory}

@Singleton
class BrandCompanyActionsImpl @Inject() (resourcesService: ResourcesService, brandCompanyGateway: BrandCompanyGateway) extends BrandCompanyActions {
  val logger: Logger = LoggerFactory.getLogger(classOf[BrandCompanyActionsImpl])
  override def addBrandCompany(brandCompany: BrandCompanyActionRequest): String = {

    var resourceKey: String = String()
    brandCompany.logoCompany match {
      case Some(value) =>
        resourceKey = resourcesService.saveResource(
          brandCompany.companyKey,
          s"${brandCompany.companyKey}/logo",
          brandCompany.logoCompany.get.filename.get,
          brandCompany.logoCompany.get.entity.data.toArray
        )

      case None =>
        resourceKey = null
    }

    val brandCompanyRequest = brandCompany
      .into[BrandCompanyGatewayRequest]
      .withFieldConst(_.colorPalette, brandCompany.colorPalette)
      .withFieldConst(_.color, brandCompany.color)
      .withFieldConst(_.companyKey, brandCompany.companyKey)
      .withFieldConst(_.logoResourceKey, Some(resourceKey))
      .transform
    val brandCompanyKey = brandCompanyGateway.addBrandCompany(brandCompanyRequest)
    brandCompanyKey
  }

}
