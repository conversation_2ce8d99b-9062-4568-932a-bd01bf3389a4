package com.qrsof.empresalit.domain.status

import com.qrsof.empresalit.domain.status.pojos.{ChangeStatusOrderRequest, StatusDto}
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.StatusListResponse

trait StatusGateway {
	def getStatusListByCompanyKey(companyKey: String): Seq[StatusListResponse]
	def getStatusByStatusKey(statusKey: String): Option[StatusDto]
	def changeStatusOrder(changeStatusOrderRequest: ChangeStatusOrderRequest): Unit
}
