package com.qrsof.empresalit.domain.holidays.actions

import com.google.inject.{Inject, Singleton}
import com.qrsof.empresalit.domain.holidays.HoliDaysGateway
import com.qrsof.empresalit.domain.holidays.models.{HoliDayDTO, HoliDaysResponse}
import org.slf4j.{Logger, LoggerFactory}

import java.text.SimpleDateFormat
import java.util.Date
@Singleton
class HoliDaysActionsImpl @Inject() (holiDaysGateway: HoliDaysGateway) extends HoliDaysActions {
  val logger: Logger = LoggerFactory.getLogger(classOf[HoliDaysActionsImpl])
  override def getHoliDays: HoliDaysResponse = {
    val holiDays = holiDaysGateway.getHoliDays
    HoliDaysResponse(holiDays)
  }

  override def getHoliDaysFiltered(startDate: Date, endDate: Date): Seq[HoliDayDTO] = {
    val dateFormat = new SimpleDateFormat("yyyy-MM-dd")
    val startDateStr = dateFormat.format(startDate)
    val endDateStr = dateFormat.format(endDate)
    holiDaysGateway.getHoliDaysFiltered(startDateStr, endDateStr)
  }
}
