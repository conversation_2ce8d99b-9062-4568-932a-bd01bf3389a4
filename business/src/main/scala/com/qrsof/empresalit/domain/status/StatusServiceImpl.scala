package com.qrsof.empresalit.domain.status

import com.qrsof.empresalit.domain.status.pojos.{ChangeStatusOrderRequest, StatusDto, StatusOrderRequest}
import jakarta.inject.{Inject, Singleton}

@Singleton
class StatusServiceImpl @Inject()(statusGateway: StatusGateway) extends StatusService{
	
	override def changeStatusOrder(companyKey: String, statusOrderRequest: StatusOrderRequest): Unit = {
		val status: StatusDto = statusGateway.getStatusByStatusKey(statusKey = statusOrderRequest.statusKey).getOrElse( throw new ClassNotFoundException("Status not found"))
		var changeStatusOrderRequest: ChangeStatusOrderRequest = ChangeStatusOrderRequest(statusOrderRequest.statusKey, statusOrderRequest.currentIndex, status.orderProcess, 0, 0, false);
		if(statusOrderRequest.currentIndex > status.orderProcess){
			changeStatusOrderRequest = changeStatusOrderRequest.copy(firstOrderIndexToUpdate = status.orderProcess  , lastOrderIndexToUpdate = statusOrderRequest.currentIndex, isReduce =  true)
		}else{
			changeStatusOrderRequest = changeStatusOrderRequest.copy(firstOrderIndexToUpdate  = statusOrderRequest.currentIndex , lastOrderIndexToUpdate = status.orderProcess )
		}
		statusGateway.changeStatusOrder(changeStatusOrderRequest)
		
	}
}
