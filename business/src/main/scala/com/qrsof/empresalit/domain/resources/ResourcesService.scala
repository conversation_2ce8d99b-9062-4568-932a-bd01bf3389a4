package com.qrsof.empresalit.domain.resources

import com.qrsof.empresalit.domain.resources.pojos.ResourceResponse

trait ResourcesService {
	def saveResource(companyKey: String, path: String, name: String, resource: Array[Byte]): String

	def getResource(resourceKey: String): ResourceResponse

	def getResourceByCompanyKeyAndResourceKey(companyKey: String, resourceKey: String): ResourceResponse
	
	def deleteResource(resourceKey: String): Unit

	def deleteResources(resourceKey: Seq[String]): Unit
}
