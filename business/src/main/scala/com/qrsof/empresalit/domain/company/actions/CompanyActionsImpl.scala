package com.qrsof.empresalit.domain.company.actions

import com.qrsof.empresalit.companies.actions.SaveNewCertificateAction
import com.qrsof.empresalit.companies.{CompanyCertificate, CompanyGateway}
import com.qrsof.empresalit.domain.addressess.AddressGateway
import com.qrsof.empresalit.domain.addressess.actions.models.AddressRequestGateway
import com.qrsof.empresalit.domain.brand_company.BrandCompanyGateway
import com.qrsof.empresalit.domain.brand_company.models.{BrandCompanyGatewayRequest, UpdateBrandCompanyRequestGateway}
import com.qrsof.empresalit.domain.company.DomainCompanyGateway
import com.qrsof.empresalit.domain.company.actions.models.{DataCompanyRequestAction, DataCompanyResponse}
import com.qrsof.empresalit.domain.resources.ResourcesService
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import io.scalaland.chimney.dsl.*
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

@Singleton
class CompanyActionsImpl @Inject() (
    companyGateway: DomainCompanyGateway,
    addressGateway: AddressGateway,
    brandCompanyGateway: BrandCompanyGateway,
    certificatesAction: CompanyGateway,
    resourcesService: ResourcesService,
    empresalitUtils: EmpresalitUtils,
    saveNewCertificateAction: SaveNewCertificateAction
)(implicit ec: ExecutionContext)
    extends CompanyActions {

  val logger: Logger = LoggerFactory.getLogger(classOf[CompanyActionsImpl])
  override def getCompanyDataByCompanyKey(companyKey: String): DataCompanyResponse = {
    companyGateway.getCompanyDataByCompanyKey(companyKey)
  }
  override def updateCompanyDataAction(dataCompanyRequestAction: DataCompanyRequestAction): Either[CompanyException, Unit] = {
    logger.info(s"CompanyActionsImpl::updateCompanyDataAction::dataCompanyRequestAction:->{}", dataCompanyRequestAction)
    try {
      addressGateway.updateAddress(dataCompanyRequestAction.address.into[AddressRequestGateway].withFieldConst(_.address_key, dataCompanyRequestAction.address.addressKey).transform)
      val isNewBrand = dataCompanyRequestAction.brandCompany.brandKey == null

      if isNewBrand then
        (for {
          logoFile <- dataCompanyRequestAction.brandCompany.logoFile
          filename <- logoFile.filename
        } yield {
          val resourceLogoKey = resourcesService.saveResource(
            dataCompanyRequestAction.dataCompany.companyKey,
            s"${dataCompanyRequestAction.dataCompany.companyKey}/logo",
            filename,
            logoFile.entity.data.toArray
          )
          brandCompanyGateway.addBrandCompany(
            BrandCompanyGatewayRequest(
              dataCompanyRequestAction.dataCompany.companyKey,
              dataCompanyRequestAction.brandCompany.colorPalette,
              dataCompanyRequestAction.brandCompany.color,
              Some(resourceLogoKey)
            )
          )
        }).getOrElse {
          brandCompanyGateway.addBrandCompany(
            BrandCompanyGatewayRequest(
              dataCompanyRequestAction.dataCompany.companyKey,
              dataCompanyRequestAction.brandCompany.colorPalette,
              dataCompanyRequestAction.brandCompany.color,
              None
            )
          )
        }
      else
        brandCompanyGateway.updateBrandCompany(
          dataCompanyRequestAction.brandCompany
            .into[UpdateBrandCompanyRequestGateway]
            .withFieldConst(_.companyKey, dataCompanyRequestAction.dataCompany.companyKey)
            .transform
        )

      // val keyCertificates = certificatesAction.getCompanyCertificateByCompanyKey(dataCompanyRequestAction.dataCompany.companyKey)
//      if (keyCertificates.isDefined) {
//        certificatesAction.updateCompanyCertificates(
//          dataCompanyRequestAction.certificatesCompany
//            .into[CompanyCertificate]
//            .withFieldConst(_.key, String())
//            .withFieldConst(_.companyKey, dataCompanyRequestAction.dataCompany.companyKey)
//            .withFieldConst(_.noCertificado, String())
//            .withFieldConst(_.privateKey, dataCompanyRequestAction.certificatesCompany.private_key.entity.data.toArray)
//            .withFieldConst(_.password, dataCompanyRequestAction.certificatesCompany.password)
//            .withFieldConst(_.publicKey, dataCompanyRequestAction.certificatesCompany.public_key.entity.data.toArray)
//            .transform
//        )
//      }

      certificatesAction.saveCompanyCertificate(
        dataCompanyRequestAction.certificatesCompany
          .into[CompanyCertificate]
          .withFieldConst(_.key, empresalitUtils.generateKey())
          .withFieldConst(_.companyKey, dataCompanyRequestAction.dataCompany.companyKey)
          .withFieldConst(_.noCertificado, String())
          .withFieldConst(_.privateKey, dataCompanyRequestAction.certificatesCompany.private_key.entity.data.toArray)
          .withFieldConst(_.password, dataCompanyRequestAction.certificatesCompany.password)
          .withFieldConst(_.publicKey, dataCompanyRequestAction.certificatesCompany.public_key.entity.data.toArray)
          .transform
      )
      companyGateway.updateCompanyDataGateway(dataCompanyRequestAction.dataCompany)
      Right(())
    } catch
      case e: Exception => {
        logger.info("CompanyActionsImpl::updateCompanyDataAction::error:->{}", e)
        throw e
      }
  }
}
