package com.qrsof.empresalit.domain.seniority_benefits

import com.qrsof.empresalit.domain.seniority_benefits.pojos.EmployeeVacationsData
import com.qrsof.empresalit.domain.employees.EmployeesGateway
import com.qrsof.empresalit.domain.employees.pojos.EmployeeDto
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import jakarta.inject.{Inject, Singleton}

import java.util.{Calendar, Date}

@Singleton
class SeniorityBenefitsServiceImpl @Inject()(employeesGateway: EmployeesGateway, seniorityBenefitsGateway: SeniorityBenefitsGateway, empresalitUtils: EmpresalitUtils) extends SeniorityBenefitsService{

	override def getVacationDaysByEmployee(employeeKey: String): EmployeeVacationsData = {
		val employee: EmployeeDto = employeesGateway.getEmployeeByKey(employeeKey).getOrElse(throw new Exception("Empleado no registrado"))
		
		val years: Int = empresalitUtils.getYears(employee.entryDate, new Date())
		val seniorityBenefit = seniorityBenefitsGateway.getSeniorityBenefitLastByYear(years)
		val days = 0;
		EmployeeVacationsData(	getDateWithAddYears(employee.entryDate, years), seniorityBenefit.get.vacationDays, seniorityBenefit.get.vacationDays - days)
	}

	private def getDateWithAddYears(date: Date, years: Int): Date = {
		val calendar = Calendar.getInstance()
		calendar.setTime(date)
		calendar.add(Calendar.YEAR, years)
		calendar.getTime
	}
	
	

	
}
