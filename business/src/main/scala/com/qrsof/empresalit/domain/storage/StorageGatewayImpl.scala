package com.qrsof.empresalit.domain.storage

import com.qrsof.core.storage.{NewResource, ResourceKey, StorageService}
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

import java.io.ByteArrayInputStream

@Singleton
class StorageGatewayImpl @Inject()(storageService: StorageService) extends StorageGateway {

	val logger: Logger = LoggerFactory.getLogger(classOf[StorageGatewayImpl])

	override def saveResource(path: String, resource: Array[Byte]): String = {
		val getKeyFile = storageService.save(NewResource(new ByteArrayInputStream(resource), path, None))
		getKeyFile.key
	}

	override def getResource(path: Option[String], resourceReference: String): Array[Byte] = {
		val resp = storageService.retrieve(ResourceKey(resourceReference))
		resp.get.content.readAllBytes()
	}

	override def deleteResource(path: Option[String], resourceReference: String): Unit = {
		storageService.delete(ResourceKey(resourceReference))
	}
}
