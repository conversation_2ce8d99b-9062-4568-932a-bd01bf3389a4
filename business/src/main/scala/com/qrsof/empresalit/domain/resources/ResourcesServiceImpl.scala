package com.qrsof.empresalit.domain.resources

import com.qrsof.empresalit.domain.resources.pojos.{ResourceRequest, ResourceResponse}
import com.qrsof.empresalit.domain.storage.StorageGateway
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

@Singleton
class ResourcesServiceImpl @Inject()(empresalitUtils: EmpresalitUtils, resourcesGateway: ResourcesGateway, storageGateway: StorageGateway) extends ResourcesService {
	val logger: Logger = LoggerFactory.getLogger(classOf[ResourcesServiceImpl])

	override def saveResource(companyKey: String, path: String, name: String, resource: Array[Byte]): String = {
		val reference = storageGateway.saveResource(path, resource)
		val request: ResourceRequest = ResourceRequest(empresalitUtils.generateKey(), companyKey, name, reference, Some(path))
		resourcesGateway.saveResource(request)
		request.resourceKey
	}

	override def getResource(resourceKey: String): ResourceResponse = {
		val resource = resourcesGateway.getResource(resourceKey)
		val file = storageGateway.getResource(resource.path, resource.reference)
		ResourceResponse(resource.name, file)
	}

	override def getResourceByCompanyKeyAndResourceKey(companyKey: String, resourceKey: String): ResourceResponse = {
		val resource = resourcesGateway.getResourceByCompanyKeyAndResourceKey(companyKey, resourceKey)
		val file = storageGateway.getResource(resource.path, resource.reference)
		ResourceResponse(resource.name, file)
	}

	override def deleteResource(resourceKey: String): Unit = {
		val resource = resourcesGateway.getResource(resourceKey)
		try{
			storageGateway.deleteResource(resource.path, resource.reference)
		}catch
			case exception: Exception =>{
				logger.error("deleteResource::exception: {}", exception.getMessage)
			}
		
	}

	override def deleteResources(resourceKeys: Seq[String]): Unit = {
		val resources = resourcesGateway.getResourcesByKeys(resourceKeys)
		var resourcesToDelete: Seq[String] = Seq.empty
		resources.foreach(resource => {
			try {
				storageGateway.deleteResource(resource.path, resource.reference)
				resourcesToDelete.:+=(resource.resourceKey)
			} catch
				case exception: Exception => {
					logger.error("deleteResources::exception: {}", exception.getMessage)
				}
		})
		if(resourcesToDelete.nonEmpty){
			resourcesGateway.deleteResourcesByKeys(resourcesToDelete)
		}
		
	}
}
