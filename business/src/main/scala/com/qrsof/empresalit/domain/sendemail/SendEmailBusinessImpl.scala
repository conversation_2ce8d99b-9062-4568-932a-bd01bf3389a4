package com.qrsof.empresalit.domain.sendemail

import com.qrsof.empresalit.domain.sendemail.pojos.EmailRequest
import jakarta.activation.{DataHandler, URLDataSource}
import jakarta.inject.Inject
import jakarta.mail.*
import jakarta.mail.internet.{InternetAddress, MimeBodyPart, MimeMessage, MimeMultipart}
import org.slf4j.{Logger, LoggerFactory}

import java.net.URL
import java.util.Properties

class SendEmailBusinessImpl @Inject() (
) extends SendEmailBusiness {

  val logger: Logger = LoggerFactory.getLogger(classOf[SendEmailBusiness])
  val emailHost: String = "Test"
  val emailPort: String = "Test"
  val emailUsername: String = "Test"
  val emailPassword: String = "Test"

  def initSession(): Session = {
    if ((emailHost == null || emailHost == "") || (emailUsername == null || emailUsername == "") || (emailPassword == null || emailPassword == "")) {
      logger.error("SendEmailBusinessImpl::initSession: {}", "Se requiere accesos para email emisor")
      throw new MessagingException()
    }
    val props: Properties = new Properties()
    props.put("mail.smtp.host", emailHost)
    props.put("mail.smtp.port", emailPort)
    props.put("mail.smtp.auth", "true")
    props.put("mail.smtp.starttls.enable", "true")
    Session.getInstance(
      props,
      new Authenticator {
        override def getPasswordAuthentication = new PasswordAuthentication(emailUsername, emailPassword)
      }
    );
  }

  override def sendSimpleEmail(emailRequest: EmailRequest): Unit = {
    try {
      logger.info("SendEmailBusinessImpl::sendSimpleEmail::email: {} y Mensaje: {} ", emailRequest.email, emailRequest.message)
      val session = initSession()
      val message: MimeMessage = MimeMessage(session)
      message.setFrom(emailUsername)
      message.setRecipient(Message.RecipientType.TO, new InternetAddress(emailRequest.email))
      message.setSubject(emailRequest.title.getOrElse(""))
      message.setText(emailRequest.message)
      Transport.send(message)

    } catch
      case messageException: MessagingException => {
        logger.error("SendEmailBusinessImpl::sendEmail::messageException: {}", messageException.toString)
      }
  }

  override def sendEmailWithHtml(emailRequest: EmailRequest): Unit = {
    try {
      logger.info("SendEmailBusinessImpl::sendEmail::email: {} y Mensaje: {} ", emailRequest.email, emailRequest.message)
      val session = initSession()
      val message = MimeMessage(session)
      message.setFrom(emailUsername)
      message.setRecipient(Message.RecipientType.TO, new InternetAddress(emailRequest.email))
      message.setSubject(emailRequest.title.getOrElse(""))
      val multipart = new MimeMultipart("related")

      // Create content by message
      val messageBodyPart: BodyPart = new MimeBodyPart();
      messageBodyPart.setContent(emailRequest.message, "text/html")
      multipart.addBodyPart(messageBodyPart)

      // Create attachment into message
      emailRequest.files.foreach(file => {
        val url = new URL(file)
        val attachmentPart = new MimeBodyPart()
        val source = new URLDataSource(url)
        attachmentPart.setDataHandler(new DataHandler(source))
        attachmentPart.setFileName(url.getFile())
        multipart.addBodyPart(attachmentPart)
      })
      message.setContent(multipart)
      Transport.send(message)

    } catch
      case messageException: MessagingException => {
        logger.error("SendEmailBusinessImpl::sendEmail::messageException: {}", messageException.toString)
      }
  }
}
