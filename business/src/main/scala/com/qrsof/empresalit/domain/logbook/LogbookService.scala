package com.qrsof.empresalit.domain.logbook

import com.qrsof.empresalit.domain.logbook.models.{LogbookRequest, LogbookTypeAndPayload, LogbookTypes}

trait LogbookService {
  def createNewLogbook(companyKey: String, newLogbook: LogbookRequest): Unit

  def createLogbookForNewInputs(newLogbooks: Seq[LogbookRequest]): Unit

  def generateTypeAndPayload(
      logbookType: LogbookTypes,
      userName: String,
      comment: Option[String] = None,
      assignedUser: Option[String] = None
  ): LogbookTypeAndPayload = {
    logbookType match {
      case LogbookTypes.Comment     => LogbookTypeAndPayload(logbookType = "Comentario", payload = s"Añadió un comentario: ${comment.getOrElse("")}")
      case LogbookTypes.Attachments => LogbookTypeAndPayload(logbookType = "Archivos", payload = "Añadió archivos:") // Map("logbookType" -> "Archivos", "payload" -> s"Añadió archivos:")
      case LogbookTypes.Assignation =>
        LogbookTypeAndPayload(
          logbookType = "Asignación",
          payload = s"Asignó la tarea a: ${assignedUser.getOrElse("")}"
        )
      case LogbookTypes.Creation => LogbookTypeAndPayload(logbookType = "Creación", payload = s"Creó la tarea")
      case _                     => throw new Error("El tipo de registro no existe")
    }
  }

}
