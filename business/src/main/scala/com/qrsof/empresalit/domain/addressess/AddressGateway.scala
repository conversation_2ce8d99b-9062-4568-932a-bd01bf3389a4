package com.qrsof.empresalit.domain.addressess

import com.qrsof.empresalit.domain.addressess.actions.models.AddressRequestGateway
import com.qrsof.empresalit.domain.addressess.actions.{AddressDataActionRequest, AddressEnt, AddressKey}

trait AddressGateway {
  def newAddress(address: AddressDataActionRequest): Address<PERSON><PERSON>

  def deleteAddress(address_key: String): Unit

  def findAddress(addressKey: String): AddressEnt

  def updateAddress(address: AddressRequestGateway): Unit
}
