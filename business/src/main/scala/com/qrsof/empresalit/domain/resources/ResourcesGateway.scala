package com.qrsof.empresalit.domain.resources

import com.qrsof.empresalit.domain.resources.pojos.ResourceRequest

trait ResourcesGateway {
	def saveResource(resourceRequest: ResourceRequest): Unit

	def getResource(resourceKey: String): ResourceRequest

	def getResourceByCompanyKeyAndResourceKey(companyKey: String, resourceKey: String): ResourceRequest

	def getResourcesByKeys(resourceKeys: Seq[String]): Seq[ResourceRequest]
	
	def deleteResourcesByKeys(resourceKeys: Seq[String]): Unit
}
