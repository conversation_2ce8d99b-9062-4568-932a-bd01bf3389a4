package com.qrsof.empresalit.domain.exceptions

object ErrorCodes extends Enumeration {
  type ErrorCode = ErrorCodeVal

  protected case class ErrorCodeVal(code: String, description: String) extends super.Val {}

  implicit def valueToVal(x: Value): Val = x.asInstanceOf[ErrorCodeVal]

  val AuthenticationTokenError = ErrorCodeVal("A001", "There is no Authentication bearer token")

  val GenerationError = ErrorCodeVal("G001", "There is no Authentication bearer token")

  val InvalidParameterError = ErrorCodeVal("E001", "Invalid parameter")

}
