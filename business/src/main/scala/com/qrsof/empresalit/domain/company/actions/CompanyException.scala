package com.qrsof.empresalit.domain.company.actions

import com.qrsof.core.app.errors.AppError
import com.qrsof.core.app.exceptions.ApplicationException
import com.qrsof.empresalit.domain.company.actions.CompanyErrorCodes.*

sealed abstract class CompanyException(override val appError: AppError) extends ApplicationException(appError)

case class UnknowError(details: String) extends CompanyException(UnknowErrorCode(details))

object CompanyErrorCodes {
  case class UnknowErrorCode(details: String) extends AppError {
    override val code: String = "EC01"
    override val error: Option[String] = Some("Unexpected error")
    override val detail: Option[String] = Some(details)
  }
}
