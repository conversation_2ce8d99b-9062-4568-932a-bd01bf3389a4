package com.qrsof.empresalit.domain.sendemail



class SendEmailUtilsImpl extends SendEmailUtils{

  override def buildMessage(message: String, host: Option[String]): String = {
    buildMessageHeader(host).concat(message).concat(buildMessageFooter())
  }

  private def buildMessageHeader(host: Option[String]): String = {
    s"<h1 style=\"text-align: center; padding: 13px;height: 40px; margin-bottom: 40px;\"><img style=\" height: 40px;\"> </h1> "
  }

  private def buildMessageFooter(): String = {
    s"<footer style=\"text-align: center; background: #ffca2e; padding: 13px;\"><p style=\"margin-top: 0;\">Este es un correo electrónico generado automáticamente. Por favor no respondas.</p>Desarrollado por Empresalit</footer> "

  }
}
