package com.qrsof.empresalit.domain.logbook

import com.google.inject.{Inject, Singleton}
import com.qrsof.empresalit.domain.logbook.models.{LogbookRequest}
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.pojos.*
import org.slf4j.{Logger, LoggerFactory}
import play.api.libs.json.{JsValue, Json}

@Singleton
class LogbookServiceImpl @Inject() (logbookGateway: LogbookGateway, empresalitUtils: EmpresalitUtils) extends LogbookService {
  val logger: Logger = LoggerFactory.getLogger(classOf[LogbookServiceImpl])
  override def createNewLogbook(companyKey: String, newLogbook: LogbookRequest): Unit = {
    val keyAttachmentLogbook = newLogbook.taskAttachments.map(_.reference.get)
    val keyAttachmentsJson: JsValue = Json.toJson(keyAttachmentLogbook)
    val keyAttachmentString: String = Json.stringify(keyAttachmentsJson)

    val logbookWithAttachments: NewLogbookDataRequest = NewLogbookDataRequest(
      logbookKey = empresalitUtils.generateKey(),
      author = newLogbook.author,
      taskKey = newLogbook.taskKey,
      logbookType = newLogbook.logbookType,
      payload = newLogbook.payload,
      keyAttachments = keyAttachmentString,
      createdAt = empresalitUtils.getDate,
      updateAt = empresalitUtils.getDate
    )

    logbookGateway.createNewLogbookForTask(logbookWithAttachments)

  }

  override def createLogbookForNewInputs(newLogbooks: Seq[LogbookRequest]): Unit = {

    val logbooksDataRequest = newLogbooks.map(newLogbook => {
      val keyAttachmentLogbook = newLogbook.taskAttachments.map(_.reference.get)
      val keyAttachmentsJson: JsValue = Json.toJson(keyAttachmentLogbook)
      val keyAttachmentString: String = Json.stringify(keyAttachmentsJson)

      NewLogbookDataRequest(
        logbookKey = empresalitUtils.generateKey(),
        author = newLogbook.author,
        taskKey = newLogbook.taskKey,
        logbookType = newLogbook.logbookType,
        payload = newLogbook.payload,
        createdAt = empresalitUtils.getDate,
        updateAt = empresalitUtils.getDate,
        keyAttachments = keyAttachmentString
      )
    })
    logbookGateway.createLogbookForNewInputs(logbooksDataRequest)
  }

//  private def scalaObjectToJsonString(data: Seq[AnyVal]): String = {
//    val dataJson = Json.toJson(data)
//    val dataString = Json.stringify(dataJson)
//    dataString
//  }
}
