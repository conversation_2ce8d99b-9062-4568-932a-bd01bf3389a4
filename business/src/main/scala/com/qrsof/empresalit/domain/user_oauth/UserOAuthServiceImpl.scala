package com.qrsof.empresalit.domain.user_oauth

import com.qrsof.empresalit.domain.users.UsersGateway
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import com.qrsof.empresalit.services.email.EmailService
import com.qrsof.jwt.validation.JwtValidationService
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Logger, LoggerFactory}

@Singleton
class UserOAuthServiceImpl @Inject() (usersGateway: UsersGateway, empresalitUtils: EmpresalitUtils, emailService: EmailService, jwtValidationService: JwtValidationService) extends UserOAuthService {

  val logger: Logger = LoggerFactory.getLogger(classOf[UserOAuthServiceImpl])

}
