package com.qrsof.empresalit.services.email

import com.qrsof.apptack.client.ApptackClientError
import com.qrsof.apptack.client.apps.email.EmailStatus
import com.qrsof.empresalit.controllers.oauth.RegisterActionRequest
import jakarta.inject.{Inject, Singleton}

@Singleton
class EmailServiceImpl @Inject() () extends EmailService {
  override def userRegister(registerActionRequest: RegisterActionRequest): Either[ApptackClientError, EmailStatus] = ???
//  {
//    client.email.sendEmailBySMTP(
//      NewEmailForm(
//        to = Seq("f0a46626-3743-403d-a4ec-2d99513aea28"),
//        subject = "Verificación de correo",
//        body = Some("Body"),
//        templateKey = Some("Template Key"),
//        templateVariables = Some(Seq.empty)
//      )
//    )
//  }
}
