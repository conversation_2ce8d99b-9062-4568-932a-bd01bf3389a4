package com.qrsof.empresalit.services.email

import play.api.libs.json.Json

import java.time.Clock
import java.time.temporal.ChronoUnit
import pdi.jwt.{JwtAlgorithm, JwtJson}

class EmailTokenGenerator {
  private val secretKey = "i8dJ2NGr_iA8IBrrqS1EFLhaTG9TCm7h8PlCk1qhJD4"

  def generateToken(email: String): String = {
    val expirationTime = Clock.systemUTC().instant().plus(24, ChronoUnit.HOURS) // 24 horas en horas

    val claims = Json.obj(
      "email" -> email,
      "exp" -> expirationTime.getEpochSecond
    )

    JwtJson.encode(claims, secretKey, JwtAlgorithm.HS256)
  }

  def verifyToken(token: String): Option[String] = {
    JwtJson.decodeJson(token, secretKey, Seq(JwtAlgorithm.HS256))
      .toOption
      .flatMap { json =>
        (json \ "email").asOpt[String]
      }
  }
}
