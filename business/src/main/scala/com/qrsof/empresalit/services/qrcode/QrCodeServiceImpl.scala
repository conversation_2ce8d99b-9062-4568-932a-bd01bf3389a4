package com.qrsof.empresalit.services.qrcode

import com.google.zxing.BarcodeFormat
import com.google.zxing.client.j2se.MatrixToImageWriter
import com.google.zxing.qrcode.QRCodeWriter

import java.io.{ByteArrayInputStream, ByteArrayOutputStream, InputStream};

class QrCodeServiceImpl extends QrCodeService {
  def generatePngImage(content: String, width: Int, height: Int): InputStream = {
    val barcodeWriter = new QRCodeWriter
    val bitMatrix = barcodeWriter.encode(content, BarcodeFormat.QR_CODE, width, height);
    val stream = new ByteArrayOutputStream()
    MatrixToImageWriter.writeToStream(bitMatrix, "PNG", stream)
    new ByteArrayInputStream(stream.toByteArray)
  }
}
