package com.qrsof.empresalit.controllers.oauth

import com.qrsof.apptack.client.apps.auth.users.RegisterUserWithPasswordRequest
import com.qrsof.apptack.client.apps.email.{EmailStatus, NewEmailForm}
import com.qrsof.apptack.client.apps.users.UsersOpts
import com.qrsof.apptack.client.{ApplicationCredentialsAuthentication, ApptackClientConfigurations, ApptackClientError, ApptackClientFactory}
import com.qrsof.empresalit.AppConfigurations
import com.qrsof.empresalit.controllers.oauth.OauthExceptionObj.{OauthUserNotFoundException, UnknownException}
import com.qrsof.empresalit.domain.users.UsersGateway
import com.qrsof.empresalit.services.email.EmailService
import com.qrsof.jwt.models.{JwtAccessToken, JwtToken}
import com.qrsof.jwt.validation.JwtValidationService
import jakarta.inject.{Inject, Singleton}
import org.slf4j.{Lo<PERSON>, LoggerFactory}

@Singleton
class OauthActionsImpl @Inject() (
    appConfigurations: AppConfigurations,
    jwtValidationService: JwtValidationService,
    apptackClientFactory: ApptackClientFactory,
    apptackClientConfigurations: ApptackClientConfigurations,
    usersOpts: UsersOpts,
    emailService: EmailService,
    usersGateway: UsersGateway
)(implicit applicationCredentialsAuthentication: ApplicationCredentialsAuthentication)
    extends OauthActions {

  val logger: Logger = LoggerFactory.getLogger(classOf[OauthActionsImpl])
  val appKey: String = appConfigurations.apptackKey
  override def login(loginActionRequest: LoginActionRequest): Either[OauthException, JwtToken] = {
    apptackClientFactory
      .getInstance()
      .app(appKey)
      .auth
      .users
      .loginWithPassword(username = loginActionRequest.username, password = loginActionRequest.password) match {
      case Left(_) =>
        Left(OauthUserNotFoundException(loginActionRequest.username))
      case Right(jwtAccessToken) =>
        Right(JwtToken(jwtAccessToken.accessToken))
    }
  }

  override def sendEmail(registerActionRequest: RegisterActionRequest): Either[ApptackClientError, EmailStatus] = {
    apptackClientFactory
      .getInstance()
      .app(appKey)
      .email
      .sendEmailBySMTP(appKey, NewEmailForm(to = Seq("anyone"), subject = "Test", body = Some("Test"), templateKey = None, templateVariables = None)) match {
      case Left(apptackException) =>
        Left(ApptackClientError(apptackException))
      case Right(emailStatus: EmailStatus) => Right(emailStatus)
    }
  }

  override def register(registerActionRequest: RegisterActionRequest): Either[OauthException, JwtToken] = {
    apptackClientFactory
      .getInstance()
      .app(appKey)
      .auth
      .users
      .registerUserWithPassword(
        RegisterUserWithPasswordRequest(
          username = registerActionRequest.username,
          password = registerActionRequest.password,
          roleKey = None,
          metadata = None
        )
      ) match {
      case Left(_) =>
        Left(UnknownException(registerActionRequest.username))
      case Right(jwtAccessToken) =>
        Right(JwtToken(jwtAccessToken.accessToken))
    }
  }

  override def getUserDataAction(key: String): Either[OauthException, Any] = {
    usersGateway.getUserByKey(key) match {
      case Some(user) => Right(user)
      case None       => Left(OauthUserNotFoundException(key))
    }
  }
}
