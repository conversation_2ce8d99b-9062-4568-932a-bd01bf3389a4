package com.qrsof.empresalit.gateways.appmanager

import com.qrsof.core.http.{ClientHttpFactory, HttpResponse}
import org.slf4j.LoggerFactory
import play.api.libs.json.{Json, OWrites, Reads}

import javax.inject.{Inject, Singleton}

@Singleton
class AppManagerImpl @Inject() (
    /*secretKey: String,
																													appKey: String,*/
    httpFactory: ClientHttpFactory,
    isProduction: Boolean,
    url: Option[String] = None
) extends AppManager {

  implicit val originInfoReads: Reads[OriginInfo] = Json.reads[OriginInfo]
  implicit val userDecodeReads: Reads[AppUser] = Json.reads[AppUser]
  implicit val accountManagerReads: Reads[AccountManager] = Json.reads[AccountManager]
  implicit val originInfoWrites: OWrites[OriginInfo] = Json.writes[OriginInfo]
  implicit val accountFormWrites: OWrites[AccountForm] = J<PERSON>.writes[AccountForm]
  implicit val userRegisterFormWrites: OWrites[AppUserRegistration] = Json.writes[AppUserRegistration]
  implicit val userLoginFormWrites: OWrites[AppUserSignIn] = Json.writes[AppUserSignIn]
  implicit val passwordChangeWrites: OWrites[PasswordChange] = Json.writes[PasswordChange]
  implicit val pushNotificationInfoWrites: OWrites[PushNotificationInfo] = Json.writes[PushNotificationInfo]
  implicit val webHooMessageWrites: OWrites[WebHookMessage] = Json.writes[WebHookMessage]

  private val productionUrl = "https://api.appmanager.mx"
  private val sandboxUrl = "https://sandbox.api.appmanager.mx"

  s"${getBaseUrl}/qrapimanager/user"
  private val logger = LoggerFactory.getLogger(classOf[AppManagerImpl])
  
  

  s"${getBaseUrl}/qrapimanager/api/notifications/push/account" // temporal

  private def getBaseUrl: String = {
    url.getOrElse {
      if (isProduction)
        productionUrl
      else
        sandboxUrl
    }
  }

  def getAppUser(token: String): AppUser = {
    val response = getResponseFromUserDecode(token)
    if (response.status.intValue() == 200) {
      logger.debug(response.toString)
      Json.fromJson[AppUser](Json.parse(response.toString)).get
    } else {
      logger.error(response.toString)
      throw ApiManagerException(response)
    }
  }

  private def getResponseFromUserDecode(token: String): HttpResponse[String] = {
    logger.debug(s"Obteniendo información del usuario con el token: $token")
    try {
      null
      // HttpResponse(entity = HttpRequest(GET, appUsersResourceUrl).entity)
      /*Await.result(
				httpFactory
					.url(appUsersResourceUrl)
					.withHeaders(AUTHORIZATION -> s"Bearer $token")
					.get(),
				Duration.Inf
			)*/

    } catch {
      case e: Throwable =>
        logger.error("Error en la petición al manager", e)
        throw new RuntimeException(e)
    }
  }

  override def registerAppUser(userRegister: AppUserRegistration): String = {
    val response: HttpResponse[String] = getResponseFromRegister(userRegister)
    logger.debug(response.toString)
    if (response.status.intValue() == 201) {
      Json.fromJson[String](Json.parse(response.toString)).get
    } else {
      throw ApiManagerException(response)
    }
  }

  private def getResponseFromRegister(userRegister: AppUserRegistration): HttpResponse[String] = {
    val jsonFormat = Json.toJson(userRegister).toString
    logger.debug(jsonFormat)

    //    HttpResponse(entity = HttpRequest(POST, s"$appUsersResourceUrl/signup", entity = jsonFormat).entity)
    null
    /*Await.result(
			httpFactory
			 .url(s"$appUsersResourceUrl/signup")
			 .post(Some(JsonRequestBody(jsonFormat))),
			Duration.Inf
		)*/
  }

  override def signInAppUser(loginForm: AppUserSignIn): String = {
    val response: HttpResponse[String] = getResponseFromSignIn(loginForm)
    logger.debug(response.status.toString)
    if (response.status.intValue() == 200) {
      Json.fromJson[String](Json.parse(response.toString)).get
    } else {
      throw ApiManagerException(response)
    }
  }

  private def getResponseFromSignIn(loginForm: AppUserSignIn): HttpResponse[String] = {
    Json.toJson(loginForm).toString
    //    HttpResponse(entity = HttpRequest(POST, s"$appUsersResourceUrl/signin", entity = jsonFormat).entity)
    null
    /*Await.result(
			httpFactory
			 .url(s"$appUsersResourceUrl/signin")
			 .post(Some(JsonRequestBody(jsonFormat))),
			Duration.Inf
		)*/
  }

  override def logOut( /*token: String*/ ): Unit = {
    getResponseFromLogOut( /*token*/ )
  }

  private def getResponseFromLogOut( /*token: String*/ ): HttpResponse[String] = {
    //    HttpResponse(entity = HttpRequest(POST, s"$appUsersResourceUrl/logout", entity = "{}").entity)
    /*Await.result(
			httpFactory
			 .url(s"$appUsersResourceUrl/logout")
			 //        .withHeaders(AUTHORIZATION -> token) //Se quito el header de SECRET_KEY
			 .post(Some(JsonRequestBody("{}"))),
			Duration.Inf
		)*/
    null
  }

  override def changeAppUserPassword(token: String, passwords: PasswordChange): Unit = {
    Json.toJson(passwords).toString
    //    HttpResponse(entity = HttpRequest(POST, s"$appUsersResourceUrl/password", entity = jsonFormat).entity)
    /*val response: HttpResponse[String] = Await.result(
			httpFactory
				.url(s"$appUsersResourceUrl/password")
				.withHeaders(AUTHORIZATION -> token) //Se quito el header de SECRET_KEY
				.put(Some(JsonRequestBody(jsonFormat))),
			Duration.Inf
		)*/
  }

  override def getAccountsByUser(token: String): Seq[AccountManager] = {
    /*val httpResponse: HttpResponse[String] = Await.result(
			httpFactory
			 .url(s"$appUsersResourceUrl/accounts")
			 .withHeaders(AUTHORIZATION -> token)
			 .get(),
			Duration.Inf)
		//    val httpResponse = HttpResponse(entity = HttpRequest(GET, s"$appUsersResourceUrl/accounts").entity)
		logger.debug("accounts of user")
		logger.debug(httpResponse.toString)
		httpResponse.status.intValue match {
			case 200 => Json.fromJson[Seq[AccountManager]](Json.parse(httpResponse.toString)).get
			case _ => throw ApiManagerException(httpResponse)
		}*/
    Seq.empty
  }

  override def sendPushNotification(notificationInfo: PushNotificationInfo): Unit = {
    Json.toJson(notificationInfo).toString
    //    val response = HttpResponse(entity = HttpRequest(POST, s"$appUsersResourceUrl/signup", entity = jsonFormat).entity)
    /* val response: HttpResponse[String] = Await.result(
			 httpFactory
				 .url(s"$pushNotificationsResourceUrl/${notificationInfo.accountKey}")
				 .withHeaders(SECRET_KEY -> "secretKey", APP_KEY -> "appKey") //Se quito el header de AUTHORIZATION
				 .post(Some(JsonRequestBody(jsonFormat))),
			 Duration.Inf
		 )

		 logger.debug(response.status.toString)
		 logger.debug(response.toString)*/

  }

  //  override def sendWebHookMessage(webHookMessage: WebHookMessage): Unit = {
  //    val jsonFormat = Json.toJson(webHookMessage).toString
  //    Await.result(
  //      httpFactory
  //        .url(webHookUrl)
  //        .withHeaders(SECRET_KEY -> secretKey, APP_KEY -> appKey)
  //        .post(JsonBodyRequest(jsonFormat)),
  //      Duration.Inf
  //    )
  //  }

}
