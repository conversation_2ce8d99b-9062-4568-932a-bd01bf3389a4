package com.qrsof.empresalit.gateways.appmanager

trait AppManager {

  def sendPushNotification(notificationInfo: PushNotificationInfo): Unit

  def getAccountsByUser(token: String): Seq[AccountManager]

  def changeAppUserPassword(token: String, passwords: PasswordChange): Unit

  def logOut(/*token: String*/): Unit

  def signInAppUser(loginForm: AppUserSignIn): String

  def getAppUser(token: String): AppUser

  def registerAppUser(userRegister: AppUserRegistration): String

}
