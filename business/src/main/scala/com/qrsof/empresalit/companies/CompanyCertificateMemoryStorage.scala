package com.qrsof.empresalit.companies

import java.util.UUID

class CompanyCertificateMemoryStorage extends CompanyCertificateStorage {
  val sore = scala.collection.mutable.Map.empty[String, Array[Byte]]

  override def getCertificate(certificateKeyReference: String): Array[Byte] = {
    sore(certificateKeyReference)
  }

  override def saveCertificate(companyKey: String, certificate: Array[Byte]): String = {
    val key = UUID.randomUUID().toString
    sore += (key -> certificate)
    key
  }
}
