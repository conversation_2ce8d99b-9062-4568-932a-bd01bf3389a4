package com.qrsof.empresalit.companies.actions

import com.qrsof.core.certificates.CertificateUtils
import com.qrsof.empresalit.companies.{CompanyCertificate, CompanyGateway, SaveNewCompanyCertificateRequest}
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils

import java.security.cert.X509Certificate
import jakarta.inject.{Inject, Singleton}

@Singleton
class SaveNewCertificateActionImpl @Inject()(empresalitUtils: EmpresalitUtils,
                                             companyGateway: CompanyGateway,
                                             certificateUtils: CertificateUtils,
                                            ) extends SaveNewCertificateAction {
  def execute(newCompanyCertificate: SaveNewCompanyCertificateRequest): Unit = {
    println("DATOS EN EXECUTE ---------------------------------------------------")
    println(newCompanyCertificate.companyKey)
    println(newCompanyCertificate.password)
    println(newCompanyCertificate.privateKey)
    println("\n\n\n\n\n\n\n\n\n\n\n")
    try {
      val x509Certificate: X509Certificate = certificateUtils.parseCertificateX509DER(newCompanyCertificate.publicKey)
      val key = empresalitUtils.generateKey()
      companyGateway.saveCompanyCertificate(
        CompanyCertificate(
          key = key, companyKey = newCompanyCertificate.companyKey,
          noCertificado = new String(x509Certificate.getSerialNumber.toByteArray.map(_.toChar)),
          privateKey = newCompanyCertificate.privateKey,
          password = newCompanyCertificate.password,
          publicKey = newCompanyCertificate.publicKey
        )
      )
    }
    catch {
      case e: Throwable =>
        println("ERROR ----------------------------------------------------------------------")
        println(e)
    }
  }
}
