package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.invoicing.domain.invoicing.v40.impuestos.Impuestos
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.{CTipoFactor, Comprobante, ObjectFactory}

import jakarta.inject.{Inject, Singleton}

@Singleton
private[xml] class ImpuestosBuilderImpl @Inject()() extends ImpuestosBuilder {
  override def execute(factory: ObjectFactory, impuestos: Impuestos): Comprobante.Impuestos = {
    val impiuestosNode = factory.createComprobanteImpuestos()
    impuestos.totalImpuestosTrasladados.foreach(tit => impiuestosNode.setTotalImpuestosTrasladados(tit.bigDecimal))
    impuestos.totalImpuestosRetenidos.foreach(tir => impiuestosNode.setTotalImpuestosRetenidos(tir.bigDecimal))
    impuestos.traslados.foreach(traslados => {
      val trasladosNode = factory.createComprobanteImpuestosTraslados()
      traslados.foreach(traslado => {
        val trasladoNode = factory.createComprobanteImpuestosTrasladosTraslado()
        trasladoNode.setBase(traslado.base.bigDecimal)
        trasladoNode.setImpuesto(traslado.impuesto)
        traslado.importe.foreach(t => trasladoNode.setImporte(t.bigDecimal))
        trasladoNode.setTipoFactor(CTipoFactor.fromValue(traslado.tipoFactor))
        traslado.tasaOCuota.foreach(t => trasladoNode.setTasaOCuota(t.bigDecimal))
        trasladosNode.getTraslado.add(trasladoNode)
      })
      impiuestosNode.setTraslados(trasladosNode)
    })

    impuestos.retenciones.foreach(retenciones => {
      val retencionesNode = factory.createComprobanteImpuestosRetenciones()
      retenciones.foreach(retencion => {
        val retencionNode = factory.createComprobanteImpuestosRetencionesRetencion()
        retencionNode.setImporte(retencion.importe.bigDecimal)
        retencionNode.setImpuesto(retencion.impuesto)
        retencionesNode.getRetencion.add(retencionNode)
      })
      impiuestosNode.setRetenciones(retencionesNode)
    })

    impiuestosNode
  }
}
