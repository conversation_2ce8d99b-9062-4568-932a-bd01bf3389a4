package com.qrsof.empresalit.invoicing.domain.complementos.nomina

case class NominaComp(
                       version: String,
                       tipoNomina: String,
                       fechaPago: String,
                       fechaInicialPago: String,
                       fechaFinalPago: String,
                       numDiasPagados: BigDecimal,
                       totalPercepciones: Option[BigDecimal] = None,
                       totalDeducciones: Option[BigDecimal] = None,
                       totalOtrosPagos: Option[BigDecimal] = None,
                       emisor: Option[Emisor] = None,
                       receptor: ReceptorNom,
                       percepciones: Option[Percepciones] = None,
                       deducciones: Option[Deducciones] = None,
                       otrosPagos: Option[OtrosPagos] = None,
                       incapacidades: Option[Incapacidades] = None
                     )
