package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.generateinvoice.ReceptorInvoice
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.{Comprobante, ObjectFactory}

import jakarta.inject.{Inject, Singleton}

@Singleton
private[xml] class ReceptorBuilderImpl @Inject()(usoCfdiFactory: UsoCfdiFactory) extends ReceptorBuilder {
  override def execute(factory: ObjectFactory, receptor: ReceptorInvoice): Comprobante.Receptor = {
    val receptorNode = factory.createComprobanteReceptor()
    receptor.name.foreach(name => {
      receptorNode.setNombre(name)
    })
    receptorNode.setRfc(receptor.rfc)
    receptorNode.setUsoCFDI(usoCfdiFactory.getInstance(receptor.usoCfdi))
    receptorNode
  }
}
