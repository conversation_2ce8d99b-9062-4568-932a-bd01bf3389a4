package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.invoicing.v30.GenerateInvoiceRequest
import com.qrsof.empresalit.invoicing.domain.invoicing.{IssuerInvoicingData, v40, xml}
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.Comprobante
import com.qrsof.empresalit.invoicing.sat.models.cfdi40

import jakarta.inject.{Inject, Singleton}

@Singleton
private[xml] class InvoiceXmlGeneratorImpl @Inject()(signOriginalChain: SignOriginalChain,
                                                     originalChainBuilder: OriginalChainBuilder,
                                                     originalChainBuilderV40: xml.v40.OriginalChainBuilder,
                                                     comprobanteBinder: ComprobanteBinder,
                                                     comprobanteBinderV40: xml.v40.ComprobanteBinder,
                                                     xmlSerializerImpl: XmlSerializer,
                                                     xmlSerializerImplV40: xml.v40.XmlSerializer,
                                                     fixNameSpacesXml: FixNameSpacesXml,
                                                     fixNameSpacesXmlV40: xml.v40.FixNameSpacesXml,
                                                    ) extends InvoiceXmlGenerator {


  def fixNameSpaces(xmlComprobante: Array[Byte], generateInvoiceRequest: GenerateInvoiceRequest): Array[Byte] = {
    fixNameSpacesXml.execute(xmlComprobante, generateInvoiceRequest)
  }

  override def getXml(issuerInvoicingData: IssuerInvoicingData, generateInvoiceRequest: GenerateInvoiceRequest): Array[Byte] = {
    val comprobante: Comprobante = comprobanteBinder.execute(issuerInvoicingData, generateInvoiceRequest)
    val originalChain: Array[Byte] = getOriginalChain(comprobante)
    val sello = buildSello(originalChain, issuerInvoicingData)
    comprobante.setSello(sello)
    val xmlComprobante = xmlSerializerImpl.execute(comprobante)
    fixNameSpaces(xmlComprobante, generateInvoiceRequest);
  }

  private def buildSello(originalChain: Array[Byte], issuerInvoicingData: IssuerInvoicingData): String = {
    signOriginalChain.execute(originalChain, issuerInvoicingData.privateKey)
  }

  private def getOriginalChain(comprobante: Comprobante): Array[Byte] = {
    originalChainBuilder.execute(comprobante)
  }

  private def getOriginalChain(comprobante: cfdi40.Comprobante): Array[Byte] = {
    originalChainBuilderV40.execute(comprobante)
  }

  override def getXml(issuerInvoicingData: IssuerInvoicingData, generateInvoiceRequest: v40.GenerateInvoiceRequest): Array[Byte] = {
    val comprobante: cfdi40.Comprobante = comprobanteBinderV40.execute(issuerInvoicingData, generateInvoiceRequest)
    val originalChain: Array[Byte] = getOriginalChain(comprobante)
    val sello = buildSello(originalChain, issuerInvoicingData)
    comprobante.setSello(sello)
    val xmlComprobante = xmlSerializerImplV40.execute(comprobante)
    fixNameSpacesXmlV40.execute(xmlComprobante, generateInvoiceRequest);
  }
}
