package com.qrsof.empresalit.invoicing.domain.complementos.pagos20

case class Totales(
                    totalRetencionesIVA: Option[BigDecimal],
                    totalRetencionesISR: Option[BigDecimal],
                    totalRetencionesIEPS: Option[BigDecimal],
                    totalTrasladosBaseIVA16: Option[BigDecimal],
                    totalTrasladosImpuestoIVA16: Option[BigDecimal],
                    totalTrasladosBaseIVA8: Option[BigDecimal],
                    totalTrasladosImpuestoIVA8: Option[BigDecimal],
                    totalTrasladosBaseIVA0: Option[BigDecimal],
                    totalTrasladosImpuestoIVA0: Option[BigDecimal],
                    totalTrasladosBaseIVAExento: Option[BigDecimal],
                    montoTotalPagos: Option[BigDecimal],
                  )
