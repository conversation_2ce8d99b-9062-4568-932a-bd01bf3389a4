package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.invoicing.v30.GenerateInvoiceRequest
import com.qrsof.empresalit.invoicing.domain.invoicing.{IssuerInvoicingData, v40}

trait InvoiceXmlGenerator {
  def getXml(issuerInvoicingData: IssuerInvoicingData, generateInvoiceRequest: GenerateInvoiceRequest): Array[Byte]

  def getXml(issuerInvoicingData: IssuerInvoicingData, generateInvoiceRequest: v40.GenerateInvoiceRequest): Array[Byte]
}
