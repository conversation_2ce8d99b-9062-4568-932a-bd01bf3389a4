package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.impuestos.Impuestos
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.{CTipoFactor, Comprobante, ObjectFactory}

import jakarta.inject.{Inject, Singleton}

@Singleton
private[xml] class ImpuestosBuilderImpl @Inject()() extends ImpuestosBuilder {
  override def execute(factory: ObjectFactory, impuestos: Impuestos): Comprobante.Impuestos = {
    val impiuestosNode = factory.createComprobanteImpuestos()
    impuestos.totalImpuestosTrasladados.foreach(tit => impiuestosNode.setTotalImpuestosTrasladados(tit.bigDecimal))
    impuestos.totalImpuestosRetenidos.foreach(tir => impiuestosNode.setTotalImpuestosRetenidos(tir.bigDecimal))
    impuestos.traslados.foreach(traslados => {
      val trasladosNode = factory.createComprobanteImpuestosTraslados()
      traslados.foreach(traslado => {
        val trasladoNode = factory.createComprobanteImpuestosTrasladosTraslado()
        trasladoNode.setImporte(traslado.importe.bigDecimal)
        trasladoNode.setImpuesto(traslado.impuesto)
        trasladoNode.setTasaOCuota(traslado.tasaOCuota.bigDecimal)
        trasladoNode.setTipoFactor(CTipoFactor.fromValue(traslado.tipoFactor))
        trasladosNode.getTraslado.add(trasladoNode)
      })
      impiuestosNode.setTraslados(trasladosNode)
    })

    impuestos.retenciones.foreach(retenciones => {
      val retencionesNode = factory.createComprobanteImpuestosRetenciones()
      retenciones.foreach(retencion => {
        val retencionNode = factory.createComprobanteImpuestosRetencionesRetencion()
        retencionNode.setImporte(retencion.importe.bigDecimal)
        retencionNode.setImpuesto(retencion.impuesto)
        retencionesNode.getRetencion.add(retencionNode)
      })
      impiuestosNode.setRetenciones(retencionesNode)
    })

    impiuestosNode
  }
}
