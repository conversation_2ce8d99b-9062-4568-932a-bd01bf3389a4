package com.qrsof.empresalit.invoicing.domain.invoicing.v40

import com.qrsof.empresalit.invoicing.domain.complementos.Complementos
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.conceptos.Concepto
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.impuestos.Impuestos

case class GenerateInvoiceRequest(
                                   companyKey: String,
                                   serie: Option[String] = None,
                                   folio: Option[String] = None,
                                   fecha: Option[String] = None,
                                   formaPago: Option[String] = None,
                                   condicionesDePago: Option[String] = None,
                                   subTotal: BigDecimal,
                                   descuento: Option[BigDecimal] = None,
                                   moneda: String,
                                   tipoCambio: Option[BigDecimal] = None,
                                   total: BigDecimal,
                                   tipoComprobante: String,
                                   exportacion: String,
                                   metodoPago: Option[String] = None,
                                   lugarExpedicion: String,
                                   confirmacion: Option[String] = None,

                                   informacionGlobal: Option[InformacionGlobal] = None,
                                   cfdiRelacionados: Option[Seq[CfdiRelacionados]] = None,

                                   receptor: Option[ReceptorInvoice],
                                   conceptos: Seq[Concepto],
                                   impuestos: Option[Impuestos] = None,
                                   complementos: Option[Complementos] = None
                                 ) {

}
