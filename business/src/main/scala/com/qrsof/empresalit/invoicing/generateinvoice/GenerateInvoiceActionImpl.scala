package com.qrsof.empresalit.invoicing.generateinvoice

import com.qrsof.empresalit.invoicing.domain.invoicing.v30.GenerateInvoiceRequest
import jakarta.inject.{Inject, Singleton}
import org.slf4j.LoggerFactory

@Singleton
class GenerateInvoiceActionImpl @Inject() (
//                                           generateInvoiceProxy: GenerateInvoiceProxy,
//                                           xmlSatGeneratorGatewayFactory: PacGatewayFactory,
) extends GenerateInvoiceAction {

  LoggerFactory.getLogger(classOf[GenerateInvoiceActionImpl])

  override def execute(generateInvoiceRequest: GenerateInvoiceRequest): Array[Byte] = {
    try {
//      val issuerInvoicingData: IssuerInvoicingData = generateInvoiceProxy.getIssuerInvoicingDataByCompanyKey(generateInvoiceRequest.companyKey)
//      val xmlSatGenerator = xmlSatGeneratorGatewayFactory.getInstance()
//      xmlSatGenerator.stampInvoice(issuerInvoicingData, generateInvoiceRequest)
      null
    } catch {
      case e: Exception =>
        throw e;
    }
  }

}
