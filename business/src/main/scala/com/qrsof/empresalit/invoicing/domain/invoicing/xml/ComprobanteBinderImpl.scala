package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.conceptos.Concepto
import com.qrsof.empresalit.invoicing.domain.impuestos.Impuestos
import com.qrsof.empresalit.invoicing.domain.invoicing.IssuerInvoicingData
import com.qrsof.empresalit.invoicing.domain.invoicing.v30.GenerateInvoiceRequest
import com.qrsof.empresalit.invoicing.generateinvoice.ReceptorInvoice
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.*
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.Comprobante.Emisor
import org.slf4j.LoggerFactory

import java.util.Base64
import jakarta.inject.{Inject, Singleton}

@Singleton
private[xml] class ComprobanteBinderImpl @Inject()(
                                                    emisorBuilder: EmisorBuilder,
                                                    receptorBuilder: ReceptorBuilder,
                                                    conceptosBuilder: ConceptosBuilder,
                                                    xmlCurrentTime: XmlTimeUtils,
                                                    monedaFactory: MonedaFactory,
                                                    tipoComprobanteFactory: TipoComprobanteFactory,
                                                    metodoPagoFactory: MetodoPagoFactory,
                                                    impuestosBuilder: ImpuestosBuilder,
                                                    complementosBuilder: ComplementosBuilder,
                                                  ) extends ComprobanteBinder {

  private val logger = LoggerFactory.getLogger(classOf[ComprobanteBinderImpl])


  override def execute(issuerInvoicingData: IssuerInvoicingData, generateInvoiceRequest: GenerateInvoiceRequest): Comprobante = {
    logger.debug("Xml generation: {} {}", issuerInvoicingData, generateInvoiceRequest)
    val factory = new ObjectFactory
    val comprobante = factory.createComprobante()
    comprobante.setVersion("3.3")
    generateInvoiceRequest.folio.foreach(comprobante.setFolio)
    generateInvoiceRequest.serie.foreach(comprobante.setSerie)
    comprobante.setFecha(xmlCurrentTime.getCurrentDate())
    generateInvoiceRequest.formaPago.foreach(fp => comprobante.setFormaPago(fp))
    comprobante.setNoCertificado(issuerInvoicingData.certificateNumber)
    comprobante.setCertificado(Base64.getEncoder.encodeToString(issuerInvoicingData.publicKey))
    generateInvoiceRequest.condicionesDePago.foreach(cp => comprobante.setCondicionesDePago(cp))
    comprobante.setSubTotal(generateInvoiceRequest.subTotal.bigDecimal)
    generateInvoiceRequest.descuento.foreach(des => comprobante.setDescuento(des.bigDecimal))
    comprobante.setMoneda(monedaFactory.getInstace(generateInvoiceRequest.moneda))
    generateInvoiceRequest.tipoCambio.foreach(tc => comprobante.setTipoCambio(tc.bigDecimal))
    comprobante.setTotal(generateInvoiceRequest.total.bigDecimal)
    comprobante.setTipoDeComprobante(tipoComprobanteFactory.getInstance(generateInvoiceRequest.tipoComprobante))
    generateInvoiceRequest.metodoPago.foreach(mp => comprobante.setMetodoPago(metodoPagoFactory.getInstance(mp)))
    comprobante.setLugarExpedicion(generateInvoiceRequest.lugarExpedicion)
    generateInvoiceRequest.confirmacion.foreach(comprobante.setConfirmacion)

    comprobante.setEmisor(buildEmisor(factory, issuerInvoicingData))
    comprobante.setReceptor(buildReceptor(factory, generateInvoiceRequest.receptor.get))
    comprobante.setConceptos(buildConceptos(factory, generateInvoiceRequest.conceptos))
    generateInvoiceRequest.impuestos.foreach(impuestos => {
      comprobante.setImpuestos(buildImpuestos(factory, impuestos))
    })
    generateInvoiceRequest.complementos.foreach(c => {
      comprobante.getComplemento.add(complementosBuilder.execute(factory, c))
    })

    comprobante
  }

  private def buildImpuestos(factory: ObjectFactory, impuestos: Impuestos): Comprobante.Impuestos = {
    impuestosBuilder.execute(factory, impuestos)
  }

  private def buildConceptos(factory: ObjectFactory, conceptos: Seq[Concepto]): Comprobante.Conceptos = {
    conceptosBuilder.execute(factory, conceptos)
  }

  private def buildReceptor(factory: ObjectFactory, receptor: ReceptorInvoice): Comprobante.Receptor = {
    receptorBuilder.execute(factory, receptor)
  }

  private def buildEmisor(factory: ObjectFactory, issuerInvoicingData: IssuerInvoicingData): Emisor = {
    emisorBuilder.execute(factory, issuerInvoicingData)
  }
}
