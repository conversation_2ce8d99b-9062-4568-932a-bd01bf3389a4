package com.qrsof.empresalit.invoicing.domain.complementos.carta.porte

import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.mercancias.*

case class Mercancias(
                       pesoBrutoTotal: BigDecimal,
                       unidadPeso: String,
                       pesoNetoTotal: Option[BigDecimal],
                       numTotalMercancias: Int,
                       cargoPorTasacion: Option[BigDecimal],
                       mercancia: Seq[Mercancia] = Nil,
                       autotransporte: Option[Autotransporte] = None,
                       transporteMaritimo: Option[TransporteMaritimo] = None,
                       transporteAereo: Option[TransporteAereo] = None,
                       transporteFerroviario: Option[TransporteFerroviario] = None
                     )
