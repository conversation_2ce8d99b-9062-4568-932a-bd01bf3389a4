package com.qrsof.empresalit.invoicing.domain.complementos.carta.porte

case class CartaPorte(
                       version: String = "2.0",
                       transpInternac: String,
                       entradaSalidaMerc: Option[String] = None,
                       paisOrigenDestino: Option[String] = None,
                       viaEntradaSalida: Option[String] = None,
                       totalDistRec: Option[BigDecimal] = None,
                       ubicaciones: Ubicaciones,
                       mercancias: Mercancias,
                       figuratransporte: Option[Figuratransporte] = None,
                     )
