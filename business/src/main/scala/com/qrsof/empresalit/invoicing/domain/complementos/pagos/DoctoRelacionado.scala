package com.qrsof.empresalit.invoicing.domain.complementos.pagos

case class DoctoRelacionado(
                             idDocumento: String,
                             serie: Option[String] = None,
                             folio: Option[String] = None,
                             monedaDR: String,
                             tipoCambioDR: Option[BigDecimal] = None,
                             metodoDePagoDR: String,
                             numParcialidad: Option[Int] = None,
                             impSaldoAnt: Option[BigDecimal] = None,
                             impPagado: Option[BigDecimal] = None,
                             impSaldoInsoluto: Option[BigDecimal] = None,
                           )
