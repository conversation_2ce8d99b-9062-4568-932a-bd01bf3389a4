package com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.mercancias

case class TransporteAereo(
                            permSCT: String,
                            numPermisoSCT: String,
                            matriculaAeronave: Option[String],
                            nombreAseg: Option[String],
                            numPolizaSeguro: Option[String],
                            numeroGuia: String,
                            lugarContrato: Option[String],
                            codigoTransportista: String,
                            rfcEmbarcador: Option[String],
                            numRegIdTribEmbarc: Option[String],
                            residenciaFiscalEmbarc: Option[String],
                            nombreEmbarcador: Option[String]
                          )
