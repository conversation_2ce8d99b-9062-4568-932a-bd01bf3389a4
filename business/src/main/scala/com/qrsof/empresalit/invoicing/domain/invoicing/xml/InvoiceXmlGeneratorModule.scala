package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.google.inject.AbstractModule
import net.codingwell.scalaguice.ScalaModule

class InvoiceXmlGeneratorModule extends AbstractModule with ScalaModule {

  override def configure() = {
    bind[ComprobanteBinder].to[ComprobanteBinderImpl]
    bind[ConceptosBuilder].to[ConceptosBuilderImpl]
    bind[EmisorBuilder].to[EmisorBuilderImpl]
    bind[ComplementosBuilder].to[ComplementosBuilderImpl]
    bind[ImpuestosBuilder].to[ImpuestosBuilderImpl]
    bind[MetodoPagoFactory].to[MetodoPagoFactoryImpl]
    bind[MonedaFactory].to[MonedaFactoryImpl]
    bind[ReceptorBuilder].to[ReceptorBuilderImpl]
    bind[TipoComprobanteFactory].to[TipoComprobanteFactoryImpl]
    bind[XmlTimeUtils].to[XmlTimeUtilsImpl]
    bind[UsoCfdiFactory].to[UsoCfdiFactoryImpl]
    bind[InvoiceXmlGenerator].to[InvoiceXmlGeneratorImpl]
    bind[OriginalChainBuilder].to[OriginalChainBuilderImpl]
    bind[SignOriginalChain].to[SignOriginalChainImpl]
    bind[XmlSerializer].to[XmlSerializerImpl]
    bind[FixNameSpacesXml].to[FixNameSpacesXmlImpl]
    bind[v40.ComprobanteBinder].to[v40.ComprobanteBinderImpl]
    bind[v40.FixNameSpacesXml].to[v40.FixNameSpacesXmlImpl]
    bind[v40.OriginalChainBuilder].to[v40.OriginalChainBuilderImpl]
    bind[v40.XmlSerializer].to[v40.XmlSerializerImpl]
    bind[v40.ComplementosBuilder].to[v40.ComplementosBuilderImpl]
    bind[v40.ConceptosBuilder].to[v40.ConceptosBuilderImpl]
    bind[v40.EmisorBuilder].to[v40.EmisorBuilderImpl]
    bind[v40.ImpuestosBuilder].to[v40.ImpuestosBuilderImpl]
    bind[v40.MetodoPagoFactory].to[v40.MetodoPagoFactoryImpl]
    bind[v40.MonedaFactory].to[v40.MonedaFactoryImpl]
    bind[v40.ReceptorBuilder].to[v40.ReceptorBuilderImpl]
    bind[v40.TipoComprobanteFactory].to[v40.TipoComprobanteFactoryImpl]
    bind[v40.UsoCfdiFactory].to[v40.UsoCfdiFactoryImpl]
  }


}
