package com.qrsof.empresalit.invoicing.domain.complementos.nomina

case class Percepciones(
                         totalSueldos: Option[BigDecimal] = None,
                         totalSeparacionIndemnizacion: Option[BigDecimal] = None,
                         totalJubilacionPensionRetiro: Option[BigDecimal] = None,
                         totalGravado: BigDecimal,
                         totalExento: BigDecimal,
                         percepcion: Seq[Percepcion] = Nil,
                         jubilacionPensionRetiro: Option[JubilacionPensionRetiro] = None,
                         separacionIndemnizacion: Option[SeparacionIndemnizacion] = None
                       )
