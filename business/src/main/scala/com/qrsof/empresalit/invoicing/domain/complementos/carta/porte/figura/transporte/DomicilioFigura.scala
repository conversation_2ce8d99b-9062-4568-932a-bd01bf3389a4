package com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.figura.transporte

case class DomicilioFigura(
                            calleFigura: Option[String] = None,
                            numeroExteriorFigura: Option[String] = None,
                            numeroInteriorFigura: Option[String] = None,
                            coloniaFigura: Option[String] = None,
                            localidadFigura: Option[String] = None,
                            referenciaFigura: Option[String] = None,
                            municipioFigura: Option[String] = None,
                            estadoFigura: String,
                            paisFigura: String,
                            codigoPostalFigura: String
                          )
