package com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.figura.transporte

case class TiposFigura(
                        tipoFigura: String,
                        rfcFigura: Option[String] = None,
                        numLicencia: Option[String] = None,
                        nombreFigura: Option[String] = None,
                        numRegIdTribFigura: Option[String] = None,
                        residenciaFiscalFigura: Option[String] = None,
                        partesTransporte: Seq[PartesTransporte] = Nil,
                        domicilio: Option[DomicilioFigura] = None
                      )
