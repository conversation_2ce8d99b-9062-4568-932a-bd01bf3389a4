package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.invoicing.domain.invoicing.v40.GenerateInvoiceRequest
import com.ximpleware.{AutoPilot, VTDGen, XMLModifier}
import org.slf4j.LoggerFactory

import java.io.ByteArrayOutputStream
import jakarta.inject.{Inject, Singleton}

@Singleton
class FixNameSpacesXmlImpl @Inject()() extends FixNameSpacesXml {

  private val log = LoggerFactory.getLogger(classOf[FixNameSpacesXmlImpl])

  def execute(xml: Array[Byte], generateInvoiceRequest: GenerateInvoiceRequest): Array[Byte] = {
    val nameSpacesMap = scala.collection.mutable.Map[String, String]("http://www.sat.gob.mx/cfd/4" -> "http://www.sat.gob.mx/sitio_internet/cfd/4/cfdv40.xsd")
    val vg = new VTDGen
    vg.setDoc(xml)
    vg.parse(false)
    val vn = vg.getNav
    val ap = new AutoPilot(vn)
    val xm = new XMLModifier(vn)
    ap.selectXPath("//@schemaLocation")
    val i = ap.evalXPath()
    generateInvoiceRequest.complementos.foreach(complementos => {
      complementos.recepcionPagos20.foreach(_ => {
        nameSpacesMap.+=("http://www.sat.gob.mx/Pagos20" -> "http://www.sat.gob.mx/sitio_internet/cfd/Pagos/Pagos20.xsd")
      })
      complementos.cartaPorte20.foreach(_ => {
        nameSpacesMap.+=("http://www.sat.gob.mx/CartaPorte20" -> "http://www.sat.gob.mx/sitio_internet/cfd/CartaPorte/CartaPorte20.xsd")
      })

      complementos.nomina12.foreach(_ => {
        nameSpacesMap.+=("http://www.sat.gob.mx/nomina12" -> "http://www.sat.gob.mx/sitio_internet/cfd/nomina/nomina12.xsd")
      })
    })
    val schemaLocations = nameSpacesMap.toSeq.map((el) => {
      s"${el._1} ${el._2}"
    })
    xm.updateToken(i + 1, schemaLocations.mkString(" "))
    val outputStream = new ByteArrayOutputStream()
    xm.output(outputStream)
    val result = outputStream.toByteArray
    log.debug("Fixed name space: {}", new String(result))
    result
  }

}
