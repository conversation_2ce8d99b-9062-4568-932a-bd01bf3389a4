package com.qrsof.empresalit.invoicing.generateinvoice.v40

import com.qrsof.empresalit.invoicing.domain.invoicing.v40
import com.qrsof.empresalit.invoicing.generateinvoice.GenerateInvoiceProxy
import jakarta.inject.{Inject, Singleton}
import org.slf4j.LoggerFactory

@Singleton
class GenerateInvoiceActionImpl @Inject() (
    generateInvoiceProxy: GenerateInvoiceProxy
) extends GenerateInvoiceAction {

  LoggerFactory.getLogger(classOf[GenerateInvoiceActionImpl])

  override def execute(generateInvoiceRequest: v40.GenerateInvoiceRequest): Array[Byte] = {
    try {
//      val issuerInvoicingData: IssuerInvoicingData = generateInvoiceProxy.getIssuerInvoicingDataByCompanyKey(generateInvoiceRequest.companyKey)
//      val xmlSatGenerator = xmlSatGeneratorGatewayFactory.getInstance()
//      xmlSatGenerator.stampInvoice(issuerInvoicingData, generateInvoiceRequest)
      null
    } catch {
      case e: Exception =>
        throw e;
    }
  }

}
