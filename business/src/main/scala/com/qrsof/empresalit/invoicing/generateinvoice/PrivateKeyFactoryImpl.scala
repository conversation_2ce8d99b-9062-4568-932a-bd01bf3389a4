package com.qrsof.empresalit.invoicing.generateinvoice

import org.bouncycastle.asn1.ASN1Sequence
import org.bouncycastle.asn1.pkcs.EncryptedPrivateKeyInfo
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.openssl.jcajce.{JcaPEMKeyConverter, JceOpenSSLPKCS8DecryptorProviderBuilder}
import org.bouncycastle.pkcs.PKCS8EncryptedPrivateKeyInfo

import java.io.{ByteArrayInputStream, InputStream}
import java.security.{PrivateKey, Security}
import jakarta.inject.{Inject, Singleton}

@Singleton
class PrivateKeyFactoryImpl @Inject()() extends PrivateKeyFactory {

  Security.addProvider(new BouncyCastleProvider()) //TODO: Revisar si se pone en el bootstrap del sistema


  override def getInstance(privateKey: Array[Byte], password: String): PrivateKey = {
    val privateKeyContent: InputStream = new ByteArrayInputStream(privateKey)
    val sequence = ASN1Sequence.getInstance(privateKeyContent.readAllBytes())
    val privateKeyInfo = new PKCS8EncryptedPrivateKeyInfo(EncryptedPrivateKeyInfo.getInstance(sequence))
    val converter = new JcaPEMKeyConverter
    val provider = new JceOpenSSLPKCS8DecryptorProviderBuilder().build(password.toCharArray)
    val keyInfo = privateKeyInfo.decryptPrivateKeyInfo(provider)
    converter.getPrivateKey(keyInfo)
  }
}
