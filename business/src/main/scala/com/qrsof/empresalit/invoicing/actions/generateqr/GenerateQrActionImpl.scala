package com.qrsof.empresalit.invoicing.actions.generateqr

import com.qrsof.empresalit.exceptions.InvalidActionRequestException
import com.qrsof.empresalit.services.qrcode.QrCodeService

import java.io.InputStream
import jakarta.inject.{Inject, Singleton}

@Singleton
class GenerateQrActionImpl @Inject()(qrCodeService: QrCodeService) extends GenerateQrAction {
  override def execute(request: GenerateQrActionRequest): InputStream = {

    if (request.rr.isEmpty && request.nr.isEmpty) {
      throw new InvalidActionRequestException("Al menos uno de los parametros rr o nr tiene que ser enviado")
    }

    val baseUrl = s"https://prodretencionverificacion.clouda.sat.gob.mx?id=${request.id}&re=${request.re}"
    val rr = request.rr.map(rr => s"&rr=${rr}")
    val nr = request.nr.map(nr => s"&nr=${nr}")
    val endString = s"&tt=${request.tt}&fe=${request.fe}"

    val finalUrl = baseUrl + rr.orElse(nr).get + endString

    qrCodeService.generatePngImage(content = finalUrl, width = 400, height = 400)
  }
}
