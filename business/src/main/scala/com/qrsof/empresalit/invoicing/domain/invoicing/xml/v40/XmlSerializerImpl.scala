package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Comprobante
import com.sun.xml.bind.marshaller.NamespacePrefixMapper
import org.slf4j.LoggerFactory

import java.io.ByteArrayOutputStream
import java.util
import jakarta.inject.{Inject, Singleton}
import javax.xml.bind.JAXBContext

@Singleton
private[xml] class XmlSerializerImpl @Inject()() extends XmlSerializer {

  private val logger = LoggerFactory.getLogger(classOf[XmlSerializer])

  override def execute(comprobante: Comprobante): Array[Byte] = {
    val jaxbContext = JAXBContext.newInstance(classOf[Comprobante])
    val marshaller = jaxbContext.createMarshaller

    //		comprobante.getComplemento.get(0)

    marshaller.setProperty("com.sun.xml.bind.namespacePrefixMapper", new DefaultNamespacePrefixMapper)
    //    marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
    import javax.xml.bind.Marshaller
    marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true)
    marshaller.setProperty(Marshaller.JAXB_SCHEMA_LOCATION, "http://www.sat.gob.mx/cfd/4 http://www.sat.gob.mx/sitio_internet/cfd/4/cfdv40.xsd")
    val cfdiOutputStream = new ByteArrayOutputStream()
    marshaller.marshal(comprobante, cfdiOutputStream)
    val cfdiXml = cfdiOutputStream.toByteArray
    logger.debug(new String(cfdiXml))
    cfdiXml
  }

  class DefaultNamespacePrefixMapper() extends NamespacePrefixMapper {

    private val namespaceMap: util.Map[String, String] = new util.HashMap[String, String]

    namespaceMap.put("http://www.sat.gob.mx/cfd/4", "cfdi")
    namespaceMap.put("http://www.w3.org/2001/XMLSchema-instance", "xsi")
    namespaceMap.put("http://www.sat.gob.mx/TimbreFiscalDigital", "tfd")
    namespaceMap.put("http://www.sat.gob.mx/Pagos20", "pago20")
    namespaceMap.put("http://www.sat.gob.mx/CartaPorte20", "cartaporte20")
    namespaceMap.put("http://www.sat.gob.mx/nomina12", "nomina12")

    override def getPreferredPrefix(namespaceUri: String, suggestion: String, requirePrefix: Boolean): String = namespaceMap.getOrDefault(namespaceUri, suggestion)
  }

}
