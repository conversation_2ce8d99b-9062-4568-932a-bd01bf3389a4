package com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.ubicaciones

case class Ubicacion(
                      tipoUbicacion: String,
                      idUbicacion: Option[String] = None,
                      rfcRemitenteDestinatario: String,
                      nombreRemitenteDestinatario: Option[String] = None,
                      numRegIdTrib: Option[String] = None,
                      residenciaFiscal: Option[String] = None,
                      numEstacion: Option[String] = None,
                      nombreEstacion: Option[String] = None,
                      navegacionTrafico: Option[String] = None,
                      fechaHoraSalidaLlegada: String,
                      tipoEstacion: Option[String] = None,
                      distanciaRecorrida: Option[BigDecimal] = None,
                      domicilio: Option[Domicilio] = None

                    )
