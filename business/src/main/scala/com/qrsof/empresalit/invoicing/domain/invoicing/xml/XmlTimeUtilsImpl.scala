package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils

import java.text.SimpleDateFormat
import java.util.GregorianCalendar
import jakarta.inject.{Inject, Singleton}
import javax.xml.datatype.{DatatypeConstants, DatatypeFactory, XMLGregorianCalendar}

@Singleton
class XmlTimeUtilsImpl @Inject()(empreslitUtils: EmpresalitUtils) extends XmlTimeUtils {
  override def getCurrentDate(): XMLGregorianCalendar = {
    val gregorianCalendar = new GregorianCalendar()
    gregorianCalendar.setTime(empreslitUtils.getDate)
    val xMLGregorianCalendar = DatatypeFactory.newInstance.newXMLGregorianCalendar(gregorianCalendar)
    xMLGregorianCalendar.setTimezone(DatatypeConstants.FIELD_UNDEFINED)
    xMLGregorianCalendar.setMillisecond(DatatypeConstants.FIELD_UNDEFINED)
    xMLGregorianCalendar
  }

  override def getDate(fechaPago: String, format: String): XMLGregorianCalendar = {
    val dateFormat = new SimpleDateFormat(format)
    val date = dateFormat.parse(fechaPago)

    val gregorianCalendar = new GregorianCalendar()
    gregorianCalendar.setTime(date)
    val xMLGregorianCalendar = DatatypeFactory.newInstance.newXMLGregorianCalendar(gregorianCalendar)
    xMLGregorianCalendar.setTimezone(DatatypeConstants.FIELD_UNDEFINED)
    xMLGregorianCalendar.setMillisecond(DatatypeConstants.FIELD_UNDEFINED)
    xMLGregorianCalendar
  }

  override def getDateWithoutTime(fechaPago: String, format: String): XMLGregorianCalendar = {
    val dateFormat = new SimpleDateFormat(format)
    val date = dateFormat.parse(fechaPago)

    val gregorianCalendar = new GregorianCalendar()
    gregorianCalendar.setTime(date)
    val xMLGregorianCalendar = DatatypeFactory.newInstance.newXMLGregorianCalendar(gregorianCalendar)
    xMLGregorianCalendar.setTimezone(DatatypeConstants.FIELD_UNDEFINED)
    xMLGregorianCalendar.setMillisecond(DatatypeConstants.FIELD_UNDEFINED)
    xMLGregorianCalendar.setTime(DatatypeConstants.FIELD_UNDEFINED,
      DatatypeConstants.FIELD_UNDEFINED,
      DatatypeConstants.FIELD_UNDEFINED,
      DatatypeConstants.FIELD_UNDEFINED);
    xMLGregorianCalendar
  }
}
