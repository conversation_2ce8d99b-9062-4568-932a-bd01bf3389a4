package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.invoicing.domain.invoicing.IssuerInvoicingData
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.{Comprobante, ObjectFactory}

import jakarta.inject.{Inject, Singleton}

@Singleton
private[xml] class EmisorBuilderImpl @Inject()() extends EmisorBuilder {
  override def execute(factory: ObjectFactory, issuerInvoicingData: IssuerInvoicingData): Comprobante.Emisor = {
    val emisor = factory.createComprobanteEmisor()
    emisor.setNombre(issuerInvoicingData.name)
    emisor.setRfc(issuerInvoicingData.rfc)
    emisor.setRegimenFiscal(issuerInvoicingData.regimenFiscalClave)
    emisor
  }
}
