package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import org.slf4j.LoggerFactory

import java.security.{PrivateKey, Signature}
import java.util.Base64
import jakarta.inject.{Inject, Singleton}

@Singleton
private[xml] class SignOriginalChainImpl @Inject()() extends SignOriginalChain {

  private val logger = LoggerFactory.getLogger(classOf[SignOriginalChainImpl])

  override def execute(orignalChain: Array[Byte], privateKey: PrivateKey): String = {
    val hashedSignature = Signature.getInstance("SHA256withRSA")
    hashedSignature.initSign(privateKey)
    hashedSignature.update(orignalChain)
    val signature = hashedSignature.sign
    val sello = Base64.getEncoder.encodeToString(signature)
    logger.debug("Sello: {}", sello)
    sello
  }
}
