package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.conceptos.Concepto
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.{CTipoFactor, Comprobante, ObjectFactory}

import jakarta.inject.{Inject, Singleton}

@Singleton
private[xml] class ConceptosBuilderImpl @Inject()() extends ConceptosBuilder {
  override def execute(factory: ObjectFactory, conceptos: Seq[Concepto]): Comprobante.Conceptos = {
    val conceptosNode = factory.createComprobanteConceptos()
    val conceptosNodes = conceptosNode.getConcepto
    conceptos.foreach(concepto => {
      val conceptoNode = factory.createComprobanteConceptosConcepto()
      conceptoNode.setCantidad(concepto.cantidad.bigDecimal)
      conceptoNode.setClaveProdServ(concepto.claveProductoServicio)
      conceptoNode.setClaveUnidad(concepto.claveUnidad)
      conceptoNode.setDescripcion(concepto.descripcion)
      conceptoNode.setValorUnitario(concepto.valorUnitario.bigDecimal)
      conceptoNode.setImporte(concepto.importe.bigDecimal)
      concepto.descuento.foreach(des => conceptoNode.setDescuento(des.bigDecimal))
      concepto.impuestos.foreach(conceptoImpuestos => {
        val conceptoImpuestoNode = factory.createComprobanteConceptosConceptoImpuestos()

        if (conceptoImpuestos.retenciones.nonEmpty) {
          val retencionesNode = factory.createComprobanteConceptosConceptoImpuestosRetenciones()
          conceptoImpuestos.retenciones.foreach(retencion => {
            val retencionNode = factory.createComprobanteConceptosConceptoImpuestosRetencionesRetencion()
            retencionNode.setBase(retencion.base.bigDecimal)
            retencionNode.setImporte(retencion.importe.bigDecimal)
            retencionNode.setImpuesto(retencion.impuesto)
            retencionNode.setTasaOCuota(retencion.tasaCuota.bigDecimal)
            retencionNode.setTipoFactor(CTipoFactor.fromValue(retencion.tipoFactor))
            retencionesNode.getRetencion.add(retencionNode)
          })
          conceptoImpuestoNode.setRetenciones(retencionesNode)
        }

        if (conceptoImpuestos.traslados.nonEmpty) {
          val trasladosNode = factory.createComprobanteConceptosConceptoImpuestosTraslados()
          conceptoImpuestos.traslados.foreach(traslado => {
            val trasladoNode = factory.createComprobanteConceptosConceptoImpuestosTrasladosTraslado()
            trasladoNode.setBase(traslado.base.bigDecimal)
            trasladoNode.setImporte(traslado.importe.bigDecimal)
            trasladoNode.setImpuesto(traslado.impuesto)
            trasladoNode.setTasaOCuota(traslado.tasaCuota.bigDecimal)
            trasladoNode.setTipoFactor(CTipoFactor.fromValue(traslado.tipoFactor))
            trasladosNode.getTraslado.add(trasladoNode)
          })
          conceptoImpuestoNode.setTraslados(trasladosNode)
        }

        conceptoNode.setImpuestos(conceptoImpuestoNode)

      })

      conceptosNodes.add(conceptoNode)
    })
    conceptosNode
  }
}
