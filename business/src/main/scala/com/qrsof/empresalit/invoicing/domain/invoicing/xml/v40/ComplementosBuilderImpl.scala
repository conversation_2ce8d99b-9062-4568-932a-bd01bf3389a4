package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.invoicing.domain.complementos.Complementos
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.mercancias
import com.qrsof.empresalit.invoicing.domain.complementos.nomina.NominaComp
import com.qrsof.empresalit.invoicing.domain.invoicing.xml.XmlTimeUtils
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.*
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CartaPorte.FiguraTransporte.TiposFigura.PartesTransporte
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CartaPorte.Mercancias.*
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CartaPorte.Mercancias.Autotransporte.Remolques.Remolque
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CartaPorte.Mercancias.Autotransporte.{IdentificacionVehicular, Seguros}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CartaPorte.Mercancias.Mercancia.{CantidadTransporta, DetalleMercancia, GuiasIdentificacion, Pedimentos}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CartaPorte.Mercancias.TransporteFerroviario.{Carro, DerechosDePaso}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CartaPorte.Mercancias.TransporteMaritimo.Contenedor
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CartaPorte.Ubicaciones.Ubicacion
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CartaPorte.{FiguraTransporte, Mercancias}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Nomina.*
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Nomina.Deducciones.Deduccion
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Nomina.Emisor.EntidadSNCF
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Nomina.OtrosPagos.OtroPago.{CompensacionSaldosAFavor, SubsidioAlEmpleo}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Nomina.Percepciones.Percepcion.{AccionesOTitulos, HorasExtra}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Nomina.Percepciones.{JubilacionPensionRetiro, SeparacionIndemnizacion}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Pagos.Pago
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Pagos.Pago.ImpuestosP.{RetencionesP, TrasladosP}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Pagos.Pago.{DoctoRelacionado, ImpuestosP}

import java.text.SimpleDateFormat
import java.util.GregorianCalendar
import jakarta.inject.{Inject, Singleton}
import javax.xml.datatype.{DatatypeConstants, DatatypeFactory}
import scala.jdk.CollectionConverters.*

@Singleton
class ComplementosBuilderImpl @Inject()(xmlTimeUtils: XmlTimeUtils) extends ComplementosBuilder {
  override def execute(factory: ObjectFactory, complementos: Complementos): Comprobante.Complemento = {
    val complemento = factory.createComprobanteComplemento()
    complementos.recepcionPagos20.foreach(rp => {
      val complementoPagos = factory.createPagos()
      val totales = factory.createPagosTotales()
      complementoPagos.setVersion(rp.version)
      rp.totales.totalRetencionesIVA.foreach(tri => totales.setTotalRetencionesIVA(tri.bigDecimal))
      rp.totales.totalRetencionesISR.foreach(tri => totales.setTotalRetencionesISR(tri.bigDecimal))
      rp.totales.totalRetencionesIEPS.foreach(tri => totales.setTotalRetencionesIEPS(tri.bigDecimal))
      rp.totales.totalTrasladosBaseIVA16.foreach(t => totales.setTotalTrasladosBaseIVA16(t.bigDecimal))
      rp.totales.totalTrasladosImpuestoIVA16.foreach(t => totales.setTotalTrasladosImpuestoIVA16(t.bigDecimal))
      rp.totales.totalTrasladosBaseIVA8.foreach(t => totales.setTotalTrasladosBaseIVA8(t.bigDecimal))
      rp.totales.totalTrasladosImpuestoIVA8.foreach(t => totales.setTotalTrasladosImpuestoIVA8(t.bigDecimal))
      rp.totales.totalTrasladosBaseIVA0.foreach(t => totales.setTotalTrasladosBaseIVA0(t.bigDecimal))
      rp.totales.totalTrasladosImpuestoIVA0.foreach(t => totales.setTotalTrasladosImpuestoIVA0(t.bigDecimal))
      rp.totales.totalTrasladosBaseIVAExento.foreach(t => totales.setTotalTrasladosBaseIVAExento(t.bigDecimal))
      rp.totales.montoTotalPagos.foreach(t => totales.setMontoTotalPagos(t.bigDecimal))
      complementoPagos.setTotales(totales)
      val pagosInvoice = rp.pagos.map(p => {
        val gregorianCalendar = new GregorianCalendar()
        val dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
        gregorianCalendar.setTime(dateFormat.parse(p.fechaPago))
        val xMLGregorianCalendar = DatatypeFactory.newInstance.newXMLGregorianCalendar(gregorianCalendar)
        xMLGregorianCalendar.setTimezone(DatatypeConstants.FIELD_UNDEFINED)
        xMLGregorianCalendar.setMillisecond(DatatypeConstants.FIELD_UNDEFINED)
        val pago = factory.createPagosPago()

        pago.setFormaDePagoP(p.formaDePagoP)
        pago.setFechaPago(xMLGregorianCalendar)
        pago.setMonedaP(CMoneda.valueOf(p.monedaP))
        p.cadPago.foreach(pago.setCadPago)
        p.tipoCambioP.foreach(bd => pago.setTipoCambioP(bd.bigDecimal))
        p.numOperacion.foreach(pago.setNumOperacion)
        p.rfcEmisorCtaOrd.foreach(pago.setRfcEmisorCtaOrd)
        p.nomBancoOrdExt.foreach(pago.setNomBancoOrdExt)
        p.ctaOrdenante.foreach(pago.setCtaOrdenante)
        p.rfcEmisorCtaBen.foreach(pago.setRfcEmisorCtaBen)
        p.ctaBeneficiario.foreach(pago.setCtaBeneficiario)
        p.tipoCadPago.foreach(pago.setTipoCadPago)
        p.selloPago.foreach(sp => pago.setSelloPago(sp.getBytes))
        p.certPago.foreach(cp => pago.setCertPago(cp.getBytes))
        pago.setMonto(p.monto.bigDecimal)

        pago.getDoctoRelacionado.addAll(p.doctosRelacionesdos.map(dr => {
          val doctoRelacionado = new DoctoRelacionado
          doctoRelacionado.setIdDocumento(dr.idDocumento)
          dr.serie.foreach(doctoRelacionado.setSerie)
          dr.folio.foreach(doctoRelacionado.setFolio)
          doctoRelacionado.setMonedaDR(CMoneda.valueOf(dr.monedaDR))
          dr.equivalenciaDR.foreach(edr => doctoRelacionado.setEquivalenciaDR(edr.bigDecimal))
          doctoRelacionado.setNumParcialidad(dr.numParcialidad.bigInteger)
          doctoRelacionado.setImpSaldoAnt(dr.impSaldoAnt.bigDecimal)
          doctoRelacionado.setImpPagado(dr.impPagado.bigDecimal)
          doctoRelacionado.setImpSaldoInsoluto(dr.impSaldoInsoluto.bigDecimal)
          doctoRelacionado.setObjetoImpDR(dr.objetoImpDR)
          dr.impuestosDR.foreach(idr => {
            val impuestosDR = factory.createPagosPagoDoctoRelacionadoImpuestosDR()
            idr.retencionesDR.foreach(rdr => {
              val retencionesDR = factory.createPagosPagoDoctoRelacionadoImpuestosDRRetencionesDR()
              rdr.retenciones.foreach(r => {
                val retencionDR = factory.createPagosPagoDoctoRelacionadoImpuestosDRRetencionesDRRetencionDR()
                retencionDR.setBaseDR(r.baseDR.bigDecimal)
                retencionDR.setImpuestoDR(r.impuestoDR)
                retencionDR.setTipoFactorDR(resolveTipoFactor(r.tipoFactorDR))
                retencionDR.setTasaOCuotaDR(r.tasaOCuotaDR.bigDecimal)
                retencionDR.setImporteDR(r.importeDR.bigDecimal)
                retencionesDR.getRetencionDR.add(
                  retencionDR
                )
              })
              impuestosDR.setRetencionesDR(retencionesDR)
            })

            idr.trasladosDR.foreach(tdr => {
              val trasladosDR = factory.createPagosPagoDoctoRelacionadoImpuestosDRTrasladosDR()
              tdr.traslados.foreach(t => {
                val trasladoDR = factory.createPagosPagoDoctoRelacionadoImpuestosDRTrasladosDRTrasladoDR()
                trasladoDR.setBaseDR(t.baseDR.bigDecimal)
                trasladoDR.setImpuestoDR(t.impuestoDR)
                trasladoDR.setTipoFactorDR(resolveTipoFactor(t.tipoFactorDR))
                t.tasaOCuotaDR.foreach(tocdr => trasladoDR.setTasaOCuotaDR(tocdr.bigDecimal))
                t.importeDR.foreach(idr => trasladoDR.setImporteDR(idr.bigDecimal))
                trasladosDR.getTrasladoDR.add(
                  trasladoDR
                )
              })
              impuestosDR.setTrasladosDR(trasladosDR)
            })

            doctoRelacionado.setImpuestosDR(impuestosDR)
          })

          doctoRelacionado
        }).asJava)


        p.impuestosP.foreach(i => {
          val impuestos = new Pago.ImpuestosP
          i.retencionesP.foreach(rs => {
            val retenciones = new ImpuestosP.RetencionesP
            retenciones.getRetencionP.addAll(rs.map(r => {
              val retencion = new RetencionesP.RetencionP()
              retencion.setImporteP(r.importeP.bigDecimal)
              retencion.setImpuestoP(r.impuestoP)
              retencion
            }).asJava)
            impuestos.setRetencionesP(retenciones)
          })

          i.trasladosP.foreach(ts => {
            val traslados = new ImpuestosP.TrasladosP()
            traslados.getTrasladoP.addAll(ts.map(r => {
              val traslado = new TrasladosP.TrasladoP()
              traslado.setBaseP(r.baseP.bigDecimal)
              traslado.setImpuestoP(r.impuestoP)
              traslado.setTipoFactorP(resolveTipoFactor(r.tipoFactorP))
              r.tasaOCuotaP.foreach(toc => traslado.setTasaOCuotaP(toc.bigDecimal))
              r.importeP.foreach(ip => traslado.setImporteP(ip.bigDecimal))
              traslado
            }).asJava)
            impuestos.setTrasladosP(traslados)
          })

          pago.setImpuestosP(impuestos)
        })

        pago
      })
      complementoPagos.getPago.addAll(pagosInvoice.asJava)
      complemento.getAny.add(complementoPagos)
    })
    complementos.cartaPorte20.foreach(cp => {
      val cartaPorte = factory.createCartaPorte()
      cartaPorte.setVersion("2.0")
      cartaPorte.setTranspInternac(cp.transpInternac)
      cp.entradaSalidaMerc.foreach(cartaPorte.setEntradaSalidaMerc)
      cp.paisOrigenDestino.foreach(pod => cartaPorte.setPaisOrigenDestino(CPais.fromValue(pod)))
      cp.viaEntradaSalida.foreach(cartaPorte.setViaEntradaSalida)
      cp.totalDistRec.foreach(td => cartaPorte.setTotalDistRec(td.bigDecimal))
      cartaPorte.setUbicaciones(factory.createCartaPorteUbicaciones())
      cp.ubicaciones.ubicacion.foreach(ubi => {
        val ubicacion = factory.createCartaPorteUbicacionesUbicacion()
        ubicacion.setTipoUbicacion(ubi.tipoUbicacion)
        ubicacion.setRFCRemitenteDestinatario(ubi.rfcRemitenteDestinatario)
        ubi.idUbicacion.foreach(ubicacion.setIDUbicacion)
        ubi.nombreRemitenteDestinatario.foreach(ubicacion.setNombreRemitenteDestinatario)
        ubi.numRegIdTrib.foreach(ubicacion.setNumRegIdTrib)
        ubi.residenciaFiscal.foreach(rf => ubicacion.setResidenciaFiscal(CPais.fromValue(rf)))
        ubi.numEstacion.foreach(ubicacion.setNumEstacion)
        ubi.nombreEstacion.foreach(ubicacion.setNombreEstacion)
        ubi.navegacionTrafico.foreach(ubicacion.setNavegacionTrafico)
        ubi.tipoEstacion.foreach(ubicacion.setTipoEstacion)
        ubi.distanciaRecorrida.foreach(dr => ubicacion.setDistanciaRecorrida(dr.bigDecimal))
        val gregorianCalendar = new GregorianCalendar()
        val dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
        gregorianCalendar.setTime(dateFormat.parse(ubi.fechaHoraSalidaLlegada))
        val xMLGregorianCalendar = DatatypeFactory.newInstance.newXMLGregorianCalendar(gregorianCalendar)
        xMLGregorianCalendar.setTimezone(DatatypeConstants.FIELD_UNDEFINED)
        xMLGregorianCalendar.setMillisecond(DatatypeConstants.FIELD_UNDEFINED)
        ubicacion.setFechaHoraSalidaLlegada(xMLGregorianCalendar)
        ubi.domicilio.foreach(dom => {
          val domicilio = new Ubicacion.Domicilio
          domicilio.setEstado(dom.estado)
          domicilio.setPais(CPais.valueOf(dom.pais))
          domicilio.setCodigoPostal(dom.codigoPostal)
          dom.calle.foreach(domicilio.setCalle)
          dom.numeroExterior.foreach(domicilio.setNumeroExterior)
          dom.numeroInterior.foreach(domicilio.setNumeroInterior)
          dom.colonia.foreach(domicilio.setColonia)
          dom.localidad.foreach(domicilio.setLocalidad)
          dom.referencia.foreach(domicilio.setReferencia)
          dom.municipio.foreach(domicilio.setMunicipio)
          ubicacion.setDomicilio(domicilio)
        })
        cartaPorte.getUbicaciones.getUbicacion.add(ubicacion)
      })

      val mercancias = new Mercancias
      mercancias.setPesoBrutoTotal(cp.mercancias.pesoBrutoTotal.bigDecimal)
      mercancias.setUnidadPeso(cp.mercancias.unidadPeso)
      mercancias.setNumTotalMercancias(cp.mercancias.numTotalMercancias.intValue)
      cp.mercancias.pesoNetoTotal.foreach(ps => mercancias.setPesoNetoTotal(ps.bigDecimal))
      cp.mercancias.cargoPorTasacion.foreach(cp => mercancias.setCargoPorTasacion(cp.bigDecimal))
      cp.mercancias.mercancia.foreach(merc => {
        val mercancia = new Mercancia
        mercancia.setBienesTransp(merc.bienesTransp)
        mercancia.setDescripcion(merc.descripcion)
        mercancia.setCantidad(merc.cantidad.bigDecimal)
        mercancia.setClaveUnidad(merc.claveUnidad)
        mercancia.setPesoEnKg(merc.pesoEnKg.bigDecimal)
        merc.clavesSTCC.foreach(mercancia.setClaveSTCC)
        merc.unidad.foreach(mercancia.setUnidad)
        merc.dimensiones.foreach(mercancia.setDimensiones)
        merc.materialPeligroso.foreach(mercancia.setMaterialPeligroso)
        merc.cveMaterialPeligroso.foreach(mercancia.setCveMaterialPeligroso)
        merc.embalaje.foreach(mercancia.setEmbalaje)
        merc.descripEmbalaje.foreach(mercancia.setDescripEmbalaje)
        merc.valorMercancia.foreach(vm => mercancia.setValorMercancia(vm.bigDecimal))
        merc.moneda.foreach(mo => mercancia.setMoneda(CMoneda.fromValue(mo)))
        merc.fraccionArancelaria.foreach(mercancia.setFraccionArancelaria)
        merc.uuidComercioExt.foreach(mercancia.setUUIDComercioExt)
        merc.pedimentos.foreach(pe => {
          val pedimentos = new Pedimentos
          pedimentos.setPedimento(pe.pedimento)
          mercancia.getPedimentos.add(pedimentos)
        })
        merc.guiasIdentificacion.foreach(gif => {
          val guias = new GuiasIdentificacion
          guias.setNumeroGuiaIdentificacion(gif.numeroGuiaIdentificacion)
          guias.setDescripGuiaIdentificacion(gif.descripGuiaIdentificacion)
          guias.setPesoGuiaIdentificacion(gif.pesoGuiaIdentificacion.bigDecimal)
          mercancia.getGuiasIdentificacion.add(guias)
        })
        merc.cantidadTransporta.foreach(ct => {
          val cantidad = new CantidadTransporta
          cantidad.setCantidad(ct.cantidad.bigDecimal)
          cantidad.setIDOrigen(ct.idOrigen)
          cantidad.setIDDestino(ct.idDestino)
          ct.cvesTransporte.foreach(cantidad.setCvesTransporte)
          mercancia.getCantidadTransporta.add(cantidad)
        })
        merc.detalleMercancia.foreach(dm => {
          val detalle = new DetalleMercancia
          detalle.setUnidadPesoMerc(dm.unidadPesoMerc)
          detalle.setPesoBruto(dm.pesoBruto.bigDecimal)
          detalle.setPesoNeto(dm.pesoNeto.bigDecimal)
          detalle.setPesoTara(dm.pesoTara.bigDecimal)
          dm.numPiezas.foreach(np => detalle.setNumPiezas(np.intValue))
          mercancia.setDetalleMercancia(detalle)
        })
        mercancias.getMercancia.add(mercancia)
      })
      cp.mercancias.autotransporte.foreach(at => {
        val auto = factory.createCartaPorteMercanciasAutotransporte()
        val permSCT = at.permSCT
        auto.setPermSCT(resolveCTipoPermiso(permSCT))
        auto.setNumPermisoSCT(at.numPermisoSCT)
        auto.setIdentificacionVehicular(getIdentificacionVehicular(at))
        auto.setSeguros(getSeguro(at))
        at.remolques.foreach(r => {
          val remolquesNode = factory.createCartaPorteMercanciasAutotransporteRemolques()
          auto.setRemolques(remolquesNode)
          r.remolque.foreach(rem => {
            val remolque = new Remolque
            val subTipoRem = rem.subTipoRem
            remolque.setSubTipoRem(resolveCSubTipoRem(subTipoRem))
            remolque.setPlaca(rem.placa)
            auto.getRemolques.getRemolque.add(remolque)
          })
        })

        mercancias.setAutotransporte(auto)
      })
      cp.mercancias.transporteMaritimo.foreach(tm => {
        val mar = new TransporteMaritimo
        val tipoEmbarcacion = tm.tipoEmbarcacion
        mar.setTipoEmbarcacion(resolveCConfigMaritima(tipoEmbarcacion))
        mar.setMatricula(tm.matricula)
        mar.setNumeroOMI(tm.numeroOMI)
        mar.setNacionalidadEmbarc(CPais.valueOf(tm.nacionalidadEmbarc))
        mar.setUnidadesDeArqBruto(tm.unidadesDeArqBruto.bigDecimal)
        val tipoCarga = tm.tipoCarga
        mar.setTipoCarga(resolveCClaveTipoCarga(tipoCarga))
        mar.setNumCertITC(tm.numCertITC)
        mar.setNombreAgenteNaviero(tm.nombreAgenteNaviero)
        mar.setNumAutorizacionNaviero(tm.numAutorizacionNaviero)
        tm.permSCT.foreach(pe => mar.setPermSCT(CTipoPermiso.fromValue(pe)))
        tm.numPermisoSCT.foreach(mar.setNumPermisoSCT)
        tm.nombreAseg.foreach(mar.setNombreAseg)
        tm.numPolizaSeguro.foreach(mar.setNumPolizaSeguro)
        tm.anioEmbarcacion.foreach(ae => mar.setAnioEmbarcacion(ae.intValue))
        tm.nombreEmbarc.foreach(mar.setNombreEmbarc)
        tm.eslora.foreach(es => mar.setEslora(es.bigDecimal))
        tm.manga.foreach(mn => mar.setManga(mn.bigDecimal))
        tm.calado.foreach(cl => mar.setCalado(cl.bigDecimal))
        tm.lineaNaviera.foreach(mar.setLineaNaviera)
        tm.numViaje.foreach(mar.setNumViaje)
        tm.numConocEmbarc.foreach(mar.setNumConocEmbarc)
        tm.contenedor.foreach(cont => {
          val contenedor = new Contenedor
          contenedor.setMatriculaContenedor(cont.matriculaContenedor)
          val tipoContenedor = cont.tipoContenedor
          contenedor.setTipoContenedor(resolveCContenedorMaritimo(tipoContenedor))
          cont.numPrecinto.foreach(contenedor.setNumPrecinto)
          mar.getContenedor.add(contenedor)
          mercancias.setTransporteMaritimo(mar)
        })
      })
      cp.mercancias.transporteAereo.foreach(ta => {
        val aereo = new TransporteAereo
        aereo.setPermSCT(resolveCTipoPermiso(ta.permSCT))
        aereo.setNumPermisoSCT(ta.numPermisoSCT)
        aereo.setNumeroGuia(ta.numeroGuia)
        val codigoTransportista = ta.codigoTransportista
        aereo.setCodigoTransportista(resolveCCodigoTransporteAereo(codigoTransportista))
        ta.matriculaAeronave.foreach(aereo.setMatriculaAeronave)
        ta.nombreAseg.foreach(aereo.setNombreAseg)
        ta.numPolizaSeguro.foreach(aereo.setNumPolizaSeguro)
        ta.lugarContrato.foreach(aereo.setLugarContrato)
        ta.rfcEmbarcador.foreach(aereo.setRFCEmbarcador)
        ta.numRegIdTribEmbarc.foreach(aereo.setNumRegIdTribEmbarc)
        ta.residenciaFiscalEmbarc.foreach(rf => aereo.setResidenciaFiscalEmbarc(CPais.fromValue(rf)))
        ta.nombreEmbarcador.foreach(aereo.setNombreEmbarcador)
        mercancias.setTransporteAereo(aereo)
      })
      cp.mercancias.transporteFerroviario.foreach(tf => {
        val ferrov = new TransporteFerroviario
        val tipoServicio = tf.tipoServicio
        ferrov.setTipoDeServicio(resolveCTipoDeServicio(tipoServicio))
        val trafico = tf.tipoDeTrafico
        ferrov.setTipoDeTrafico(resolveCTipoDeTrafico(trafico))
        tf.nombreAseg.foreach(ferrov.setNombreAseg)
        tf.numPolizaSeguro.foreach(ferrov.setNumPolizaSeguro)
        tf.derechosDePaso.foreach(dp => {
          val derechos = new DerechosDePaso
          val tipoDerechoDePaso = dp.tipoDerechoDePaso
          derechos.setTipoDerechoDePaso(resolveCDerechosDePaso(tipoDerechoDePaso))
          derechos.setKilometrajePagado(dp.kilometrajePagado.bigDecimal)
          ferrov.getDerechosDePaso.add(derechos)
        })
        tf.carro.foreach(carr => {
          val carro = new Carro
          val tipoCarro = carr.tipoCarro
          carro.setTipoCarro(resolveCTipoCarro(tipoCarro))
          carro.setMatriculaCarro(carr.matriculaCarro)
          carro.setGuiaCarro(carr.guiaCarro)
          carro.setToneladasNetasCarro(carr.toneladasNetasCarro.bigDecimal)
          carr.contenedor.foreach(ct => {
            val conten = new Carro.Contenedor
            val tipoContenedor = ct.tipoContenedor
            conten.setTipoContenedor(resolveCContenedor(tipoContenedor))
            conten.setPesoContenedorVacio(ct.pesoContenedorVacio.bigDecimal)
            conten.setPesoNetoMercancia(ct.pesoNetoMercancia.bigDecimal)
            carro.getContenedor.add(conten)
          })
          ferrov.getCarro.add(carro)
        })
        mercancias.setTransporteFerroviario(ferrov)
      })
      cartaPorte.setMercancias(mercancias)
      cp.figuratransporte.foreach(ft => {
        val figuraTransporte = factory.createCartaPorteFiguraTransporte()
        ft.tiposFigura.foreach(tip => {
          val tiposFiguraNode = factory.createCartaPorteFiguraTransporteTiposFigura()
          tiposFiguraNode.setTipoFigura(tip.tipoFigura)
          tip.rfcFigura.foreach(tiposFiguraNode.setRFCFigura)
          tip.numLicencia.foreach(tiposFiguraNode.setNumLicencia)
          tip.nombreFigura.foreach(tiposFiguraNode.setNombreFigura)
          tip.numRegIdTribFigura.foreach(tiposFiguraNode.setNumRegIdTribFigura)
          tip.residenciaFiscalFigura.foreach(rf => tiposFiguraNode.setResidenciaFiscalFigura(CPais.fromValue(rf)))
          tip.partesTransporte.foreach(pt => {
            val partes = new PartesTransporte
            partes.setParteTransporte(CParteTransporte.fromValue(pt.parteTransporte))
            tiposFiguraNode.getPartesTransporte.add(partes)
          })
          tip.domicilio.foreach(dom => {
            val domicilio = new FiguraTransporte.TiposFigura.Domicilio
            domicilio.setEstado(dom.estadoFigura)
            domicilio.setPais(CPais.fromValue(dom.paisFigura))
            domicilio.setCodigoPostal(dom.codigoPostalFigura)
            dom.calleFigura.foreach(domicilio.setCalle)
            dom.numeroExteriorFigura.foreach(domicilio.setNumeroExterior)
            dom.numeroInteriorFigura.foreach(domicilio.setNumeroInterior)
            dom.coloniaFigura.foreach(domicilio.setColonia)
            dom.localidadFigura.foreach(domicilio.setLocalidad)
            dom.referenciaFigura.foreach(domicilio.setReferencia)
            dom.municipioFigura.foreach(domicilio.setMunicipio)
            tiposFiguraNode.setDomicilio(domicilio)
          })
          figuraTransporte.getTiposFigura.add(tiposFiguraNode)
        })
        cartaPorte.setFiguraTransporte(figuraTransporte)
      })
      complemento.getAny.add(cartaPorte)
    })
    complementos.nomina12.foreach(nom => {
      val nomina = new Nomina
      nomina.setVersion("1.2")
      nomina.setTipoNomina(CTipoNomina.valueOf(nom.tipoNomina))

      nomina.setFechaPago(xmlTimeUtils.getDateWithoutTime(nom.fechaPago, "yyyy-MM-dd"))
      nomina.setFechaInicialPago(xmlTimeUtils.getDateWithoutTime(nom.fechaInicialPago, "yyyy-MM-dd"))
      nomina.setFechaFinalPago(xmlTimeUtils.getDateWithoutTime(nom.fechaFinalPago, "yyyy-MM-dd"))

      nomina.setNumDiasPagados(nom.numDiasPagados.bigDecimal)
      nom.totalPercepciones.foreach(tot => nomina.setTotalPercepciones(tot.bigDecimal))
      nom.totalDeducciones.foreach(ded => nomina.setTotalDeducciones(ded.bigDecimal))
      nom.totalOtrosPagos.foreach(otr => nomina.setTotalOtrosPagos(otr.bigDecimal))
      nom.emisor.foreach(emi => {
        val emisor = new Nomina.Emisor
        emi.curp.foreach(emisor.setCurp)
        emi.registroPatronal.foreach(emisor.setRegistroPatronal)
        emi.rfcPatronOrigen.foreach(emisor.setRfcPatronOrigen)
        emi.entidadSNCF.foreach(enti => {
          val entidad = new EntidadSNCF
          entidad.setOrigenRecurso(COrigenRecurso.valueOf(enti.origenRecurso))
          enti.montoRecursoPropio.foreach(mon => entidad.setMontoRecursoPropio(mon.bigDecimal))
          emisor.setEntidadSNCF(entidad)
        })
        nomina.setEmisor(emisor)
      })
      nomina.setReceptor(getReceptor(nom))
      nom.percepciones.foreach(per => {
        val percep = new Percepciones
        per.totalSueldos.foreach(ts => percep.setTotalSueldos(ts.bigDecimal))
        per.totalSeparacionIndemnizacion.foreach(tsi => percep.setTotalSeparacionIndemnizacion(tsi.bigDecimal))
        per.totalJubilacionPensionRetiro.foreach(tjr => percep.setTotalJubilacionPensionRetiro(tjr.bigDecimal))
        percep.setTotalGravado(per.totalGravado.bigDecimal)
        percep.setTotalExento(per.totalExento.bigDecimal)
        percep.getPercepcion.addAll(per.percepcion.map(p => {
          val percepcion = new Percepciones.Percepcion
          percepcion.setTipoPercepcion(p.tipoPercepcion)
          percepcion.setClave(p.clave)
          percepcion.setConcepto(p.concepto)
          percepcion.setImporteGravado(p.importeGravado.bigDecimal)
          percepcion.setImporteExento(p.importeExento.bigDecimal)
          p.accionesOTitulos.foreach(aot => {
            val acciones = new AccionesOTitulos
            acciones.setValorMercado(aot.valorMercado.bigDecimal)
            acciones.setPrecioAlOtorgarse(aot.precioAlOtorgarse.bigDecimal)
            percepcion.setAccionesOTitulos(acciones)
          })
          p.horasExtra.foreach(he => {
            val horas = new HorasExtra
            horas.setDias(he.dias.intValue)
            horas.setTipoHoras(he.tipoHoras)
            horas.setHorasExtra(he.horasExtra.intValue)
            horas.setImportePagado(he.importePagado.bigDecimal)
            percepcion.getHorasExtra.add(horas)
          })
          percepcion
        }).asJava)
        per.jubilacionPensionRetiro.foreach(jp => {
          val jubilacion = new JubilacionPensionRetiro
          jp.totalUnaExhibicion.foreach(tu => jubilacion.setTotalUnaExhibicion(tu.bigDecimal))
          jp.totalParcialidad.foreach(tp => jubilacion.setTotalParcialidad(tp.bigDecimal))
          jp.montoDiario.foreach(md => jubilacion.setMontoDiario(md.bigDecimal))
          jubilacion.setIngresoAcumulable(jp.ingresoAcumulable.bigDecimal)
          jubilacion.setIngresoNoAcumulable(jp.ingresoNoAcumulable.bigDecimal)
          percep.setJubilacionPensionRetiro(jubilacion)
        })
        per.separacionIndemnizacion.foreach(si => {
          val separacion = new SeparacionIndemnizacion
          separacion.setTotalPagado(si.totalPagado.bigDecimal)
          separacion.setNumAñosServicio(si.numAniosServicio.intValue)
          separacion.setUltimoSueldoMensOrd(si.ultimoSueldoMensOrd.bigDecimal)
          separacion.setIngresoAcumulable(si.ingresoAcumulable.bigDecimal)
          separacion.setIngresoNoAcumulable(si.ingresoNoAcumulable.bigDecimal)
          percep.setSeparacionIndemnizacion(separacion)
        })
        nomina.setPercepciones(percep)
      })
      nom.deducciones.foreach(ded => {
        val deducciones = new Deducciones
        ded.totalOtrasDeducciones.foreach(to => deducciones.setTotalOtrasDeducciones(to.bigDecimal))
        ded.totalImpuestosRetenidos.foreach(ir => deducciones.setTotalImpuestosRetenidos(ir.bigDecimal))
        deducciones.getDeduccion.addAll(ded.deduccion.map(d => {
          val deduccion = new Deduccion
          deduccion.setTipoDeduccion(d.tipoDeduccion)
          deduccion.setClave(d.clave)
          deduccion.setConcepto(d.concepto)
          deduccion.setImporte(d.importe.bigDecimal)
          deduccion
        }).asJava)
        nomina.setDeducciones(deducciones)
      })
      nom.otrosPagos.foreach(otp => {
        val otrosP = new OtrosPagos
        otrosP.getOtroPago.addAll(otp.otroPago.map(o => {
          val otroPago = new OtrosPagos.OtroPago
          otroPago.setTipoOtroPago(o.tipoOtroPago)
          otroPago.setClave(o.clave)
          otroPago.setConcepto(o.concepto)
          otroPago.setImporte(o.importe.bigDecimal)
          o.subsidioAlEmpleo.foreach(sae => {
            val subsidio = new SubsidioAlEmpleo
            subsidio.setSubsidioCausado(sae.subsidioCausado.bigDecimal)
            otroPago.setSubsidioAlEmpleo(subsidio)
          })
          o.compensacionSaldosAFavor.foreach(csf => {
            val compensacion = new CompensacionSaldosAFavor
            compensacion.setSaldoAFavor(csf.saldoAFavor.bigDecimal)
            compensacion.setAño(csf.anio.shortValue())
            compensacion.setRemanenteSalFav(csf.remanenteSalFav.bigDecimal)
            otroPago.setCompensacionSaldosAFavor(compensacion)
          })
          otroPago
        }).asJava)
        nomina.setOtrosPagos(otrosP)
      })
      nom.incapacidades.foreach(incap => {
        val incapacidades = new Incapacidades
        incapacidades.getIncapacidad.addAll(incap.incapacidad.map(inc => {
          val incapacidad = new Incapacidades.Incapacidad
          incapacidad.setDiasIncapacidad(inc.diasIncapacidad.intValue)
          incapacidad.setTipoIncapacidad(inc.tipoIncapacidad)
          inc.importeMonetario.foreach(im => incapacidad.setImporteMonetario(im.bigDecimal))
          incapacidad
        }).asJava)
        nomina.setIncapacidades(incapacidades)
      })
      complemento.getAny.add(nomina)
    })
    complemento
  }

  private def resolveCContenedor(tipoContenedor: String) = {
    try {
      CContenedor.valueOf(tipoContenedor)
    }
    catch {
      case _: IllegalArgumentException => CContenedor.fromValue(tipoContenedor)
    }
  }

  private def resolveCTipoCarro(tipoCarro: String) = {
    try {
      CTipoCarro.valueOf(tipoCarro)
    }
    catch {
      case _: IllegalArgumentException => CTipoCarro.fromValue(tipoCarro)
    }
  }

  private def resolveCDerechosDePaso(tipoDerechoDePaso: String) = {
    try {
      CDerechosDePaso.valueOf(tipoDerechoDePaso)
    }
    catch {
      case _: IllegalArgumentException => CDerechosDePaso.fromValue(tipoDerechoDePaso)
    }
  }

  private def resolveCTipoDeTrafico(trafico: String) = {
    try {
      CTipoDeTrafico.valueOf(trafico)
    }
    catch {
      case _: IllegalArgumentException => CTipoDeTrafico.fromValue(trafico)
    }
  }

  private def resolveCTipoDeServicio(tipoServicio: String) = {
    try {
      CTipoDeServicio.valueOf(tipoServicio)
    }
    catch {
      case _: IllegalArgumentException => CTipoDeServicio.fromValue(tipoServicio)
    }
  }

  private def resolveCCodigoTransporteAereo(codigoTransportista: String) = {
    try {
      CCodigoTransporteAereo.valueOf(codigoTransportista)
    }
    catch {
      case _: IllegalArgumentException => CCodigoTransporteAereo.fromValue(codigoTransportista)
    }
  }

  private def resolveCContenedorMaritimo(tipoContenedor: String) = {
    try {
      CContenedorMaritimo.valueOf(tipoContenedor)
    }
    catch {
      case _: IllegalArgumentException => CContenedorMaritimo.fromValue(tipoContenedor)
    }
  }

  private def resolveCClaveTipoCarga(tipoCarga: String) = {
    try {
      CClaveTipoCarga.valueOf(tipoCarga)
    }
    catch {
      case _: IllegalArgumentException => CClaveTipoCarga.fromValue(tipoCarga)
    }
  }

  private def resolveCConfigMaritima(tipoEmbarcacion: String) = {
    try {
      CConfigMaritima.valueOf(tipoEmbarcacion)
    }
    catch {
      case _: IllegalArgumentException => CConfigMaritima.fromValue(tipoEmbarcacion)
    }
  }

  private def resolveCSubTipoRem(subTipoRem: String) = {
    try {
      CSubTipoRem.valueOf(subTipoRem)
    }
    catch {
      case _: IllegalArgumentException => CSubTipoRem.fromValue(subTipoRem)
    }
  }

  private def resolveCTipoPermiso(permSCT: String) = {
    try {
      CTipoPermiso.valueOf(permSCT)
    }
    catch {
      case _: IllegalArgumentException => CTipoPermiso.fromValue(permSCT)
    }
  }

  private def resolveTipoFactor(tipoFactorDR: String) = {
    try {
      CTipoFactor.valueOf(tipoFactorDR)
    } catch {
      case _: IllegalArgumentException => CTipoFactor.fromValue(tipoFactorDR)
    }
  }

  private def getReceptor(nom: NominaComp) = {
    val receptor = new Receptor
    receptor.setCurp(nom.receptor.curp)
    nom.receptor.numSeguridadSocial.foreach(receptor.setNumSeguridadSocial)
    nom.receptor.fechaInicioRelLaboral.foreach(fi => receptor.setFechaInicioRelLaboral(xmlTimeUtils.getDateWithoutTime(fi, "yyyy-MM-dd")))
    nom.receptor.antiguedad.foreach(receptor.setAntigüedad)
    receptor.setTipoContrato(nom.receptor.tipoContrato)
    nom.receptor.sindicalizado.foreach(receptor.setSindicalizado)
    nom.receptor.tipoJornada.foreach(receptor.setTipoJornada)
    receptor.setTipoRegimen(nom.receptor.tipoRegimen)
    receptor.setNumEmpleado(nom.receptor.numEmpleado)
    nom.receptor.departamento.foreach(receptor.setDepartamento)
    nom.receptor.puesto.foreach(receptor.setPuesto)
    nom.receptor.riesgoPuesto.foreach(receptor.setRiesgoPuesto)
    receptor.setPeriodicidadPago(nom.receptor.periodicidadPago)
    nom.receptor.banco.foreach(receptor.setBanco)
    nom.receptor.cuentaBancaria.foreach(cb => receptor.setCuentaBancaria(BigInt(cb).bigInteger))
    nom.receptor.salarioBaseCotApor.foreach(sb => receptor.setSalarioBaseCotApor(sb.bigDecimal))
    nom.receptor.salarioDiarioIntegrado.foreach(sd => receptor.setSalarioDiarioIntegrado(sd.bigDecimal))
    receptor.setClaveEntFed(CEstado.valueOf(nom.receptor.claveEntFed))
    nom.receptor.subContratacion.foreach(subC => {
      val subCont = new Receptor.SubContratacion
      subCont.setRfcLabora(subC.rfcLabora)
      subCont.setPorcentajeTiempo(subC.porcentajeTiempo.bigDecimal)
      receptor.getSubContratacion.add(subCont)
    })
    receptor
  }

  private def getSeguro(at: mercancias.Autotransporte) = {
    val seguro = new Seguros
    seguro.setAseguraRespCivil(at.seguros.aseguraRespCivil)
    seguro.setPolizaRespCivil(at.seguros.polizaRespCivil)
    at.seguros.aseguraMedAmbiente.foreach(seguro.setAseguraMedAmbiente)
    at.seguros.polizaMedAmbiente.foreach(seguro.setPolizaMedAmbiente)
    at.seguros.aseguraCarga.foreach(seguro.setAseguraCarga)
    at.seguros.polizaCarga.foreach(seguro.setPolizaCarga)
    at.seguros.primaSeguro.foreach(ps => seguro.setPrimaSeguro(ps.bigDecimal))
    seguro
  }

  private def getIdentificacionVehicular(at: mercancias.Autotransporte) = {
    val identificacion = new IdentificacionVehicular
    identificacion.setConfigVehicular(resolveEnumForConfigVehicular(at))
    identificacion.setPlacaVM(at.identificacionVehicular.placaVM)
    identificacion.setAnioModeloVM(at.identificacionVehicular.anioModeloVM)
    identificacion
  }

  private def resolveEnumForConfigVehicular(at: mercancias.Autotransporte) = {
    try {
      CConfigAutotransporte.valueOf(at.identificacionVehicular.configVehicular)
    } catch {
      case _: IllegalArgumentException =>
        CConfigAutotransporte.fromValue(at.identificacionVehicular.configVehicular)
    }
  }
}
