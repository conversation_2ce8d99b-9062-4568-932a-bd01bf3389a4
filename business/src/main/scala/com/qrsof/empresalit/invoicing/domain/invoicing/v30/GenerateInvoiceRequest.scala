package com.qrsof.empresalit.invoicing.domain.invoicing.v30

import com.qrsof.empresalit.invoicing.domain.complementos.Complementos
import com.qrsof.empresalit.invoicing.domain.conceptos.Concepto
import com.qrsof.empresalit.invoicing.domain.impuestos.Impuestos
import com.qrsof.empresalit.invoicing.generateinvoice.ReceptorInvoice

case class GenerateInvoiceRequest(
                                   version: String,
                                   companyKey: String,
                                   serie: Option[String] = None,
                                   condicionesDePago: Option[String] = None,
                                   folio: Option[String] = None,
                                   lugarExpedicion: String,
                                   formaPago: Option[String] = None,
                                   metodoPago: Option[String] = None,
                                   moneda: String,
                                   tipoComprobante: String,
                                   tipoCambio: Option[BigDecimal] = None,
                                   subTotal: BigDecimal,
                                   total: BigDecimal,
                                   descuento: Option[BigDecimal] = None,
                                   exportacion: Option[String] = None,
                                   confirmacion: Option[String] = None,
                                   receptor: Option[ReceptorInvoice],
                                   conceptos: Seq[Concepto],
                                   impuestos: Option[Impuestos] = None,
                                   complementos: Option[Complementos] = None
                                 ) {

}
