package com.qrsof.empresalit.invoicing.actions.getinvoicestatus
import com.qrsof.empresalit.companies.CompanyGateway
import jakarta.inject.{Inject, Singleton}

@Singleton
class GetInvoiceStatusActionImpl @Inject() (companyGateway: CompanyGateway) extends GetInvoiceStatusAction {
  override def execute(request: GetInvoiceStatusRequest): InvoiceStatusResponse = {
    companyGateway.getCompanyByKey(request.companyKey).get
    InvoiceStatusResponse("uuid", "status", "cancellable", "statusCancellation", "statusCode")
  }
}
