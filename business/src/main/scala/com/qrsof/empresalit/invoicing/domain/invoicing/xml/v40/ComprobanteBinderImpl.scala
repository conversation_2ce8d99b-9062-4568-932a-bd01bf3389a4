package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.invoicing.domain.invoicing.v40.conceptos.Concepto
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.impuestos.Impuestos
import com.qrsof.empresalit.invoicing.domain.invoicing.xml.XmlTimeUtils
import com.qrsof.empresalit.invoicing.domain.invoicing.{IssuerInvoicingData, v40}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.*
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Comprobante.{CfdiRelacionados, Emisor}
import org.slf4j.LoggerFactory

import java.util.Base64
import jakarta.inject.{Inject, Singleton}
import scala.jdk.CollectionConverters.*

@Singleton
class ComprobanteBinderImpl @Inject()(
                                       emisorBuilder: EmisorBuilder,
                                       receptorBuilder: ReceptorBuilder,
                                       conceptosBuilder: ConceptosBuilder,
                                       xmlTimeUtils: XmlTimeUtils,
                                       monedaFactory: MonedaFactory,
                                       tipoComprobanteFactory: TipoComprobanteFactory,
                                       metodoPagoFactory: MetodoPagoFactory,
                                       impuestosBuilder: ImpuestosBuilder,
                                       complementosBuilder: ComplementosBuilder,
                                     ) extends ComprobanteBinder {

  private val logger = LoggerFactory.getLogger(classOf[ComprobanteBinderImpl])


  override def execute(issuerInvoicingData: IssuerInvoicingData, generateInvoiceRequest: v40.GenerateInvoiceRequest): Comprobante = {
    logger.debug("Xml generation: {} {}", issuerInvoicingData, generateInvoiceRequest)
    val factory = new ObjectFactory
    val comprobante = factory.createComprobante()
    comprobante.setVersion("4.0")
    generateInvoiceRequest.serie.foreach(comprobante.setSerie)
    generateInvoiceRequest.folio.foreach(comprobante.setFolio)
    comprobante.setFecha(generateInvoiceRequest.fecha.map(xmlTimeUtils.getDate(_, "yyyy-MM-dd'T'hh:mm:ss")).getOrElse(xmlTimeUtils.getCurrentDate()))
    generateInvoiceRequest.formaPago.foreach(fp => comprobante.setFormaPago(fp))
    comprobante.setNoCertificado(issuerInvoicingData.certificateNumber)
    generateInvoiceRequest.condicionesDePago.foreach(cp => {
      comprobante.setCondicionesDePago(cp)
    })
    comprobante.setSubTotal(generateInvoiceRequest.subTotal.bigDecimal)
    comprobante.setMoneda(monedaFactory.getInstace(generateInvoiceRequest.moneda))
    generateInvoiceRequest.tipoCambio.foreach(tc => comprobante.setTipoCambio(tc.bigDecimal))

    comprobante.setTotal(generateInvoiceRequest.total.bigDecimal)
    generateInvoiceRequest.descuento.foreach(des => comprobante.setDescuento(des.bigDecimal))
    comprobante.setTipoDeComprobante(tipoComprobanteFactory.getInstance(generateInvoiceRequest.tipoComprobante))
    comprobante.setExportacion(generateInvoiceRequest.exportacion)
    generateInvoiceRequest.metodoPago.foreach(mp => {
      comprobante.setMetodoPago(metodoPagoFactory.getInstance(mp))
    })
    comprobante.setLugarExpedicion(generateInvoiceRequest.lugarExpedicion)
    generateInvoiceRequest.confirmacion.foreach(comprobante.setConfirmacion)
    generateInvoiceRequest.informacionGlobal.foreach(ig => {
      val informacionGlobalNode = factory.createComprobanteInformacionGlobal()
      informacionGlobalNode.setPeriodicidad(ig.periodicidad)
      informacionGlobalNode.setAño(ig.anio)
      informacionGlobalNode.setMeses(ig.meses)
      comprobante.setInformacionGlobal(informacionGlobalNode)
    })
    generateInvoiceRequest.cfdiRelacionados.foreach(_.foreach(cr => {
      val relacionadosNode = factory.createComprobanteCfdiRelacionados()
      relacionadosNode.setTipoRelacion(cr.tipoRelacion)
      val value: Seq[CfdiRelacionados.CfdiRelacionado] = cr.cfdiRelacionado.map(cRelacionado => {
        val cfdiRelacionadoNode = factory.createComprobanteCfdiRelacionadosCfdiRelacionado()
        cfdiRelacionadoNode.setUUID(cRelacionado.uuid)
        cfdiRelacionadoNode
      })
      relacionadosNode.getCfdiRelacionado.addAll(value.asJava)
      comprobante.getCfdiRelacionados.add(relacionadosNode)
    }))
    comprobante.setCertificado(Base64.getEncoder.encodeToString(issuerInvoicingData.publicKey))
    comprobante.setEmisor(buildEmisor(factory, issuerInvoicingData))
    comprobante.setReceptor(buildReceptor(factory, generateInvoiceRequest.receptor.get))
    comprobante.setConceptos(buildConceptos(factory, generateInvoiceRequest.conceptos))
    generateInvoiceRequest.impuestos.foreach(impuestos => {
      comprobante.setImpuestos(buildImpuestos(factory, impuestos))
    })
    generateInvoiceRequest.complementos.foreach(c => {
      comprobante.setComplemento(complementosBuilder.execute(factory, c))
    })

    comprobante
  }

  private def buildImpuestos(factory: ObjectFactory, impuestos: Impuestos): Comprobante.Impuestos = {
    impuestosBuilder.execute(factory, impuestos)
  }

  private def buildConceptos(factory: ObjectFactory, conceptos: Seq[Concepto]): Comprobante.Conceptos = {
    conceptosBuilder.execute(factory, conceptos)
  }

  private def buildReceptor(factory: ObjectFactory, receptor: v40.ReceptorInvoice): Comprobante.Receptor = {
    receptorBuilder.execute(factory, receptor)
  }

  private def buildEmisor(factory: ObjectFactory, issuerInvoicingData: IssuerInvoicingData): Emisor = {
    emisorBuilder.execute(factory, issuerInvoicingData)
  }
}
