package com.qrsof.empresalit.invoicing.domain.complementos.nomina

case class ReceptorNom(
                        curp: String,
                        numSeguridadSocial: Option[String] = None,
                        fechaInicioRelLaboral: Option[String] = None,
                        antiguedad: Option[String] = None,
                        tipoContrato: String,
                        sindicalizado: Option[String] = None,
                        tipoJornada: Option[String] = None,
                        tipoRegimen: String,
                        numEmpleado: String,
                        departamento: Option[String] = None,
                        puesto: Option[String] = None,
                        riesgoPuesto: Option[String] = None,
                        periodicidadPago: String,
                        banco: Option[String] = None,
                        cuentaBancaria: Option[Long] = None,
                        salarioBaseCotApor: Option[BigDecimal] = None,
                        salarioDiarioIntegrado: Option[BigDecimal] = None,
                        claveEntFed: String,
                        subContratacion: Seq[SubContratacion] = Nil
                      )
