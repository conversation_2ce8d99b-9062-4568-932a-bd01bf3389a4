package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.sat.models.cfdi33.Comprobante
import org.slf4j.LoggerFactory

import java.io.{ByteArrayInputStream, ByteArrayOutputStream}
import jakarta.inject.{Inject, Singleton}
import javax.xml.transform.TransformerFactory
import javax.xml.transform.stream.{StreamResult, StreamSource}

@Singleton
private[xml] class OriginalChainBuilderImpl @Inject()(
                                                       xmlSerializer: XmlSerializer
                                                     ) extends OriginalChainBuilder {
  private val logger = LoggerFactory.getLogger(classOf[OriginalChainBuilderImpl])

  override def execute(comprobante: Comprobante): Array[Byte] = {
    val xml = xmlSerializer.execute(comprobante)
    logger.debug("Xml: {}", new String(xml))
    val xsltFactory = TransformerFactory.newInstance
    val inputStream = this.getClass.getResourceAsStream("/33/cadenaoriginal_3_3.xslt")
    val transformer = xsltFactory.newTransformer(new StreamSource(inputStream))
    val byteArrayOutputStream = new ByteArrayOutputStream()
    transformer.transform(new StreamSource(new ByteArrayInputStream(xml)), new StreamResult(byteArrayOutputStream))
    val byteArray = byteArrayOutputStream.toByteArray
    logger.debug("Cadena original: {}", new String(byteArray))
    byteArray
  }
}
