package com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.mercancias

case class Mercancia(
                      bienesTransp: String,
                      clavesSTCC: Option[String] = None,
                      descripcion: String,
                      cantidad: BigDecimal,
                      claveUnidad: String,
                      unidad: Option[String] = None,
                      dimensiones: Option[String] = None,
                      materialPeligroso: Option[String] = None,
                      cveMaterialPeligroso: Option[String] = None,
                      embalaje: Option[String] = None,
                      descripEmbalaje: Option[String] = None,
                      pesoEnKg: BigDecimal,
                      valorMercancia: Option[BigDecimal] = None,
                      moneda: Option[String] = None,
                      fraccionArancelaria: Option[String] = None,
                      uuidComercioExt: Option[String] = None,
                      pedimentos: Seq[Pedimento] = Nil,
                      guiasIdentificacion: Seq[GuiasIdentificacion] = Nil,
                      cantidadTransporta: Seq[CantidadTransporta] = Nil,
                      detalleMercancia: Seq[DetalleMercancia] = Nil
                    )
