package com.qrsof.empresalit.invoicing.domain.invoicing.v40.conceptos

import com.qrsof.empresalit.invoicing.domain.conceptos.InformacionAduanera

case class Parte(
                  claveProdServ: String,
                  noIdentificacion: Option[String],
                  cantidad: BigDecimal,
                  unidad: Option[String],
                  descripcion: String,
                  valorUnitario: Option[BigDecimal],
                  importe: Option[BigDecimal],
                  informacionAduanera: Seq[InformacionAduanera] = Nil,
                )
