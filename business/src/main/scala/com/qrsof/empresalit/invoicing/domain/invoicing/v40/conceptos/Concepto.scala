package com.qrsof.empresalit.invoicing.domain.invoicing.v40.conceptos

import com.qrsof.empresalit.invoicing.domain.conceptos.{ConceptosImpuestos, CuentaPredial, InformacionAduanera}

case class Concepto(
                     claveProductoServicio: String,
                     noIdentificacion: Option[String] = None,
                     cantidad: BigDecimal,
                     claveUnidad: String,
                     unidad: Option[String] = None,
                     descripcion: String,
                     valorUnitario: BigDecimal,
                     importe: BigDecimal,
                     descuento: Option[BigDecimal] = None,
                     objetoImp: String,

                     impuestos: Option[ConceptosImpuestos] = None,
                     aCuentaTerceros: Option[ACuentaTerceros],
                     informacionAduanera: Seq[InformacionAduanera] = Nil,
                     cuentaPredial: Seq[CuentaPredial] = Nil,
                     parte: Seq[Parte] = Nil,
                   )
