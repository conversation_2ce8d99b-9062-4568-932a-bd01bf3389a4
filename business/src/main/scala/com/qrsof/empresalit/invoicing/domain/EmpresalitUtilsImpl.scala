package com.qrsof.empresalit.invoicing.domain

import java.util.{Calendar, Date, UUID}
import jakarta.inject.{Inject, Singleton}

@Singleton
class EmpresalitUtilsImpl @Inject()() extends EmpresalitUtils {
	override def getDate: Date = {
		new Date()
	}

	override def generateKey(): String = UUID.randomUUID().toString

	override def generateStringWithUUID(size: Int): String = {
		val uuid = UUID.randomUUID().toString.replaceAll("-", "")
		uuid.substring(0, size).toUpperCase
	}

	override def getYears(startDate: Date, endDate: Date): Int = {
		if (startDate.before(endDate)) {
			val startDateCalendar = Calendar.getInstance()
			startDateCalendar.setTime(startDate)
			val endDateCalendar = Calendar.getInstance()
			endDateCalendar.get(Calendar.YEAR)

			var years: Int = endDateCalendar.get(Calendar.YEAR) - startDateCalendar.get(Calendar.YEAR)
			if (startDateCalendar.get(Calendar.DAY_OF_YEAR) > endDateCalendar.get(Calendar.DAY_OF_YEAR)) {
				years.-=(1)
			}
			years
		} else {
			0
		}
	}


	override def getDateAddYears(date: Date, years: Int): Date = {
		val calendar = Calendar.getInstance()
		calendar.setTime(date)
		calendar.add(Calendar.YEAR, years)
		calendar.getTime
	}
}
