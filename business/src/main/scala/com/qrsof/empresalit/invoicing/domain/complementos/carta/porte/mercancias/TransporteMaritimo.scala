package com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.mercancias

case class TransporteMaritimo(
                               permSCT: Option[String],
                               numPermisoSCT: Option[String],
                               nombreAseg: Option[String],
                               numPolizaSeguro: Option[String],
                               tipoEmbarcacion: String,
                               matricula: String,
                               numeroOMI: String,
                               anioEmbarcacion: Option[Int],
                               nombreEmbarc: Option[String],
                               nacionalidadEmbarc: String,
                               unidadesDeArqBruto: BigDecimal,
                               tipoCarga: String,
                               numCertITC: String,
                               eslora: Option[BigDecimal],
                               manga: Option[BigDecimal],
                               calado: Option[BigDecimal],
                               lineaNaviera: Option[String],
                               nombreAgenteNaviero: String,
                               numAutorizacionNaviero: String,
                               numViaje: Option[String],
                               numConocEmbarc: Option[String],
                               contenedor: Seq[Contenedor]
                             )
