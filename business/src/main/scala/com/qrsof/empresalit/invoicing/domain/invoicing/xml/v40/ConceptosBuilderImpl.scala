package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.invoicing.domain.invoicing.v40.conceptos.Concepto
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.{CTipoFactor, Comprobante, ObjectFactory}

import jakarta.inject.{Inject, Singleton}

@Singleton
private[xml] class ConceptosBuilderImpl @Inject()() extends ConceptosBuilder {
  override def execute(factory: ObjectFactory, conceptos: Seq[Concepto]): Comprobante.Conceptos = {
    val conceptosNode = factory.createComprobanteConceptos()
    val conceptosNodes = conceptosNode.getConcepto
    conceptos.foreach(concepto => {
      val conceptoNode = factory.createComprobanteConceptosConcepto()
      conceptoNode.setClaveProdServ(concepto.claveProductoServicio)
      concepto.noIdentificacion.foreach(conceptoNode.setNoIdentificacion)
      conceptoNode.setCantidad(concepto.cantidad.bigDecimal)
      conceptoNode.setClaveUnidad(concepto.claveUnidad)
      concepto.unidad.foreach(conceptoNode.setUnidad)
      conceptoNode.setDescripcion(concepto.descripcion)
      conceptoNode.setValorUnitario(concepto.valorUnitario.bigDecimal)
      conceptoNode.setImporte(concepto.importe.bigDecimal)
      concepto.descuento.foreach(des => conceptoNode.setDescuento(des.bigDecimal))
      conceptoNode.setObjetoImp(concepto.objetoImp)
      concepto.impuestos.foreach(conceptoImpuestos => {
        val conceptoImpuestoNode = factory.createComprobanteConceptosConceptoImpuestos()

        if (conceptoImpuestos.retenciones.nonEmpty) {
          val retencionesNode = factory.createComprobanteConceptosConceptoImpuestosRetenciones()
          conceptoImpuestos.retenciones.foreach(retencion => {
            val retencionNode = factory.createComprobanteConceptosConceptoImpuestosRetencionesRetencion()
            retencionNode.setBase(retencion.base.bigDecimal)
            retencionNode.setImpuesto(retencion.impuesto)
            retencionNode.setTipoFactor(CTipoFactor.fromValue(retencion.tipoFactor))
            retencionNode.setTasaOCuota(retencion.tasaCuota.bigDecimal)
            retencionNode.setImporte(retencion.importe.bigDecimal)
            retencionesNode.getRetencion.add(retencionNode)
          })
          conceptoImpuestoNode.setRetenciones(retencionesNode)
        }

        if (conceptoImpuestos.traslados.nonEmpty) {
          val trasladosNode = factory.createComprobanteConceptosConceptoImpuestosTraslados()
          conceptoImpuestos.traslados.foreach(traslado => {
            val trasladoNode = factory.createComprobanteConceptosConceptoImpuestosTrasladosTraslado()
            trasladoNode.setBase(traslado.base.bigDecimal)
            trasladoNode.setImpuesto(traslado.impuesto)
            trasladoNode.setTipoFactor(CTipoFactor.fromValue(traslado.tipoFactor))
            trasladoNode.setTasaOCuota(traslado.tasaCuota.bigDecimal)
            trasladoNode.setImporte(traslado.importe.bigDecimal)
            trasladosNode.getTraslado.add(trasladoNode)
          })
          conceptoImpuestoNode.setTraslados(trasladosNode)
        }

        conceptoNode.setImpuestos(conceptoImpuestoNode)

      })
      concepto.aCuentaTerceros.foreach(act => {
        val aCuentaTerceros = factory.createComprobanteConceptosConceptoACuentaTerceros()
        aCuentaTerceros.setRfcACuentaTerceros(act.rfcACuentaTerceros)
        aCuentaTerceros.setNombreACuentaTerceros(act.nombreACuentaTerceros)
        aCuentaTerceros.setRegimenFiscalACuentaTerceros(act.regimenFiscalACuentaTerceros)
        aCuentaTerceros.setDomicilioFiscalACuentaTerceros(act.domicilioFiscalACuentaTerceros)
        conceptoNode.setACuentaTerceros(aCuentaTerceros)
      })
      if (concepto.informacionAduanera.nonEmpty) {
        val informacionAduaneraNode = conceptoNode.getInformacionAduanera
        concepto.informacionAduanera.foreach(ia => {
          val informacionAduanera = factory.createComprobanteConceptosConceptoInformacionAduanera()
          informacionAduanera.setNumeroPedimento(ia.numeroPedimento)
          informacionAduaneraNode.add(informacionAduanera)
        })
      }
      if (concepto.cuentaPredial.nonEmpty) {
        val cuentaPredials = conceptoNode.getCuentaPredial
        concepto.cuentaPredial.foreach(cp => {
          val cuentaPredial = factory.createComprobanteConceptosConceptoCuentaPredial()
          cuentaPredial.setNumero(cp.numero)
          cuentaPredials.add(cuentaPredial)
        })
      }
      concepto.parte.foreach(parte => {
        val partesNode = conceptoNode.getParte
        val parteNode = factory.createComprobanteConceptosConceptoParte()
        parteNode.setClaveProdServ(parte.claveProdServ)
        parte.noIdentificacion.foreach(parteNode.setNoIdentificacion)
        parteNode.setCantidad(parte.cantidad.bigDecimal)
        parte.unidad.foreach(parteNode.setUnidad)
        parteNode.setDescripcion(parte.descripcion)
        parte.valorUnitario.foreach(v => parteNode.setValorUnitario(v.bigDecimal))
        parte.importe.foreach(i => parteNode.setImporte(i.bigDecimal))
        parte.informacionAduanera.foreach(ia => {
          val informacionAduaneraNodeList = parteNode.getInformacionAduanera
          val informacionAduaneraNode = factory.createComprobanteConceptosConceptoParteInformacionAduanera()
          informacionAduaneraNode.setNumeroPedimento(ia.numeroPedimento)
          informacionAduaneraNodeList.add(informacionAduaneraNode)
        })


        partesNode.add(parteNode)
      })

      conceptosNodes.add(conceptoNode)
    })
    conceptosNode
  }
}
