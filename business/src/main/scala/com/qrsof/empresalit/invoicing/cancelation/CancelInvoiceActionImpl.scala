package com.qrsof.empresalit.invoicing.cancelation

import com.qrsof.empresalit.companies.CompanyGateway
import jakarta.inject.{Inject, Singleton}

@Singleton
class CancelInvoiceActionImpl @Inject() (
    companyGateway: CompanyGateway
) extends CancelInvoiceAction {
  override def execute(request: InvoiceCancelationRequest): InvoiceCancelationResponse = {
    companyGateway.getCompanyCertificateByCompanyKey(request.companyKey).get
    InvoiceCancelationResponse("", "")
  }

}
