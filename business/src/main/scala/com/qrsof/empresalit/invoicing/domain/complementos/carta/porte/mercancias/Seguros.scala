package com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.mercancias

case class Seguros(
                    aseguraRespCivil: String,
                    polizaRespCivil: String,
                    aseguraMedAmbiente: Option[String] = None,
                    polizaMedAmbiente: Option[String] = None,
                    aseguraCarga: Option[String] = None,
                    polizaCarga: Option[String] = None,
                    primaSeguro: Option[BigDecimal] = None
                  )
