package com.qrsof.empresalit.invoicing.generateinvoice

import com.qrsof.empresalit.companies.CompanyGateway
import com.qrsof.empresalit.invoicing.domain.invoicing
import com.qrsof.empresalit.invoicing.domain.invoicing.IssuerInvoicingData

import jakarta.inject.{Inject, Singleton}

@Singleton
class GenerateInvoiceProxyImpl @Inject()(companyDao: CompanyGateway, privateKeyFactory: PrivateKeyFactory) extends GenerateInvoiceProxy {
  override def getIssuerInvoicingDataByCompanyKey(companyKey: String): IssuerInvoicingData = {
    val company = companyDao.getCompanyByKey(companyKey).get
    val companyCertificate = companyDao.getCompanyCertificateByCompanyKey(companyKey).get
    invoicing.IssuerInvoicingData(
      rfc = company.rfc,
      name = company.name,
      regimenFiscalClave = company.regimenFiscalClave,
      certificateNumber = companyCertificate.noCertificado,
      publicKey = companyCertificate.publicKey,
      privateKey = privateKeyFactory.getInstance(companyCertificate.privateKey, companyCertificate.password)
    )
  }
}
