package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.invoicing.domain.invoicing.v40.conceptos.Concepto
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Comprobante.Conceptos
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.ObjectFactory

private[xml] trait ConceptosBuilder {
  def execute(factory: ObjectFactory, conceptos: Seq[Concepto]): Conceptos
}
