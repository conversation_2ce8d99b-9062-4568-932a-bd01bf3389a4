package com.qrsof.empresalit.invoicing.domain.complementos.pagos

import com.qrsof.empresalit.invoicing.domain.impuestos.Impuestos

case class Pago(
                 fechaPago: String,
                 formaDePagoP: String,
                 monedaP: String,
                 tipoCambioP: Option[BigDecimal] = None,
                 monto: BigDecimal,
                 numOperacion: Option[String] = None,
                 rfcEmisorCtaOrd: Option[String] = None,
                 ctaOrdenante: Option[String] = None,
                 nomBancoOrdExt: Option[String] = None,
                 rfcEmisorCtaBen: Option[String] = None,
                 ctaBeneficiario: Option[String] = None,
                 tipoCadPago: Option[String] = None,
                 certPago: Option[String] = None,
                 cadPago: Option[String] = None,
                 selloPago: Option[String] = None,
                 doctosRelacionesdos: Seq[DoctoRelacionado] = Nil,
                 impuestos: Option[Impuestos] = None
               )
