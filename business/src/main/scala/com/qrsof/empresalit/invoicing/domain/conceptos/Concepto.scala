package com.qrsof.empresalit.invoicing.domain.conceptos

case class Concepto(
                     claveProductoServicio: String,
                     noIdentificacion: Option[String] = None,
                     cantidad: BigDecimal,
                     claveUnidad: String,
                     unidad: Option[String] = None,
                     descripcion: String,
                     valorUnitario: BigDecimal,
                     importe: BigDecimal,
                     descuento: Option[BigDecimal] = None,
                     impuestos: Option[ConceptosImpuestos] = None,
                     informacionAduanera: Seq[InformacionAduanera] = Nil,
                     cuentaPredial: Option[CuentaPredial] = None,
                   )
