package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.invoicing.IssuerInvoicingData
import com.qrsof.empresalit.invoicing.domain.invoicing.v30.GenerateInvoiceRequest
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.Comprobante

private[xml] trait ComprobanteBinder {
  def execute(issuerInvoicingData: IssuerInvoicingData, generateInvoiceRequest: GenerateInvoiceRequest): Comprobante
}
