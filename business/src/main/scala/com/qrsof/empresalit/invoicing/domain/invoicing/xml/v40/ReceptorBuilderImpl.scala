package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.invoicing.domain.invoicing.v40
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.{<PERSON><PERSON>, Comprobante, ObjectFactory}

import jakarta.inject.{Inject, Singleton}

@Singleton
private[xml] class ReceptorBuilderImpl @Inject()(usoCfdiFactory: UsoCfdiFactory) extends ReceptorBuilder {
  override def execute(factory: ObjectFactory, receptor: v40.ReceptorInvoice): Comprobante.Receptor = {
    val receptorNode = factory.createComprobanteReceptor()
    receptorNode.setRfc(receptor.rfc)
    receptorNode.setNombre(receptor.nombre)
    receptorNode.setDomicilioFiscalReceptor(receptor.domicilioFiscalReceptor)
    receptor.residenciaFiscal.foreach(rf => receptorNode.setResidenciaFiscal(CPais.fromValue(rf)))
    receptor.numRegIdTrib.foreach(receptorNode.setNumRegIdTrib)
    receptorNode.setRegimenFiscalReceptor(receptor.regimenFiscalReceptor)
    receptorNode.setUsoCFDI(usoCfdiFactory.getInstance(receptor.usoCfdi))
    receptorNode
  }
}
