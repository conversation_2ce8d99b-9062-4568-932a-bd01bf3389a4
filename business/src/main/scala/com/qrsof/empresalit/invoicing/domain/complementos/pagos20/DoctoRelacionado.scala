package com.qrsof.empresalit.invoicing.domain.complementos.pagos20

case class DoctoRelacionado(
                             idDocumento: String,
                             serie: Option[String] = None,
                             folio: Option[String] = None,
                             monedaDR: String,
                             equivalenciaDR: Option[BigDecimal] = None,
                             numParcialidad: BigInt,
                             impSaldoAnt: BigDecimal,
                             impPagado: BigDecimal,
                             impSaldoInsoluto: BigDecimal,
                             objetoImpDR: String,
                             impuestosDR: Option[ImpuestosDR] = None
                           )
