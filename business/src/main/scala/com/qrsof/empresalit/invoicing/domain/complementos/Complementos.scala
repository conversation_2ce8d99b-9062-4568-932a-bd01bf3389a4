package com.qrsof.empresalit.invoicing.domain.complementos

import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.CartaPorte
import com.qrsof.empresalit.invoicing.domain.complementos.nomina.NominaComp
import com.qrsof.empresalit.invoicing.domain.complementos.pagos.RecepcionPago

case class Complementos(
                         recepcionPagos: Option[RecepcionPago] = None,
                         recepcionPagos20: Option[pagos20.RecepcionPago] = None,
                         cartaPorte20: Option[CartaPorte] = None,
                         nomina12: Option[NominaComp] = None
                       )
