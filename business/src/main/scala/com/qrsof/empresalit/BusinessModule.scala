package com.qrsof.empresalit

import com.google.inject.{AbstractModule, Provides}
import com.qrsof.apptack.client.apps.accounts.{AccountOpts, AccountOptsImpl}
import com.qrsof.apptack.client.apps.users.{UsersOpts, UsersOptsImpl}
import com.qrsof.apptack.client.{ApplicationCredentialsAuthentication, ApptackGuiceModule, Authentication}
import com.qrsof.core.certificates.{CertificateUtils, CertificateUtilsImpl}
import com.qrsof.core.storage.filesystem.FilesystemStorageGuiceModule
import com.qrsof.empresalit.companies.actions.{SaveNewCertificateAction, SaveNewCertificateActionImpl}
import com.qrsof.empresalit.companies.{CompanyCertificateMemoryStorage, CompanyCertificateStorage}
import com.qrsof.empresalit.domain.brand_company.actions.{BrandCompanyActions, BrandCompanyActionsImpl}
import com.qrsof.empresalit.domain.company.actions.ss3.{StorageCertificatesAction, StorageCertificatesActionImpl}
import com.qrsof.empresalit.domain.company.actions.{CompanyActions, CompanyActionsImpl}
import com.qrsof.empresalit.domain.holidays.actions.{HoliDaysActions, HoliDaysActionsImpl}
import com.qrsof.empresalit.domain.logbook.{LogbookService, LogbookServiceImpl}
import com.qrsof.empresalit.domain.resources.{ResourcesService, ResourcesServiceImpl}
import com.qrsof.empresalit.domain.sendemail.{SendEmailBusiness, SendEmailBusinessImpl, SendEmailUtils, SendEmailUtilsImpl}
import com.qrsof.empresalit.domain.seniority_benefits.{SeniorityBenefitsService, SeniorityBenefitsServiceImpl}
import com.qrsof.empresalit.domain.status.{StatusService, StatusServiceImpl}
import com.qrsof.empresalit.domain.storage.{StorageGateway, StorageGatewayImpl}
import com.qrsof.empresalit.domain.user_oauth.{UserOAuthService, UserOAuthServiceImpl}
import com.qrsof.empresalit.invoicing.actions.generateqr.{GenerateQrAction, GenerateQrActionImpl}
import com.qrsof.empresalit.invoicing.actions.getinvoicestatus.{GetInvoiceStatusAction, GetInvoiceStatusActionImpl}
import com.qrsof.empresalit.invoicing.actions.retreivemetadata.{RetreiveInvoiceMetadataAction, RetreiveInvoiceMetadataActionImpl}
import com.qrsof.empresalit.invoicing.cancelation.{CancelInvoiceAction, CancelInvoiceActionImpl}
import com.qrsof.empresalit.invoicing.domain.invoicing.xml.InvoiceXmlGeneratorModule
import com.qrsof.empresalit.invoicing.domain.{EmpresalitUtils, EmpresalitUtilsImpl}
import com.qrsof.empresalit.invoicing.generateinvoice.*
import com.qrsof.empresalit.services.email.{EmailService, EmailServiceImpl}
import com.qrsof.empresalit.services.qrcode.{QrCodeService, QrCodeServiceImpl}
import com.qrsof.empresalit.views.maincontainer.actions.{ViewMainContainerActions, ViewMainContainerActionsImpl}
import com.qrsof.empresalit.views.maincontainer.clients.actions.{ViewClientsActions, ViewClientsActionsImpl}
import com.qrsof.empresalit.views.maincontainer.companies.action.{ViewCompaniesAction, ViewCompaniesActionImpl}
import com.qrsof.empresalit.views.maincontainer.debtsReceivables.actions.{ViewDebtsReceivableActions, ViewDebtsReceivableActionsImpl}
import com.qrsof.empresalit.views.maincontainer.debtsToPay.actions.{ViewDebtToPayActions, ViewDebtToPayActionsImpl}
import com.qrsof.empresalit.views.maincontainer.employee.actions.{ViewEmployeeActions, ViewEmployeeActionsImpl}
import com.qrsof.empresalit.views.maincontainer.jobs.actions.{ViewJobsActions, ViewJobsActionsImpl}
import com.qrsof.empresalit.views.maincontainer.kanbanBoard.actions.{ViewKanbanBoardActions, ViewKanbanBoardActionsImpl, ViewTaskConfirmationActions, ViewTaskConfirmationActionsImpl}
import com.qrsof.empresalit.views.maincontainer.presenteeism.{ViewPresenteeismActions, ViewPresenteeismActionsImpl}
import com.qrsof.empresalit.views.maincontainer.quote.actions.{ViewQuoteActions, ViewQuoteActionsImpl}
import com.qrsof.empresalit.views.maincontainer.sales.receptionofequipments.{ViewReceptionOfEquipmentActions, ViewReceptionOfEquipmentActionsImpl}
import com.qrsof.empresalit.views.maincontainer.stores.actions.{ViewStoreActions, ViewStoreActionsImpl}
import com.qrsof.empresalit.views.maincontainer.suppliers.actions.{ViewSuppliersActions, ViewSuppliersActionsImpl}
import com.qrsof.empresalit.views.onboarding.actions.{ViewCompanyActions, ViewCompanyActionsImpl}
import com.qrsof.jwt.validation.{JwtValidationService, JwtValidationServiceImpl, RSAJwksKeyProvider, RSAKeyProvider}
import com.qrsof.core.storage.digitalocean.DigitalOceanStorageGuiceModule
import jakarta.inject.Singleton
import net.codingwell.scalaguice.ScalaModule

class BusinessModule(appConfigurations: AppConfigurations, apptackUrl: Option[String]) extends AbstractModule with ScalaModule {

	override def configure(): Unit = {
		bind[JwtValidationService].to[JwtValidationServiceImpl].in(classOf[Singleton])
		bind[RSAKeyProvider].to[RSAJwksKeyProvider].in(classOf[Singleton])
		install(new InvoiceXmlGeneratorModule)
		bind[EmpresalitUtils].to[EmpresalitUtilsImpl]
		bind[CompanyCertificateStorage].to[CompanyCertificateMemoryStorage]
		bind[PrivateKeyFactory].to[PrivateKeyFactoryImpl]
		bind[GenerateInvoiceAction].to[GenerateInvoiceActionImpl]
		bind[v40.GenerateInvoiceAction].to[v40.GenerateInvoiceActionImpl]
		bind[GenerateInvoiceProxy].to[GenerateInvoiceProxyImpl]
		bind[SaveNewCertificateAction].to[SaveNewCertificateActionImpl]
		bind[CertificateUtils].to[CertificateUtilsImpl]
		bind[CancelInvoiceAction].to[CancelInvoiceActionImpl]
		bind[RetreiveInvoiceMetadataAction].to[RetreiveInvoiceMetadataActionImpl]
		bind[GetInvoiceStatusAction].to[GetInvoiceStatusActionImpl]
		bind[GenerateQrAction].to[GenerateQrActionImpl]
		bind[QrCodeService].to[QrCodeServiceImpl]
		bind[CompanyActions].to[CompanyActionsImpl].in(classOf[Singleton])
		bind[StorageCertificatesAction].to[StorageCertificatesActionImpl].in(classOf[Singleton])
		bind[ViewCompanyActions].to[ViewCompanyActionsImpl].in(classOf[Singleton])
		bind[ViewMainContainerActions].to[ViewMainContainerActionsImpl].in(classOf[Singleton])
		bind[ViewDebtToPayActions].to[ViewDebtToPayActionsImpl].in(classOf[Singleton])
		bind[ViewSuppliersActions].to[ViewSuppliersActionsImpl].in(classOf[Singleton])
		bind[ViewDebtsReceivableActions].to[ViewDebtsReceivableActionsImpl].in(classOf[Singleton])
		bind[ViewClientsActions].to[ViewClientsActionsImpl].in(classOf[Singleton])
		bind[UsersOpts].to[UsersOptsImpl].in(classOf[Singleton])
		bind[AccountOpts].to[AccountOptsImpl].in(classOf[Singleton])
		install(
			new ApptackGuiceModule(production = appConfigurations.environment.equals(AppEnvironment.Production), apptackUrl)
		)
		bind[EmailService].to[EmailServiceImpl].in(classOf[Singleton])
		bind[ViewCompaniesAction].to[ViewCompaniesActionImpl].in(classOf[Singleton])
		bind[ViewEmployeeActions].to[ViewEmployeeActionsImpl].in(classOf[Singleton])
		bind[ViewJobsActions].to[ViewJobsActionsImpl].in(classOf[Singleton])
		bind[ViewStoreActions].to[ViewStoreActionsImpl].in(classOf[Singleton])
		bind[ViewQuoteActions].to[ViewQuoteActionsImpl].in(classOf[Singleton])
		bind[ViewReceptionOfEquipmentActions].to[ViewReceptionOfEquipmentActionsImpl]
		bind[ResourcesService].to[ResourcesServiceImpl]
		bind[SendEmailUtils].to[SendEmailUtilsImpl]
		bind[SendEmailBusiness].to[SendEmailBusinessImpl]
		bind[ViewKanbanBoardActions].to[ViewKanbanBoardActionsImpl].in(classOf[Singleton])
		bind[ViewTaskConfirmationActions].to[ViewTaskConfirmationActionsImpl].in(classOf[Singleton])
		bind[StatusService].to[StatusServiceImpl].in(classOf[Singleton])
		bind[LogbookService].to[LogbookServiceImpl].in(classOf[Singleton])
		bind[UserOAuthService].to[UserOAuthServiceImpl].in(classOf[Singleton])
		bind[SeniorityBenefitsService].to[SeniorityBenefitsServiceImpl].in(classOf[Singleton])
		bind[ViewPresenteeismActions].to[ViewPresenteeismActionsImpl].in(classOf[Singleton])
		bind[BrandCompanyActions].to[BrandCompanyActionsImpl].in(classOf[Singleton])
		bind[StorageGateway].to[StorageGatewayImpl].in(classOf[Singleton])
		bind[HoliDaysActions].to[HoliDaysActionsImpl].in(classOf[Singleton])

		appConfigurations.environment match {
			case AppEnvironment.Production =>
				install(new DigitalOceanStorageGuiceModule())
			case AppEnvironment.Sandbox =>
				install(new DigitalOceanStorageGuiceModule())
			case AppEnvironment.Development =>
				install(new FilesystemStorageGuiceModule())
			case AppEnvironment.Test =>
				install(new FilesystemStorageGuiceModule())
			case AppEnvironment.CI =>
				install(new FilesystemStorageGuiceModule())
		}
	}

	@Provides
	def applicationCredentialsAuthentication: ApplicationCredentialsAuthentication = {
		ApplicationCredentialsAuthentication(appKey = appConfigurations.apptackKey, appSecret = appConfigurations.apptackSecret)
	}

	private val applicationCredentials = ApplicationCredentialsAuthentication(appKey = appConfigurations.apptackKey, appSecret = appConfigurations.apptackSecret)

	@Provides
	def provideAuthentication: Authentication = applicationCredentials
}
