package com.qrsof.empresalit.services.qrcode

import org.apache.commons.io.FileUtils
import org.scalatest.freespec.PathAnyFreeSpec

import java.io.File

class QrCodeServiceImplTest extends PathAnyFreeSpec {

  "Generate QRCode image" in {
    val qrCodeServiceImpl = new QrCodeServiceImpl

    val bytes = qrCodeServiceImpl.generatePngImage(
      content = "https://prodretencionverificacion.clouda.sat.gob.mx?id=ad662d33-6934-459c-a128-BDf0393f0f44&re=XAXX010101000&rr=XAXX010101000&tt=1234567890.123456&fe=rH8/bw==",
      width = 400,
      height = 400
    )

    FileUtils.writeByteArrayToFile(new File("testqr.png"), bytes.readAllBytes())

  }

}
