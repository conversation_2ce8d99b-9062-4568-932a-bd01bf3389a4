package com.qrsof.empresalit.companies.actions

import com.qrsof.core.certificates.CertificateUtils
import com.qrsof.empresalit.companies.{CompanyCertificate, CompanyGateway, SaveNewCompanyCertificateRequest}
import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import org.mockito.ArgumentCaptor
import org.mockito.Mockito.{verify, when}
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import java.math.BigInteger
import java.security.cert.X509Certificate

class SaveNewCertificateActionImplSpec extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "SeveNewCertificateActionImpl" - {
    val empresalitUtils = mock[EmpresalitUtils]
    val companyGateway = mock[CompanyGateway]
    val certificateUtils = mock[CertificateUtils]
    val x509Certificate = mock[X509Certificate]
    val seveNewCertificateActionImpl = new SaveNewCertificateActionImpl(empresalitUtils, companyGateway, certificateUtils)

    val companyKey = "companyKey"
    val noCertificado = "01020102012010210010210"
    val privateKey = "privateKey".getBytes
    val password = "password"
    val publicKey = "publicKey".getBytes
    val generatedKey = "generatedkey"

    when(empresalitUtils.generateKey()) thenReturn generatedKey
    when(certificateUtils.parseCertificateX509DER(publicKey)) thenReturn x509Certificate
    when(x509Certificate.getSerialNumber) thenReturn new BigInteger(noCertificado.getBytes)
    "when: save company certificate" - {
      seveNewCertificateActionImpl.execute(
        SaveNewCompanyCertificateRequest(
          companyKey = companyKey,
          privateKey = privateKey,
          password = password,
          publicKey = publicKey
        )
      )
      "then: generate certificate key" in {
        verify(empresalitUtils).generateKey()
      }

      "and: decode certificate" in {
        verify(certificateUtils).parseCertificateX509DER(publicKey)
      }

      "and: save company certificate in system" in {
        val eventCaptor = ArgumentCaptor.forClass(classOf[CompanyCertificate])

        verify(companyGateway).saveCompanyCertificate(eventCaptor.capture())
        val value = eventCaptor.getValue

        value.key shouldEqual generatedKey
        value.companyKey shouldEqual companyKey
        value.noCertificado shouldEqual noCertificado
        value.privateKey shouldEqual privateKey
        value.password shouldEqual password
        value.publicKey shouldEqual publicKey
      }
    }
  }

}
