package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import org.mockito.Mockito
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import java.util.Calendar

class XmlTimeUtilsImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "XmlCurrentTimeImpl" - {
    val empreslitUtils = mock[EmpresalitUtils]
    val xmlCurrentTimeImpl = new XmlTimeUtilsImpl(empreslitUtils)
    val calendar = Calendar.getInstance()
    calendar.set(Calendar.MILLISECOND, 0)
    val date = calendar.getTime
    Mockito.when(empreslitUtils.getDate) thenReturn date
    "execute" in {
      val xMLGregorianCalendar = xmlCurrentTimeImpl.getCurrentDate()
      xMLGregorianCalendar.toGregorianCalendar.getTime.getTime shouldEqual date.getTime
    }
  }
}
