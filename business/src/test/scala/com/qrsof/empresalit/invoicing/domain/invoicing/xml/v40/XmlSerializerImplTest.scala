package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.PathAnyFreeSpecAppTest
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.Comprobante
import org.slf4j.LoggerFactory

class XmlSerializerImplTest extends PathAnyFreeSpecAppTest {
  private val logger = LoggerFactory.getLogger(classOf[XmlSerializerImplTest])

  "XmlSerializerImpl" - {
    val xmlSerializerImpl = new XmlSerializerImpl
    "execute" in {
      val comprobante = new Comprobante
      val xml = xmlSerializerImpl.execute(comprobante)
      val xmlString = new String(xml)
      logger.info("XML: {}", xmlString)
      xmlString shouldNot be(null)
    }
  }
}
