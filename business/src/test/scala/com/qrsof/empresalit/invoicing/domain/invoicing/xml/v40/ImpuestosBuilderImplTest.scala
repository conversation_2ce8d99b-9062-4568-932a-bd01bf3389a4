package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.PathAnyFreeSpecAppTest
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.impuestos.{Impuestos, Retencion, Traslado}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.{CTipoFactor, ObjectFactory}

class ImpuestosBuilderImplTest extends PathAnyFreeSpecAppTest {

  "ImpuestosBuilderImpl" - {
    val impuestosBuilderImpl = new ImpuestosBuilderImpl
    "execute" in {
      val objectFactory = new ObjectFactory
      val impTraslado = BigDecimal(1)
      val impRetenido = BigDecimal(2)
      val impuestos = Impuestos(
        totalImpuestosTrasladados = Some(impTraslado),
        totalImpuestosRetenidos = Some(impRetenido),
        retenciones = Some(Seq(Retencion("iva", impRetenido))),
        traslados = Some(Seq(Traslado(
          base = BigDecimal(1),
          impuesto = "iva",
          tipoFactor = "Tasa",
          tasaOCuota = Some(BigDecimal("0.16000")),
          importe = Some(impTraslado)
        )))
      )
      val impuestosResult = impuestosBuilderImpl.execute(objectFactory, impuestos)
      impuestosResult.getTotalImpuestosRetenidos.floatValue() shouldEqual impRetenido.floatValue
      impuestosResult.getTotalImpuestosTrasladados.floatValue() shouldEqual impTraslado.floatValue
      impuestosResult.getTraslados.getTraslado.get(0).getImporte.floatValue() shouldEqual impTraslado.floatValue
      impuestosResult.getTraslados.getTraslado.get(0).getImpuesto shouldEqual "iva"
      impuestosResult.getTraslados.getTraslado.get(0).getTasaOCuota.floatValue() shouldEqual BigDecimal("0.16000").floatValue
      impuestosResult.getTraslados.getTraslado.get(0).getTipoFactor shouldEqual CTipoFactor.fromValue("Tasa")
      impuestosResult.getTraslados.getTraslado.get(0).getBase.toString shouldEqual "1"
      impuestosResult.getRetenciones.getRetencion.get(0).getImporte.doubleValue() shouldEqual impRetenido.doubleValue
      impuestosResult.getRetenciones.getRetencion.get(0).getImpuesto shouldEqual "iva"
    }
  }

}
