package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.EmpresalitUtils
import com.qrsof.empresalit.invoicing.domain.conceptos.Concepto
import com.qrsof.empresalit.invoicing.domain.invoicing.IssuerInvoicingData
import com.qrsof.empresalit.invoicing.domain.invoicing.v30.GenerateInvoiceRequest
import com.qrsof.empresalit.invoicing.generateinvoice.ReceptorInvoice
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.ObjectFactory
import org.mockito.Mockito.{verify, when}
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import java.security.PrivateKey
import java.util.Date

class InvoiceXmlGeneratorImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "InvoiceXmlGeneratorImpl" - {


    val privateKey = mock[PrivateKey]
    val signOriginalChain = mock[SignOriginalChain]
    val originalChainBuilder = mock[OriginalChainBuilder]
    val empreslitUtils = mock[EmpresalitUtils]
    val xmlSerializer = mock[XmlSerializer]
    val fixNameSpacesXml = mock[FixNameSpacesXml]

    val comprobanteBinder = mock[ComprobanteBinder]

    val emisorKey = "emisorKey"

    val factory = new ObjectFactory
    val comprobante = factory.createComprobante()

    val issuerInvoicingData = IssuerInvoicingData(
      certificateNumber = "nocertificado",
      privateKey = privateKey,
      publicKey = "publickey".getBytes,
      rfc = "rfc",
      name = "razonsocial",
      regimenFiscalClave = "regFiscal"
    )
    val generateInvoiceRequest = GenerateInvoiceRequest(
      version = "3.3",
      companyKey = emisorKey,
      serie = Some("A"),
      folio = Some("1"),
      lugarExpedicion = "42083",
      formaPago = Some("FP"),
      metodoPago = Some("PUE"),
      moneda = "MXN",
      tipoComprobante = "I",
      subTotal = BigDecimal(1),
      total = BigDecimal(2),
      receptor = Some(ReceptorInvoice(
        rfc = "AAA010101AAA",
        usoCfdi = "Gastos en general"
      )),
      conceptos = Seq(
        Concepto(
          claveProductoServicio = "clave",
          cantidad = BigDecimal(1),
          claveUnidad = "unidad",
          valorUnitario = BigDecimal(2),
          descripcion = "descripcion",
          importe = BigDecimal(2)
        )
      )
    )

    val date = new Date()
    when(empreslitUtils.getDate) thenReturn date

    val invoicingGeneratorImpl = new InvoiceXmlGeneratorImpl(signOriginalChain,
      originalChainBuilder, null,
      comprobanteBinder,
      null,
      xmlSerializer,
      null,
      fixNameSpacesXml,
      null
    )

    when(comprobanteBinder.execute(issuerInvoicingData, generateInvoiceRequest)) thenReturn comprobante
    val expectedXml = "xml".getBytes
    val expectedFixedNameSpaceXml = "fixedxml".getBytes
    val originalChain = "originalchain".getBytes
    when(originalChainBuilder.execute(comprobante)) thenReturn originalChain
    when(xmlSerializer.execute(comprobante)) thenReturn expectedXml
    when(fixNameSpacesXml.execute(expectedXml, generateInvoiceRequest)) thenReturn expectedFixedNameSpaceXml

    "when: generate xml invoice" - {
      val xml = invoicingGeneratorImpl.getXml(issuerInvoicingData, generateInvoiceRequest)

      "should: bind comprobante" in {
        verify(comprobanteBinder).execute(issuerInvoicingData, generateInvoiceRequest)
      }

      "and: get original chain" in {
        verify(originalChainBuilder).execute(comprobante)
      }

      "and: sign original chain" in {
        verify(signOriginalChain).execute("originalchain".getBytes, issuerInvoicingData.privateKey)
      }

      "and generate xml from comprobante model" in {
        verify(xmlSerializer).execute(comprobante)
        xml shouldEqual expectedFixedNameSpaceXml
      }

      "and fix namespace schema locations" in {
        verify(fixNameSpacesXml).execute(expectedXml, generateInvoiceRequest)
        xml shouldEqual expectedFixedNameSpaceXml
      }

    }
  }

}
