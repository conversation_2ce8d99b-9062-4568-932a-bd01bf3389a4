package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.sat.models.cfdi33.CUsoCFDI
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class UsoCfdiFactoryImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "UsoCfdiFactoryImpl" - {
    "getInstance" in {
      val usoCfdiFactoryImpl = new UsoCfdiFactoryImpl
      usoCfdiFactoryImpl.getInstance("G01") shouldEqual CUsoCFDI.G_01
    }
  }
}
