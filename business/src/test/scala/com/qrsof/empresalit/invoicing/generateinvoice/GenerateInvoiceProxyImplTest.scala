package com.qrsof.empresalit.invoicing.generateinvoice

import com.qrsof.empresalit.companies.{Company, CompanyCertificate, CompanyGateway}
import org.mockito.Mockito
import org.mockito.Mockito.when
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import java.security.PrivateKey

class GenerateInvoiceProxyImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "Get ceritificate data for generating invoice" - {
    val companyKey = "company-key"
    val companyName = "company-name"
    val companyRfc = "company-rfc"
    val regimenFiscalClave = "company-regimenFiscalClave"

    val certificateNumber = "certificateNumber"
    val privateCertificateKey = "privateCertificateKey"
    val password = "password"
    val publicKey = "publicKey"
    val certificate = CompanyCertificate(
      noCertificado = certificateNumber,
      privateKey = privateCertificateKey.getBytes,
      password = password,
      publicKey = publicKey.getBytes,
      key = "key",
      companyKey = companyKey
    )

    val privateKey = mock[PrivateKey]

    val companyDao = mock[CompanyGateway]
    val privateKeyFactory = mock[PrivateKeyFactory]
    val generateInvoiceProxyImpl = new GenerateInvoiceProxyImpl(companyDao, privateKeyFactory)
    "GIVEN: company exists" - {
      when(companyDao.getCompanyByKey(companyKey)) thenReturn Some(Company(
        key = companyKey,
        name = companyName,
        rfc = companyRfc,
        regimenFiscalClave = regimenFiscalClave,
      ))

      "AND: certificate exists" - {
        when(companyDao.getCompanyCertificateByCompanyKey(companyKey)) thenReturn Some(certificate)

        "WHEN: execute" - {

          when(privateKeyFactory.getInstance(certificate.privateKey, certificate.password)) thenReturn privateKey

          val issuerInvoicingData = generateInvoiceProxyImpl.getIssuerInvoicingDataByCompanyKey(companyKey)

          "THEN: get company from db" in {
            Mockito.verify(companyDao).getCompanyByKey(companyKey)
          }

          "AND: get certificate date from db" in {
            Mockito.verify(companyDao).getCompanyCertificateByCompanyKey(companyKey)
          }

          "AND: get fiscal name" in {
            issuerInvoicingData.name shouldEqual companyName
          }

          "AND: get RFC" in {
            issuerInvoicingData.rfc shouldEqual companyRfc
          }

          "AND: get regimen fiscal key" in {
            issuerInvoicingData.regimenFiscalClave shouldEqual regimenFiscalClave
          }

          "AND: get certificate number" in {
            issuerInvoicingData.certificateNumber shouldEqual certificateNumber
          }

          "AND: get public certificate" in {
            issuerInvoicingData.publicKey shouldEqual publicKey.getBytes
          }

          "AND: generate private key from certificate data" in {
            Mockito.verify(privateKeyFactory).getInstance(certificate.privateKey, certificate.password)
          }

          "AND: get private certificate key" in {
            issuerInvoicingData.privateKey shouldEqual privateKey
          }

        }
      }
    }
  }

}
