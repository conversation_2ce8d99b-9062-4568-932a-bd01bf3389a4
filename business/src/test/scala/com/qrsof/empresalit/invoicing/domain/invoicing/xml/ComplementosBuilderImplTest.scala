package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.EmpresalitUtilsImpl
import com.qrsof.empresalit.invoicing.domain.complementos.Complementos
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.figura.transporte.{DomicilioFigura, PartesTransporte, TiposFigura}
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.mercancias.*
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.ubicaciones.{Domicilio, Ubicacion}
import com.qrsof.empresalit.invoicing.domain.complementos.carta.porte.{CartaPorte, Figuratransporte, Mercancias, Ubicaciones}
import com.qrsof.empresalit.invoicing.domain.complementos.nomina.*
import com.qrsof.empresalit.invoicing.domain.complementos.pagos.{DoctoRelacionado, Pago, RecepcionPago}
import com.qrsof.empresalit.invoicing.domain.impuestos.{Impuestos, Retencion, Traslado}
import com.qrsof.empresalit.invoicing.sat.models.cfdi33
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.*
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers


class ComplementosBuilderImplTest extends PathAnyFreeSpec with Matchers {

  "Build complementos" - {
    "Complemento de pago" - {
      val complementos = Complementos(
        recepcionPagos = Some(RecepcionPago(
          version = "1.0",
          Seq(
            Pago(
              fechaPago = "2021-04-09T23:25:42",
              formaDePagoP = "01",
              monedaP = "MXN",
              monto = BigDecimal("12768.00"),
              doctosRelacionesdos = Seq(
                DoctoRelacionado(
                  idDocumento = "IdDocumento",
                  serie = Some("Serie"),
                  folio = Some("Folio"),
                  monedaDR = "MXN",
                  tipoCambioDR = Some(BigDecimal("20.00")),
                  metodoDePagoDR = "PPD",
                  numParcialidad = Some(9),
                  impSaldoAnt = Some(BigDecimal("45.52")),
                  impPagado = Some(BigDecimal("42.52")),
                  impSaldoInsoluto = Some(BigDecimal("44.52")),
                )
              ),
              tipoCambioP = Some(BigDecimal(10)),
              numOperacion = Some("numoperacion"),
              rfcEmisorCtaOrd = Some("RfcEmisorCtaOrd"),
              nomBancoOrdExt = Some("NomBancoOrdExt"),
              ctaOrdenante = Some("CtaOrdenante"),
              rfcEmisorCtaBen = Some("RfcEmisorCtaBen"),
              ctaBeneficiario = Some("CtaBeneficiario"),
              tipoCadPago = Some("TipoCadPago"),
              certPago = Some("CertPago"),
              cadPago = Some("CadPago"),
              selloPago = Some("SelloPago"),
              impuestos = Some(
                Impuestos(
                  totalImpuestosRetenidos = Some(BigDecimal("23.23")),
                  totalImpuestosTrasladados = Some(BigDecimal("24.24")),
                  retenciones = Some(
                    Seq(
                      Retencion(
                        impuesto = "01",
                        importe = BigDecimal("45.12")
                      )
                    )
                  ),
                  traslados = Some(
                    Seq(
                      Traslado(
                        impuesto = "02",
                        importe = BigDecimal("591.48"),
                        tipoFactor = "TASA",
                        tasaOCuota = BigDecimal("16.0000")
                      )
                    )
                  )
                )
              )
            )
          )
        ))
      )

      val complementosBuilderImpl = new ComplementosBuilderImpl(new XmlTimeUtilsImpl(new EmpresalitUtilsImpl))
      val complementosInvoice: Comprobante.Complemento = complementosBuilderImpl.execute(new ObjectFactory(), complementos)
      val pagos = complementosInvoice.getAny.get(0).asInstanceOf[Pagos]

      "version" in {
        pagos.getVersion shouldEqual "1.0"
      }

      "forma de pago" in {
        pagos.getPago.get(0).getFormaDePagoP shouldEqual "01"
      }
      "fecha de pago" in {
        pagos.getPago.get(0).getFechaPago.toString shouldEqual "2021-04-09T23:25:42"
      }
      "moneda" in {
        pagos.getPago.get(0).getMonedaP.toString shouldEqual "MXN"
      }
      "tipo de cambio" in {
        pagos.getPago.get(0).getTipoCambioP shouldEqual BigDecimal(10).bigDecimal
      }
      "monto" in {
        pagos.getPago.get(0).getMonto shouldEqual BigDecimal("12768.00").bigDecimal
      }
      "numero de operacion" in {
        pagos.getPago.get(0).getNumOperacion shouldEqual "numoperacion"
      }
      "clave RFC de la entidad emisora de la cuenta origen" in {
        pagos.getPago.get(0).getRfcEmisorCtaOrd shouldEqual "RfcEmisorCtaOrd"
      }
      "nombre del banco ordenante" in {
        pagos.getPago.get(0).getNomBancoOrdExt shouldEqual "NomBancoOrdExt"
      }
      "número de la cuenta con la que se realizó el pago" in {
        pagos.getPago.get(0).getCtaOrdenante shouldEqual "CtaOrdenante"
      }
      "clave RFC de la entidad operadora de la cuenta destino" in {
        pagos.getPago.get(0).getRfcEmisorCtaBen shouldEqual "RfcEmisorCtaBen"
      }
      "número de cuenta en donde se recibió el pago" in {
        pagos.getPago.get(0).getCtaBeneficiario shouldEqual "CtaBeneficiario"
      }
      "clave del tipo de cadena de pago" in {
        pagos.getPago.get(0).getTipoCadPago shouldEqual "TipoCadPago"
      }
      "sello digital que se asocie al pago" in {
        new String(pagos.getPago.get(0).getSelloPago) shouldEqual "SelloPago"
      }
      "certificado que ampara al pago" in {
        new String(pagos.getPago.get(0).getCertPago) shouldEqual "CertPago"
      }
      "cadena original del comprobante de pago" in {
        pagos.getPago.get(0).getCadPago shouldEqual "CadPago"
      }

      "Documento relacionado" - {
        "identificador del documento relacionado con el pago" in {
          pagos.getPago.get(0).getDoctoRelacionado.get(0).getIdDocumento shouldEqual "IdDocumento"
        }
        "Atributo opcional para precisar la serie del comprobante para control interno del contribuyente" in {
          pagos.getPago.get(0).getDoctoRelacionado.get(0).getSerie shouldEqual "Serie"
        }
        "Atributo opcional para precisar el folio del comprobante para control interno del contribuyente" in {
          pagos.getPago.get(0).getDoctoRelacionado.get(0).getFolio shouldEqual "Folio"
        }
        "Atributo requerido para identificar la clave de la moneda utilizada en los importes del documento relacionado" in {
          pagos.getPago.get(0).getDoctoRelacionado.get(0).getMonedaDR shouldEqual CMoneda.valueOf("MXN")
        }
        "Atributo condicional para expresar el tipo de cambio conforme con la moneda registrada en el documento relacionado" in {
          pagos.getPago.get(0).getDoctoRelacionado.get(0).getTipoCambioDR shouldEqual BigDecimal("20.00").bigDecimal
        }
        "Atributo requerido para expresar la clave del método de pago que se registró en el documento relacionado" in {
          pagos.getPago.get(0).getDoctoRelacionado.get(0).getMetodoDePagoDR shouldEqual CMetodoPago.PPD
        }
        "Atributo condicional para expresar el número de parcialidad que corresponde al pago" in {
          pagos.getPago.get(0).getDoctoRelacionado.get(0).getNumParcialidad shouldEqual BigInt(9).bigInteger
        }
        "Atributo condicional para expresar el monto del saldo insoluto de la parcialidad anterior" in {
          pagos.getPago.get(0).getDoctoRelacionado.get(0).getImpSaldoAnt shouldEqual BigDecimal("45.52").bigDecimal
        }
        "Atributo condicional para expresar el importe pagado para el documento relacionado" in {
          pagos.getPago.get(0).getDoctoRelacionado.get(0).getImpPagado shouldEqual BigDecimal("42.52").bigDecimal
        }
        "Atributo condicional para expresar la diferencia entre el importe del saldo anterior y el monto del pago" in {
          pagos.getPago.get(0).getDoctoRelacionado.get(0).getImpSaldoInsoluto shouldEqual BigDecimal("44.52").bigDecimal
        }
      }

      "Impuestos" - {
        "Atributo condicional para expresar el total de los impuestos retenidos que se desprenden del pago" in {
          pagos.getPago.get(0).getImpuestos.get(0).getTotalImpuestosRetenidos shouldEqual BigDecimal("23.23").bigDecimal
        }
        "Atributo condicional para expresar el total de los impuestos trasladados que se desprenden del pago" in {
          pagos.getPago.get(0).getImpuestos.get(0).getTotalImpuestosTrasladados shouldEqual BigDecimal("24.24").bigDecimal
        }

        "Impuestos retenidos aplicables" - {
          "Atributo requerido para señalar el importe o monto del impuesto retenido" in {
            pagos.getPago.get(0).getImpuestos.get(0).getRetenciones.getRetencion.get(0).getImporte shouldEqual BigDecimal("45.12").bigDecimal
          }
          "Atributo requerido para señalar la clave del tipo de impuesto retenido" in {
            pagos.getPago.get(0).getImpuestos.get(0).getRetenciones.getRetencion.get(0).getImpuesto shouldEqual "01"
          }
        }

        "Impuestos trasladados aplicables" - {
          "Atributo requerido para señalar el importe o monto del impuesto trasladado." in {
            pagos.getPago.get(0).getImpuestos.get(0).getTraslados.getTraslado.get(0).getImporte shouldEqual BigDecimal("591.48").bigDecimal
          }
          "Atributo requerido para señalar la clave del tipo de impuesto trasladado." in {
            pagos.getPago.get(0).getImpuestos.get(0).getTraslados.getTraslado.get(0).getImpuesto shouldEqual "02"
          }
          "Atributo requerido para señalar la clave del tipo de factor que se aplica a la base del impuesto" in {
            pagos.getPago.get(0).getImpuestos.get(0).getTraslados.getTraslado.get(0).getTipoFactor shouldEqual CTipoFactor.TASA
          }
          "Atributo requerido para señalar el valor de la tasa o cuota del impuesto que se traslada" in {
            pagos.getPago.get(0).getImpuestos.get(0).getTraslados.getTraslado.get(0).getTasaOCuota shouldEqual BigDecimal("16.0000").bigDecimal
          }
        }

      }

    }
    "Carta porte" - {
      val complementoCP = Complementos(
        cartaPorte20 = Some(CartaPorte(
          transpInternac = "No",
          entradaSalidaMerc = Some("Salida"),
          paisOrigenDestino = Some("MEX"),
          viaEntradaSalida = Some("MEX"),
          totalDistRec = Some(BigDecimal("20.12")),
          ubicaciones = Ubicaciones(Seq(
            Ubicacion(
              tipoUbicacion = "Origen",
              idUbicacion = Some("MXSHRIDK234"),
              rfcRemitenteDestinatario = "rfcRemitenteDestinatario",
              nombreRemitenteDestinatario = Some("nombreRemitenteDestinatario"),
              numRegIdTrib = Some("case class Ubicacion"),
              residenciaFiscal = Some("ARG"),
              numEstacion = Some("numEstacion"),
              nombreEstacion = Some("nombreEstacion"),
              navegacionTrafico = Some("navegacionTrafico"),
              fechaHoraSalidaLlegada = "2021-04-09T23:25:42",
              tipoEstacion = Some("tipoEstacion"),
              distanciaRecorrida = Some(BigDecimal("21.23")),
              domicilio = Some(
                Domicilio(
                  calle = Some("calle"),
                  numeroExterior = Some("numeroExterior"),
                  numeroInterior = Some("numeroInterior"),
                  colonia = Some("colonia"),
                  localidad = Some("localidad"),
                  referencia = Some("referencia"),
                  municipio = Some("municipio"),
                  estado = "Hidalgo",
                  pais = "MEX",
                  codigoPostal = "12345"))
            ))),
          mercancias = Mercancias(
            pesoBrutoTotal = BigDecimal("23.45"),
            unidadPeso = "unidadPeso",
            pesoNetoTotal = Some(BigDecimal("21.24")),
            numTotalMercancias = 50,
            cargoPorTasacion = Some(BigDecimal("32.31")),
            mercancia = Seq(
              Mercancia(
                bienesTransp = "bienesTransp",
                clavesSTCC = Some("clavesSTCC"),
                descripcion = "descripcion",
                cantidad = BigDecimal("32.43"),
                claveUnidad = "claveUnidad",
                unidad = Some("unidad"),
                dimensiones = Some("dimensiones"),
                materialPeligroso = Some("materialPeligroso"),
                cveMaterialPeligroso = Some("cveMaterialPeligroso"),
                embalaje = Some("embalaje"),
                descripEmbalaje = Some("descripEmbalaje"),
                pesoEnKg = BigDecimal("24"),
                valorMercancia = Some(BigDecimal("23.23")),
                moneda = Some("MXN"),
                fraccionArancelaria = Some("fraccionArancelaria"),
                uuidComercioExt = Some("uuidComercioExt"),
                pedimentos = List(
                  Pedimento(
                    pedimento = "pedimento"
                  )),
                guiasIdentificacion = List(
                  GuiasIdentificacion(
                    numeroGuiaIdentificacion = "numeroGuiaIdentificacion",
                    descripGuiaIdentificacion = "descripGuiaIdentificacion",
                    pesoGuiaIdentificacion = BigDecimal("12.11")
                  )),
                cantidadTransporta = List(
                  CantidadTransporta(
                    cantidad = BigDecimal("23.32"),
                    idOrigen = "idOrigen",
                    idDestino = "idDestino",
                    cvesTransporte = Some("cvesTransporte")
                  )),
                detalleMercancia = List(DetalleMercancia(
                  unidadPesoMerc = "unidadPesoMerc",
                  pesoBruto = BigDecimal("32.24"),
                  pesoNeto = BigDecimal("34.23"),
                  pesoTara = BigDecimal("43.34"),
                  numPiezas = Some(23)
                ))
              )),
            autotransporte = Some(
              Autotransporte(
                permSCT = "TPAF_01",
                numPermisoSCT = "23",
                identificacionVehicular = IdentificacionVehicular(
                  configVehicular = "C_2",
                  placaVM = "placaVM",
                  anioModeloVM = 2020
                ),
                seguros = Seguros(
                  aseguraRespCivil = "aseguraRespCivil",
                  polizaRespCivil = "polizaRespCivil",
                  aseguraMedAmbiente = Some("aseguraMedAmbiente"),
                  polizaMedAmbiente = Some("polizaMedioAmbiente"),
                  aseguraCarga = Some("aseguraCarga"),
                  polizaCarga = Some("polizaCarga"),
                  primaSeguro = Some(BigDecimal("23.23"))
                ),
                remolques = Some(Remolques(Seq(
                  Remolque(
                    subTipoRem = "CTR_004",
                    placa = "FM435TR3535"
                  ))))
              )),
            transporteMaritimo = Some(
              TransporteMaritimo(
                permSCT = Some("TPAF02"),
                numPermisoSCT = Some("234"),
                nombreAseg = Some("nombreAseg"),
                numPolizaSeguro = Some("numPolizaSeguro"),
                tipoEmbarcacion = "B_03",
                matricula = "matricula",
                numeroOMI = "numeroOMI",
                anioEmbarcacion = Some(2021),
                nombreEmbarc = Some("nombreEmbarc"),
                nacionalidadEmbarc = "MEX",
                unidadesDeArqBruto = BigDecimal("23.12"),
                tipoCarga = "GMN",
                numCertITC = "numCertITC",
                eslora = Some(BigDecimal("50.43")),
                manga = Some(BigDecimal("34.23")),
                calado = Some(BigDecimal("43.24")),
                lineaNaviera = Some("lineaNaviera"),
                nombreAgenteNaviero = "nombreAgenteNaviero",
                numAutorizacionNaviero = "2345",
                numViaje = Some("3456"),
                numConocEmbarc = Some("45436"),
                contenedor = Seq(
                  Contenedor(
                    matriculaContenedor = "ME453EWR",
                    tipoContenedor = "CM_001",
                    numPrecinto = Some("numPrecinto")
                  ))
              )),
            transporteAereo = Some(
              TransporteAereo(
                permSCT = "TPAF_05",
                numPermisoSCT = "24325",
                matriculaAeronave = Some("KSAJFEN453"),
                nombreAseg = Some("nombreAseg"),
                numPolizaSeguro = Some("42354"),
                numeroGuia = "23235",
                lugarContrato = Some("lugarContrato"),
                codigoTransportista = "CA_002",
                rfcEmbarcador = Some("SASA24387"),
                numRegIdTribEmbarc = Some("3348357"),
                residenciaFiscalEmbarc = Some("ARG"),
                nombreEmbarcador = Some("nombreEmbarcador")
              )),
            transporteFerroviario = Some(
              TransporteFerroviario(
                tipoServicio = "TS_01",
                tipoDeTrafico = "TT_01",
                nombreAseg = Some("nombreAseg"),
                numPolizaSeguro = Some("30923843"),
                derechosDePaso = Seq(
                  DerechosDePaso(
                    tipoDerechoDePaso = "CDP_001",
                    kilometrajePagado = BigDecimal("324.34")
                  )), carro = Seq(
                  Carro(
                    tipoCarro = "TC_01",
                    matriculaCarro = "ETD23213",
                    guiaCarro = "guiaCarro",
                    toneladasNetasCarro = BigDecimal("23.24"),
                    contenedor = Seq(ContenedorCarro(
                      tipoContenedor = "TC_01",
                      pesoContenedorVacio = BigDecimal("23.23"),
                      pesoNetoMercancia = BigDecimal("85.33")
                    ))
                  ))
              ))
          ),
          figuratransporte = Some(
            Figuratransporte(
              tiposFigura = Seq(
                TiposFigura(
                  tipoFigura = "tipoFigura",
                  rfcFigura = Some("SASA4305998"),
                  numLicencia = Some("34275"),
                  nombreFigura = Some("nombreFigura"),
                  numRegIdTribFigura = Some("3545"),
                  residenciaFiscalFigura = Some("MEX"),
                  partesTransporte = Seq(
                    PartesTransporte(
                      parteTransporte = "PT01"
                    )), domicilio = Some(
                    DomicilioFigura(
                      calleFigura = Some("calleFigura"),
                      numeroExteriorFigura = Some("24"),
                      numeroInteriorFigura = Some("34"),
                      coloniaFigura = Some("coloniaFigura"),
                      localidadFigura = Some("localidadFigura"),
                      referenciaFigura = Some("referenciaFigura"),
                      municipioFigura = Some("municipioFigura"),
                      estadoFigura = "Hidalgo",
                      paisFigura = "MEX",
                      codigoPostalFigura = "42136"
                    ))
                ))
            ))
        )))
      val complementosBuilderImpl = new ComplementosBuilderImpl(new XmlTimeUtilsImpl(new EmpresalitUtilsImpl))
      val complementosInvoice: Comprobante.Complemento = complementosBuilderImpl.execute(new ObjectFactory(), complementoCP)
      val cartaPorte = complementosInvoice.getAny.get(0).asInstanceOf[cfdi33.CartaPorte]

      "CartaPorte" - {
        "Expresa si las mercancias salen o entran al territorio nacional" in {
          cartaPorte.getTranspInternac shouldEqual "No"
        }
        "Precisa las mercancias que entran y salen de territorio nacional" in {
          cartaPorte.getEntradaSalidaMerc shouldEqual "Salida"
        }
        "Clave de pais de origen o destino de las mercancias que se transportan" in {
          cartaPorte.getPaisOrigenDestino shouldEqual CPais.valueOf("MEX")
        }
        "Registro de la via de ingreso o salida de las mercancias" in {
          cartaPorte.getViaEntradaSalida shouldEqual "MEX"
        }
        "Expresa el total de distancia que recorren las mercancias en territorio nacional" in {
          cartaPorte.getTotalDistRec shouldEqual BigDecimal("20.12").bigDecimal
        }
        "Ubicaciones" - {
          "Ubicacion" - {
            "Tipo ubicacion" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getTipoUbicacion shouldEqual "Origen"
            }
            "ID ubicacion" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getIDUbicacion shouldEqual "MXSHRIDK234"
            }
            "RFC del remitente o destinatario" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getRFCRemitenteDestinatario shouldEqual "rfcRemitenteDestinatario"
            }
            "Nombre del remitente o destinatario" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getNombreRemitenteDestinatario shouldEqual "nombreRemitenteDestinatario"
            }
            "Numero de identificacion o registro de fiscal del pais de residencia" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getNumRegIdTrib shouldEqual "case class Ubicacion"
            }
            "Clave del pais de residencia" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getResidenciaFiscal shouldEqual CPais.valueOf("ARG")
            }
            "Clave de estacion de origen o destino para el traslado de bienes" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getNumEstacion shouldEqual "numEstacion"
            }
            "Nombre de la estacion de origen o destino" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getNombreEstacion shouldEqual "nombreEstacion"
            }
            "Tipo de puerto de origen o destino donde se documentaron los bienes" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getNavegacionTrafico shouldEqual "navegacionTrafico"
            }
            "Registro de fecha y hora estimada en el que salen o llegan los bienes" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getFechaHoraSalidaLlegada.toString shouldEqual "2021-04-09T23:25:42"
            }
            "Tipo de estacion por el que pasan los bienes durante su traslado" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getTipoEstacion shouldEqual "tipoEstacion"
            }
            "Registro de distancia recorrida entre el origen y el destino" in {
              cartaPorte.getUbicaciones.getUbicacion.get(0).getDistanciaRecorrida shouldEqual BigDecimal("21.23").bigDecimal
            }
            "Domicilio" - {
              "Calle en el que esta hubicado el origen o destino de los bienes" in {
                cartaPorte.getUbicaciones.getUbicacion.get(0).getDomicilio.getCalle shouldEqual "calle"
              }
              "Numero exterior del domicilio de origen o destino de los bienes" in {
                cartaPorte.getUbicaciones.getUbicacion.get(0).getDomicilio.getNumeroExterior shouldEqual "numeroExterior"
              }
              "Numero interior del domicilio de origen o destino de los bienes" in {
                cartaPorte.getUbicaciones.getUbicacion.get(0).getDomicilio.getNumeroInterior shouldEqual "numeroInterior"
              }
              "Colonia del domicilio origen o destino de los bienes" in {
                cartaPorte.getUbicaciones.getUbicacion.get(0).getDomicilio.getColonia shouldEqual "colonia"
              }
              "Ciudad o poblacion del domicilio de origen o destino de los bienes" in {
                cartaPorte.getUbicaciones.getUbicacion.get(0).getDomicilio.getLocalidad shouldEqual "localidad"
              }
              "Referencia geografica adicional" in {
                cartaPorte.getUbicaciones.getUbicacion.get(0).getDomicilio.getReferencia shouldEqual "referencia"
              }
              "Municipio, delegacion o alcaldia del domicilio de origen o destino de los bienes" in {
                cartaPorte.getUbicaciones.getUbicacion.get(0).getDomicilio.getMunicipio shouldEqual "municipio"
              }
              "Estado, entidad, region, comunidad donde se encuentra ubicado el origen o destino de los bienes" in {
                cartaPorte.getUbicaciones.getUbicacion.get(0).getDomicilio.getEstado shouldEqual "Hidalgo"
              }
              "Clave del pais en donde se encuentra ubicado el origen o el destino del pais de los bienes" in {
                cartaPorte.getUbicaciones.getUbicacion.get(0).getDomicilio.getPais shouldEqual CPais.valueOf("MEX")
              }
              "Codigo postal en donde se encuentra el origen o destino de los bienes" in {
                cartaPorte.getUbicaciones.getUbicacion.get(0).getDomicilio.getCodigoPostal shouldEqual "12345"
              }
            }
          }
        }
        "Mercancias" - {
          "Registro de suma de peso bruto total estimado de los bienes" in {
            cartaPorte.getMercancias.getPesoBrutoTotal shouldEqual BigDecimal("23.45").bigDecimal
          }
          "Clave de unidad de medida del peso de los bienes" in {
            cartaPorte.getMercancias.getUnidadPeso shouldEqual "unidadPeso"
          }
          "Registro de la suma de los valores indicados en el atributo pesoNeto" in {
            cartaPorte.getMercancias.getPesoNetoTotal shouldEqual BigDecimal("21.24").bigDecimal
          }
          "Registro del numero total de los bienes que se trasladan" in {
            cartaPorte.getMercancias.getNumTotalMercancias shouldEqual 50
          }
          "Monto del importe pagado por la tasacion de los bienes" in {
            cartaPorte.getMercancias.getCargoPorTasacion shouldEqual BigDecimal("32.31").bigDecimal
          }
          "Mercancia" - {
            "Clave de producto de los bienes y o mercancias que se trasladan" in {
              cartaPorte.getMercancias.getMercancia.get(0).getBienesTransp shouldEqual "bienesTransp"
            }
            "Expresa la clave de producto de la STCC" in {
              cartaPorte.getMercancias.getMercancia.get(0).getClaveSTCC shouldEqual "clavesSTCC"
            }
            "Detalla caracteristicas de los bienes" in {
              cartaPorte.getMercancias.getMercancia.get(0).getDescripcion shouldEqual "descripcion"
            }
            "Cantidad total del bienes que se trasladan" in {
              cartaPorte.getMercancias.getMercancia.get(0).getCantidad shouldEqual BigDecimal("32.43").bigDecimal
            }
            "Clave de la unidad de medida estandarizada" in {
              cartaPorte.getMercancias.getMercancia.get(0).getClaveUnidad shouldEqual "claveUnidad"
            }
            "Unidad de medida propia para la cantidad de los bienes que se transportan" in {
              cartaPorte.getMercancias.getMercancia.get(0).getUnidad shouldEqual "unidad"
            }
            "Medidas de empaque de los bienes y mercancias" in {
              cartaPorte.getMercancias.getMercancia.get(0).getDimensiones shouldEqual "dimensiones"
            }
            "Especifica si los materiales que se trasladan son considerados como peligrosos" in {
              cartaPorte.getMercancias.getMercancia.get(0).getMaterialPeligroso shouldEqual "materialPeligroso"
            }
            "Clave del tipo de material peligroso" in {
              cartaPorte.getMercancias.getMercancia.get(0).getCveMaterialPeligroso shouldEqual "cveMaterialPeligroso"
            }
            "Clave del tipo de embalaje que se requiere para transportar el material" in {
              cartaPorte.getMercancias.getMercancia.get(0).getEmbalaje shouldEqual "embalaje"
            }
            "Descripcion del embalaje de los bienes que se trasladan" in {
              cartaPorte.getMercancias.getMercancia.get(0).getDescripEmbalaje shouldEqual "descripEmbalaje"
            }
            "Indica en kilogramos el peso estimado de los bienes que se trasladan" in {
              cartaPorte.getMercancias.getMercancia.get(0).getPesoEnKg shouldEqual BigDecimal("24").bigDecimal
            }
            "Expresa el monto del valor de los bienes que se trasladan" in {
              cartaPorte.getMercancias.getMercancia.get(0).getValorMercancia shouldEqual BigDecimal("23.23").bigDecimal
            }
            "Clave de la moneda utilizada para expresar el valor de los bienes" in {
              cartaPorte.getMercancias.getMercancia.get(0).getMoneda shouldEqual CMoneda.valueOf("MXN")
            }
            "Expresa la clave de la fraccion arancelaria que corresponde" in {
              cartaPorte.getMercancias.getMercancia.get(0).getFraccionArancelaria shouldEqual "fraccionArancelaria"
            }
            "Folio fiscal (UUID) del comprobante de comercio exterior que los relaciona" in {
              cartaPorte.getMercancias.getMercancia.get(0).getUUIDComercioExt shouldEqual "uuidComercioExt"
            }
            "Pedimentos" - {
              "Numero de pedimento de importacion que se encuentra asociado al traslado de los bienes" in {
                cartaPorte.getMercancias.getMercancia.get(0).getPedimentos.get(0).getPedimento shouldEqual "pedimento"
              }
            }
            "Guias Identificacion" - {
              "Numero de guia de cada paquete" in {
                cartaPorte.getMercancias.getMercancia.get(0).getGuiasIdentificacion.get(0).getNumeroGuiaIdentificacion shouldEqual "numeroGuiaIdentificacion"
              }
              "Descripcion del contenido del paquete" in {
                cartaPorte.getMercancias.getMercancia.get(0).getGuiasIdentificacion.get(0).getDescripGuiaIdentificacion shouldEqual "descripGuiaIdentificacion"
              }
              "Indicador de peso del paquete en kilogramos" in {
                cartaPorte.getMercancias.getMercancia.get(0).getGuiasIdentificacion.get(0).getPesoGuiaIdentificacion shouldEqual BigDecimal("12.11").bigDecimal
              }
            }
            "Cantidad Transportada" - {
              "Numero de bienes que se trasladan" in {
                cartaPorte.getMercancias.getMercancia.get(0).getCantidadTransporta.get(0).getCantidad shouldEqual BigDecimal("23.32").bigDecimal
              }
              "Clave de identificador del origen de los bienes" in {
                cartaPorte.getMercancias.getMercancia.get(0).getCantidadTransporta.get(0).getIDOrigen shouldEqual "idOrigen"
              }
              "Clave de identificador del destino de los bienes" in {
                cartaPorte.getMercancias.getMercancia.get(0).getCantidadTransporta.get(0).getIDDestino shouldEqual "idDestino"
              }
              "Clave a traves de la cual se identifica el medio por el que se transportan los bienes" in {
                cartaPorte.getMercancias.getMercancia.get(0).getCantidadTransporta.get(0).getCvesTransporte shouldEqual "cvesTransporte"
              }
            }
            "Detalle mercancia" - {
              "Clave de la unidad de medida estandarizada del peso de los bienes" in {
                cartaPorte.getMercancias.getMercancia.get(0).getDetalleMercancia.getUnidadPesoMerc shouldEqual "unidadPesoMerc"
              }
              "Registro del peso bruto total de los bienes que se trasladan" in {
                cartaPorte.getMercancias.getMercancia.get(0).getDetalleMercancia.getPesoBruto shouldEqual BigDecimal("32.24").bigDecimal
              }
              "Peso neto total de los bienes que se trasladan" in {
                cartaPorte.getMercancias.getMercancia.get(0).getDetalleMercancia.getPesoNeto shouldEqual BigDecimal("34.23").bigDecimal
              }
              "Registro del peso bruto, menos el peso neto de los bienes" in {
                cartaPorte.getMercancias.getMercancia.get(0).getDetalleMercancia.getPesoTara shouldEqual BigDecimal("43.34").bigDecimal
              }
              "Numero de piezas de los bienes que se trasladan" in {
                cartaPorte.getMercancias.getMercancia.get(0).getDetalleMercancia.getNumPiezas shouldEqual 23
              }
            }
          }
          "Autotransporte" - {
            "Clave del tipo de permiso proporcionado por la SCT o autoridad correspondiente" in {
              cartaPorte.getMercancias.getAutotransporte.getPermSCT shouldEqual CTipoPermiso.valueOf("TPAF_01")
            }
            "Numero de permiso otorgado por la SCT o autoridad correspondiente" in {
              cartaPorte.getMercancias.getAutotransporte.getNumPermisoSCT shouldEqual "23"
            }
            "Identificacion Vehicular" - {
              "Clave de nomenclatura del autotransporte que es utilizado para transportar los bienes" in {
                cartaPorte.getMercancias.getAutotransporte.getIdentificacionVehicular.getConfigVehicular shouldEqual CConfigAutotransporte.valueOf("C_2")
              }
              "Registrar solo los caracteres alfanumericos, sin guiones ni espacios de la placa vehicular" in {
                cartaPorte.getMercancias.getAutotransporte.getIdentificacionVehicular.getPlacaVM shouldEqual "placaVM"
              }
              "Registro del año del autotransporte que es utilizado para transportar los bienes" in {
                cartaPorte.getMercancias.getAutotransporte.getIdentificacionVehicular.getAnioModeloVM.toString shouldEqual "2020"
              }
            }
            "Seguros" - {
              "Nombre de la aseguradora que cubre riesgos por responsabilidad civil del autotransporte" in {
                cartaPorte.getMercancias.getAutotransporte.getSeguros.getAseguraRespCivil shouldEqual "aseguraRespCivil"
              }
              "Numero de poliza asignado por la aseguradora" in {
                cartaPorte.getMercancias.getAutotransporte.getSeguros.getPolizaRespCivil shouldEqual "polizaRespCivil"
              }
              "Nombre de la aseguradora que cubre posibles daños al medio ambiente" in {
                cartaPorte.getMercancias.getAutotransporte.getSeguros.getAseguraMedAmbiente shouldEqual "aseguraMedAmbiente"
              }
              "Numero de poliza asignado por la aseguradora, que cubre posibles daños al medio ambiente" in {
                cartaPorte.getMercancias.getAutotransporte.getSeguros.getPolizaMedAmbiente shouldEqual "polizaMedioAmbiente"
              }
              "Nombre de la aseguradora que cubre los daños de la carga" in {
                cartaPorte.getMercancias.getAutotransporte.getSeguros.getAseguraCarga shouldEqual "aseguraCarga"
              }
              "Numero de poliza asignado por la aseguradora, que cubre los daños de la carga" in {
                cartaPorte.getMercancias.getAutotransporte.getSeguros.getPolizaCarga shouldEqual "polizaCarga"
              }
              "Valor del importe por el cargo adicional convenido entre el transportista y el cliente" in {
                cartaPorte.getMercancias.getAutotransporte.getSeguros.getPrimaSeguro shouldEqual BigDecimal("23.23").bigDecimal
              }
            }
            "Remolques" - {
              "Remolque" - {
                "Clave del subtipo de remolque o semirremolque que se emplean con el autotransporte para el traslado de bienes" in {
                  cartaPorte.getMercancias.getAutotransporte.getRemolques.getRemolque.get(0).getSubTipoRem shouldEqual CSubTipoRem.valueOf("CTR_004")
                }
                "Caracteres alfanumericos, sin guiones ni espacios de la placa vehicular del remolque o semirremolque" in {
                  cartaPorte.getMercancias.getAutotransporte.getRemolques.getRemolque.get(0).getPlaca shouldEqual "FM435TR3535"
                }
              }
            }
          }
          "Transporte Maritimo" - {
            "Clave de permiso proporcionado por la SCT la cual debe corresponder con la embarcación que se está utilizando para el traslado de los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getPermSCT shouldEqual CTipoPermiso.valueOf("TPAF_02")
            }
            "Registro del número del permiso otorgado por la SCT" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNumPermisoSCT shouldEqual "234"
            }
            "Registro del nombre de la aseguradora que cubre la protección por responsabilidad civil de la embarcacion" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNombreAseg shouldEqual "nombreAseg"
            }
            "Registro del número de póliza asignada por la aseguradora que cubre por responsabilidad civil a la embarcación" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNumPolizaSeguro shouldEqual "numPolizaSeguro"
            }
            "Registro de la clave de identificación del tipo de embarcación" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getTipoEmbarcacion shouldEqual CConfigMaritima.valueOf("B_03")
            }
            "Registro del número de la matrícula o de embarcación que es utilizada para transportar los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getMatricula shouldEqual "matricula"
            }
            "Registro del número de identificación asignado por la Organización Marítima Internacional, a la embarcación encargada de transportar los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNumeroOMI shouldEqual "numeroOMI"
            }
            "Registro del año de la embarcación en la que se transportan los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getAnioEmbarcacion.toString shouldEqual "2021"
            }
            "Registro del nombre de la embarcación en la que se realiza el traslado de los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNombreEmbarc shouldEqual "nombreEmbarc"
            }
            "Registro de la clave del país correspondiente a la nacionalidad de la embarcación que transporta los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNacionalidadEmbarc shouldEqual CPais.valueOf("MEX")
            }
            "Registro del valor de las unidades de arqueo bruto conforme a las medidas internacionales definidas por el ITC para cada embarcación en la que se transportan los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getUnidadesDeArqBruto shouldEqual BigDecimal("23.12").bigDecimal
            }
            "Especifica el tipo de carga en el cual se clasifican los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getTipoCarga shouldEqual CClaveTipoCarga.valueOf("GMN")
            }
            "Registro de número del certificado emitido por la ITC para la embarcación que transporta los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNumCertITC shouldEqual "numCertITC"
            }
            "Registro de la longitud de eslora, definida en pies, con la que cuenta la embarcación en la que se transportan los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getEslora shouldEqual BigDecimal("50.43").bigDecimal
            }
            "Registro de la longitud de manga, definida en pies, con la que cuenta la embarcación en el que se transportan los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getManga shouldEqual BigDecimal("34.23").bigDecimal
            }
            "Registro de la longitud del calado, definida en pies, con la que cuenta la embarcación en la que se transportan los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getCalado shouldEqual BigDecimal("43.24").bigDecimal
            }
            "Registro del nombre de la línea naviera autorizada de gestionar el traslado de los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getLineaNaviera shouldEqual "lineaNaviera"
            }
            "Registrar el nombre del agente naviero consignatario autorizado para gestionar el traslado de los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNombreAgenteNaviero shouldEqual "nombreAgenteNaviero"
            }
            "Expresar el número de la autorización como agente naviero consignatario emitida por la SCT" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNumAutorizacionNaviero shouldEqual "2345"
            }
            "Registrar el número del viaje con el que se identifica el traslado de los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNumViaje shouldEqual "3456"
            }
            "Registro del número de conocimiento de embarque con el que se identifica el traslado de los bienes" in {
              cartaPorte.getMercancias.getTransporteMaritimo.getNumConocEmbarc shouldEqual "45436"
            }
            "Contenedor" - {
              "Registro de la matrícula o el número de identificación del contenedor marítimo en el que se transportan los bienes" in {
                cartaPorte.getMercancias.getTransporteMaritimo.getContenedor.get(0).getMatriculaContenedor shouldEqual "ME453EWR"
              }
              "Registro de la clave de identificación correspondiente con el tipo de contenedor marítimo en el que se transportan los bienes" in {
                cartaPorte.getMercancias.getTransporteMaritimo.getContenedor.get(0).getTipoContenedor shouldEqual CContenedorMaritimo.valueOf("CM_001")
              }
              "Registro de número del sello o precinto de los contenedores marítimos que son utilizados para trasladar los bienes" in {
                cartaPorte.getMercancias.getTransporteMaritimo.getContenedor.get(0).getNumPrecinto shouldEqual "numPrecinto"
              }
            }
          }
          "Transporte aereo" - {
            "Registro de clave del permiso proporcionado por la SCT, la cual debe corresponder con la aeronave que se está utilizando para realizar el traslado de los bienes" in {
              cartaPorte.getMercancias.getTransporteAereo.getPermSCT shouldEqual CTipoPermiso.valueOf("TPAF_05")
            }
            "Registro del número de permiso proporcionado por la SCT o la autoridad análoga, según corresponda, para el transporte de bienes" in {
              cartaPorte.getMercancias.getTransporteAereo.getNumPermisoSCT shouldEqual "24325"
            }
            "Registro del número de la matrícula de la aeronave con la que se realiza el traslado de los bienes, mas el guion medio" in {
              cartaPorte.getMercancias.getTransporteAereo.getMatriculaAeronave shouldEqual "KSAJFEN453"
            }
            "Registro del nombre de la aseguradora que cubre los riesgos de la aeronave con la que transportan los bienes" in {
              cartaPorte.getMercancias.getTransporteAereo.getNombreAseg shouldEqual "nombreAseg"
            }
            "Registro del número de póliza asignado por la aseguradora que cubre la protección e indemnización por responsabilidad civil de la aeronave que transporta los bienes" in {
              cartaPorte.getMercancias.getTransporteAereo.getNumPolizaSeguro shouldEqual "42354"
            }
            "Registro del número de guía aérea con el que se trasladan los bienes" in {
              cartaPorte.getMercancias.getTransporteAereo.getNumeroGuia shouldEqual "23235"
            }
            "Registro del lugar, entidad, región, localidad o análogo, donde se celebró el contrato para realizar el traslado de los bienes" in {
              cartaPorte.getMercancias.getTransporteAereo.getLugarContrato shouldEqual "lugarContrato"
            }
            "Registro del valor del código que tiene asignado el transportista" in {
              cartaPorte.getMercancias.getTransporteAereo.getCodigoTransportista shouldEqual CCodigoTransporteAereo.valueOf("CA_002")
            }
            "Registro de RFC del embarcador de los bienes" in {
              cartaPorte.getMercancias.getTransporteAereo.getRFCEmbarcador shouldEqual "SASA24387"
            }
            "Incorpora el número de identificación o registro fiscal del país de residencia cuando el embarcador sea residente en el extranjero" in {
              cartaPorte.getMercancias.getTransporteAereo.getNumRegIdTribEmbarc shouldEqual "3348357"
            }
            "Registro de la clave del país de residencia" in {
              cartaPorte.getMercancias.getTransporteAereo.getResidenciaFiscalEmbarc shouldEqual CPais.valueOf("ARG")
            }
            "Registro del nombre del embarcador de los bienes que se trasladan" in {
              cartaPorte.getMercancias.getTransporteAereo.getNombreEmbarcador shouldEqual "nombreEmbarcador"
            }
          }
          "Transporte Ferroviario" - {
            "Registro de la clave del tipo de servicio utilizado para el traslado de los bienes por via ferrea" in {
              cartaPorte.getMercancias.getTransporteFerroviario.getTipoDeServicio shouldEqual CTipoDeServicio.valueOf("TS_01")
            }
            "Registro de clave del tipo de tráfico para realizar el traslado de los bienes en territorio nacional" in {
              cartaPorte.getMercancias.getTransporteFerroviario.getTipoDeTrafico shouldEqual CTipoDeTrafico.valueOf("TT_01")
            }
            "Registro del nombre de la aseguradora que cubre los riesgos para el traslado de los bienes por via ferrea" in {
              cartaPorte.getMercancias.getTransporteFerroviario.getNombreAseg shouldEqual "nombreAseg"
            }
            "Registro del número de póliza asignada por la aseguradora para la protección e indemnización por responsabilidad civil en el traslado de los bienes transportados via ferrea" in {
              cartaPorte.getMercancias.getTransporteFerroviario.getNumPolizaSeguro shouldEqual "30923843"
            }
            "Derechos de paso" - {
              "Registro de clave del derecho de paso pagado por el transportista en las vías férreas" in {
                cartaPorte.getMercancias.getTransporteFerroviario.getDerechosDePaso.get(0).getTipoDerechoDePaso shouldEqual CDerechosDePaso.valueOf("CDP_001")
              }
              "Registro del total de kilómetros pagados por el transportista en las vías férreas" in {
                cartaPorte.getMercancias.getTransporteFerroviario.getDerechosDePaso.get(0).getKilometrajePagado shouldEqual BigDecimal("324.34").bigDecimal
              }
            }
            "Carro" - {
              "Registro de clave del tipo de carro utilizado para el traslado de los bienes por via ferrea" in {
                cartaPorte.getMercancias.getTransporteFerroviario.getCarro.get(0).getTipoCarro shouldEqual CTipoCarro.valueOf("TC_01")
              }
              "Registro del número de contenedor, carro de ferrocarril o número económico del vehículo en el que se trasladan los bienes por via ferrea" in {
                cartaPorte.getMercancias.getTransporteFerroviario.getCarro.get(0).getMatriculaCarro shouldEqual "ETD23213"
              }
              "Registro del número de guía asignado al contenedor, carro de ferrocarril o vehículo, en el que se trasladan los bienes por vía férrea." in {
                cartaPorte.getMercancias.getTransporteFerroviario.getCarro.get(0).getGuiaCarro shouldEqual "guiaCarro"
              }
              "Registro de cantidad de las toneladas netas depositadas en el contenedor, carro de ferrocarril o vehículo en el que se trasladan los bienes por vía férrea" in {
                cartaPorte.getMercancias.getTransporteFerroviario.getCarro.get(0).getToneladasNetasCarro shouldEqual BigDecimal("23.24").bigDecimal
              }
              "Contenedor" - {
                "Registro de clave con la que se identifica al tipo de contenedor o el vagón en el que se realiza el traslado de los bienes" in {
                  cartaPorte.getMercancias.getTransporteFerroviario.getCarro.get(0).getContenedor.get(0).getTipoContenedor shouldEqual CContenedor.valueOf("TC_01")
                }
                "Registro en kilogramos, el peso del contenedor vacío en el que se trasladan los bienes" in {
                  cartaPorte.getMercancias.getTransporteFerroviario.getCarro.get(0).getContenedor.get(0).getPesoContenedorVacio shouldEqual BigDecimal("23.23").bigDecimal
                }
                "Registro en kilogramos el peso neto de los bienes y/o mercancías que son trasladados en el contenedor" in {
                  cartaPorte.getMercancias.getTransporteFerroviario.getCarro.get(0).getContenedor.get(0).getPesoNetoMercancia shouldEqual BigDecimal("85.33").bigDecimal
                }
              }
            }
          }
        }
        "Figura Transporte" - {
          "Tipos Figura" - {
            "Registro de clave de la figura de transporte que interviene en el traslado de los bienes" in {
              cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getTipoFigura shouldEqual "tipoFigura"
            }
            "Registro del RFC de la figura de transporte que interviene en el traslado de los bienes" in {
              cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getRFCFigura shouldEqual "SASA4305998"
            }
            "Expresion del número de la licencia o el permiso otorgado al operador del autotransporte de carga en el que realiza el traslado de los bienes" in {
              cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getNumLicencia shouldEqual "34275"
            }
            "Registro del nombre de la figura de transporte que interviene en el traslado de los bienes" in {
              cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getNombreFigura shouldEqual "nombreFigura"
            }
            "Registro del número de identificación o registro fiscal del país de residencia de la figura de transporte que interviene en el traslado de los bienes, si reside en el extranjero" in {
              cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getNumRegIdTribFigura shouldEqual "3545"
            }
            "Registro de clave del país de residencia de la figura de transporte que interviene en el traslado de los bienes" in {
              cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getResidenciaFiscalFigura shouldEqual CPais.valueOf("MEX")
            }
            "Partes transporte" - {
              "Regitro de información de la parte del transporte de la cual el emisor del comprobante es distinto al dueño de la misma, medios que se utilicen para el traslado de los bienes" in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getPartesTransporte.get(0).getParteTransporte shouldEqual CParteTransporte.valueOf("PT_01")
              }
            }
            "Domicilio" - {
              "Registro de la calle en la que está ubicado el domicilio del(los) tipo(s) de figura transporte" in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getDomicilio.getCalle shouldEqual "calleFigura"
              }
              "Registro del número exterior en donde se ubica el domicilio del(los) tipo(s) de figura transporte" in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getDomicilio.getNumeroExterior shouldEqual "24"
              }
              "Registro del número interior en caso de existir, en donde se ubica el domicilio del(los) tipo(s) de figura transporte" in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getDomicilio.getNumeroInterior shouldEqual "34"
              }
              "Expresa la clave de la colonia o dato análogo en donde se ubica el domicilio del(los) tipo(s) de figura transporte." in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getDomicilio.getColonia shouldEqual "coloniaFigura"
              }
              "Registro de la clave de la ciudad, población, distrito o dato análogo de donde se encuentra ubicado el domicilio del(los) tipo(s) de figura transporte" in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getDomicilio.getLocalidad shouldEqual "localidadFigura"
              }
              "Registro de una referencia geográfica adicional que permita una fácil o precisa ubicación del domicilio del(los) tipo(s) de figura transporte" in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getDomicilio.getReferencia shouldEqual "referenciaFigura"
              }
              "Registro de la clave del municipio, delegación o alcaldía, condado o dato análogo en donde se encuentra ubicado el domicilio del(los) tipo(s) de figura transporte" in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getDomicilio.getMunicipio shouldEqual "municipioFigura"
              }
              "Registro del estado, entidad, región, comunidad, o dato análogo en donde se encuentra ubicado el domicilio del(los) tipo(s) de figura transporte." in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getDomicilio.getEstado shouldEqual "Hidalgo"
              }
              "Registro de clave del país en donde se encuentra ubicado el domicilio del(los) tipo(s) de figura transporte" in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getDomicilio.getPais shouldEqual CPais.valueOf("MEX")
              }
              "Registro del código postal en donde se encuentra ubicado el domicilio del(los) tipo(s) de figura transporte." in {
                cartaPorte.getFiguraTransporte.getTiposFigura.get(0).getDomicilio.getCodigoPostal shouldEqual "42136"
              }
            }
          }
        }
      }
    }
    "Complemento nomina" - {
      val complementos = Complementos(
        nomina12 = Some(
          NominaComp(
            version = "1.2",
            tipoNomina = "O",
            fechaPago = "2021-04-09",
            fechaInicialPago = "2021-04-09",
            fechaFinalPago = "2021-04-09",
            numDiasPagados = BigDecimal("2.5"),
            totalPercepciones = Some(BigDecimal("24")),
            totalDeducciones = Some(BigDecimal("24")),
            totalOtrosPagos = Some(BigDecimal("24")),
            emisor = Some(
              Emisor(
                curp = Some("CURP"),
                registroPatronal = Some("RegistroPatronal"),
                rfcPatronOrigen = Some("RFC"),
                entidadSNCF = Some(
                  EntidadSNCF(
                    origenRecurso = "IP",
                    montoRecursoPropio = Some(BigDecimal("21.324"))
                  )
                )
              )
            ),
            receptor = ReceptorNom(
              curp = "CURP",
              numSeguridadSocial = Some("numSeguridadSocial"),
              fechaInicioRelLaboral = Some("2021-04-09"),
              antiguedad = Some("antiguedad"),
              tipoContrato = "tipoContrato",
              sindicalizado = Some("sindicalizado"),
              tipoJornada = Some("tipoJornada"),
              tipoRegimen = "tipoRegimen",
              numEmpleado = "numEmpleado",
              departamento = Some("departamento"),
              puesto = Some("puesto"),
              riesgoPuesto = Some("riesgoPuesto"),
              periodicidadPago = "periodicidadPago",
              banco = Some("banco"),
              cuentaBancaria = Some(23),
              salarioBaseCotApor = Some(BigDecimal("23.232")),
              salarioDiarioIntegrado = Some(BigDecimal("32.35")),
              claveEntFed = "AGU",
              subContratacion = Seq(
                SubContratacion(
                  rfcLabora = "RFC",
                  porcentajeTiempo = BigDecimal("34.23")
                )
              )
            ),
            percepciones = Some(
              Percepciones(
                totalSueldos = Some(BigDecimal("345")),
                totalSeparacionIndemnizacion = Some(BigDecimal("345")),
                totalJubilacionPensionRetiro = Some(BigDecimal("345")),
                totalGravado = BigDecimal("24"),
                totalExento = BigDecimal("24"),
                percepcion = Seq(
                  Percepcion(
                    tipoPercepcion = "tipoPersepcion",
                    clave = "clave",
                    concepto = "concepto",
                    importeGravado = BigDecimal("34"),
                    importeExento = BigDecimal("242"),
                    accionesOTitulos = Some(
                      AccionesOTitulos(
                        valorMercado = BigDecimal("23.21"),
                        precioAlOtorgarse = BigDecimal("32.65")
                      )
                    ),
                    horasExtra = Seq(
                      HorasExtra(
                        dias = 23,
                        tipoHoras = "tipoHoras",
                        horasExtra = 43,
                        importePagado = BigDecimal("43")
                      )
                    )
                  )
                ),
                jubilacionPensionRetiro = Some(
                  JubilacionPensionRetiro(
                    totalUnaExhibicion = Some(BigDecimal("34.87")),
                    totalParcialidad = Some(BigDecimal("34.87")),
                    montoDiario = Some(BigDecimal("34.87")),
                    ingresoAcumulable = BigDecimal("34.87"),
                    ingresoNoAcumulable = BigDecimal("34.87")
                  )
                ),
                separacionIndemnizacion = Some(
                  SeparacionIndemnizacion(
                    totalPagado = BigDecimal("34.87"),
                    numAniosServicio = 4,
                    ultimoSueldoMensOrd = BigDecimal("34.87"),
                    ingresoAcumulable = BigDecimal("34.87"),
                    ingresoNoAcumulable = BigDecimal("34.87")
                  )
                )
              )
            ),
            deducciones = Some(
              Deducciones(
                totalOtrasDeducciones = Some(BigDecimal("34.87")),
                totalImpuestosRetenidos = Some(BigDecimal("34.87")),
                deduccion = Seq(
                  Deduccion(
                    tipoDeduccion = "tipoDeduccion",
                    clave = "clave",
                    concepto = "concepto",
                    importe = BigDecimal("34.87")
                  )
                )
              )
            ),
            otrosPagos = Some(
              OtrosPagos(
                otroPago = Seq(
                  OtroPago(
                    tipoOtroPago = "tipoOtroPago",
                    clave = "clave",
                    concepto = "concepto",
                    importe = BigDecimal("34.87"),
                    subsidioAlEmpleo = Some(
                      SubsidioAlEmpleo(
                        subsidioCausado = BigDecimal("24")
                      )
                    ),
                    compensacionSaldosAFavor = Some(
                      CompensacionSaldo(
                        saldoAFavor = BigDecimal("345"),
                        anio = 2020.toShort,
                        remanenteSalFav = BigDecimal("345")
                      )
                    )
                  )
                )
              )
            ),
            incapacidades = Some(
              Incapacidades(
                incapacidad = Seq(
                  Incapacidad(
                    diasIncapacidad = 4,
                    tipoIncapacidad = "TipoIncapacidad",
                    importeMonetario = Some(BigDecimal("345"))
                  )
                )
              )
            )
          )
        )
      )
      val complementosBuilderImpl = new ComplementosBuilderImpl(new XmlTimeUtilsImpl(new EmpresalitUtilsImpl))
      val complementosInvoice: Comprobante.Complemento = complementosBuilderImpl.execute(new ObjectFactory(), complementos)
      val nomina = complementosInvoice.getAny.get(0).asInstanceOf[Nomina]

      "Nomina" - {
        "Atributo requerido para la expresión de la versión del complemento." in {
          nomina.getVersion shouldEqual "1.2"
        }
        "Atributo requerido para indicar el tipo de nómina, puede ser O= Nómina ordinaria o E= Nómina extraordinaria." in {
          nomina.getTipoNomina shouldEqual CTipoNomina.valueOf("O")
        }
        "Atributo requerido para la expresión de la fecha efectiva de erogación del gasto. Se expresa en la forma AAAA-MM-DD" in {
          nomina.getFechaPago.toString shouldEqual "2021-04-09"
        }
        "Atributo requerido para la expresión de la fecha inicial del período de pago" in {
          nomina.getFechaInicialPago.toString shouldEqual "2021-04-09"
        }
        "Atributo requerido para la expresión de la fecha final del período de pago." in {
          nomina.getFechaFinalPago.toString shouldEqual "2021-04-09"
        }
        "Atributo requerido para la expresión del número o la fracción de días pagados." in {
          nomina.getNumDiasPagados shouldEqual BigDecimal("2.5").bigDecimal
        }
        "Atributo condicional para representar la suma de las percepciones." in {
          nomina.getTotalPercepciones shouldEqual BigDecimal("24").bigDecimal
        }
        "Atributo condicional para representar la suma de las deducciones aplicables." in {
          nomina.getTotalDeducciones shouldEqual BigDecimal("24").bigDecimal
        }
        "Atributo condicional para representar la suma de otros pagos." in {
          nomina.getTotalOtrosPagos shouldEqual BigDecimal("24").bigDecimal
        }
        "emisor" - {
          "Atributo condicional para expresar la CURP del emisor del comprobante de nómina cuando es una persona física." in {
            nomina.getEmisor.getCurp shouldEqual "CURP"
          }
          "Atributo condicional para expresar el registro patronal, clave de ramo - pagaduría o la que le asigne la institución de seguridad social al patrón" in {
            nomina.getEmisor.getRegistroPatronal shouldEqual "RegistroPatronal"
          }
          "Atributo opcional para expresar el RFC de la persona que fungió como patrón cuando el pago al trabajador se realice a través de un tercero como vehículo" in {
            nomina.getEmisor.getRfcPatronOrigen shouldEqual "RFC"
          }
          "entidad SNCF" - {
            "Identifica el origen del recurso utilizado para el pago de nómina del personal que presta o desempeña un servicio" in {
              nomina.getEmisor.getEntidadSNCF.getOrigenRecurso shouldEqual COrigenRecurso.valueOf("IP")
            }
            "Atributo condicional para expresar el monto del recurso pagado con cargo a sus participaciones u otros ingresos locales" in {
              nomina.getEmisor.getEntidadSNCF.getMontoRecursoPropio shouldEqual BigDecimal("21.324").bigDecimal
            }
          }
        }
        "receptor" - {
          "Atributo requerido para expresar la CURP del receptor del comprobante de nómina." in {
            nomina.getReceptor.getCurp shouldEqual "CURP"
          }
          "Atributo condicional para expresar el número de seguridad social del trabajador." in {
            nomina.getReceptor.getNumSeguridadSocial shouldEqual "numSeguridadSocial"
          }
          "Atributo condicional para expresar la fecha de inicio de la relación laboral entre el empleador y el empleado." in {
            nomina.getReceptor.getFechaInicioRelLaboral.toString shouldEqual "2021-04-09"
          }
          "Atributo condicional para expresar el número de semanas o el periodo de años, meses y días que el empleado ha mantenido relación laboral con el empleador." in {
            nomina.getReceptor.getAntigüedad shouldEqual "antiguedad"
          }
          "Atributo requerido para expresar el tipo de contrato que tiene el trabajador." in {
            nomina.getReceptor.getTipoContrato shouldEqual "tipoContrato"
          }
          "Atributo opcional para indicar si el trabajador está asociado a un sindicato. " in {
            nomina.getReceptor.getSindicalizado shouldEqual "sindicalizado"
          }
          "Atributo condicional para expresar el tipo de jornada que cubre el trabajador." in {
            nomina.getReceptor.getTipoJornada shouldEqual "tipoJornada"
          }
          "Atributo requerido para la expresión de la clave del régimen por el cual se tiene contratado al trabajador." in {
            nomina.getReceptor.getTipoRegimen shouldEqual "tipoRegimen"
          }
          "Atributo requerido para expresar el número de empleado de 1 a 15 posiciones." in {
            nomina.getReceptor.getNumEmpleado shouldEqual "numEmpleado"
          }
          "Atributo opcional para la expresión del departamento o área a la que pertenece el trabajador." in {
            nomina.getReceptor.getDepartamento shouldEqual "departamento"
          }
          "Atributo opcional para la expresión del puesto asignado al empleado o actividad que realiza." in {
            nomina.getReceptor.getPuesto shouldEqual "puesto"
          }
          "Atributo opcional para expresar la clave conforme a la Clase en que deben inscribirse los patrones, de acuerdo con las actividades que desempeñan sus trabajadores" in {
            nomina.getReceptor.getRiesgoPuesto shouldEqual "riesgoPuesto"
          }
          "Atributo requerido para la forma en que se establece el pago del salario." in {
            nomina.getReceptor.getPeriodicidadPago shouldEqual "periodicidadPago"
          }
          "Atributo condicional para la expresión de la clave del Banco conforme al catálogo, donde se realiza el depósito de nómina." in {
            nomina.getReceptor.getBanco shouldEqual "banco"
          }
          "Atributo condicional para la expresión de la cuenta bancaria a 11 posiciones o número de teléfono celular a 10 posiciones o número de tarjeta de crédito o débito" in {
            nomina.getReceptor.getCuentaBancaria shouldEqual BigInt(23).bigInteger
          }
          "Atributo opcional para expresar la retribución otorgada al trabajador, que se integra por los pagos hechos en efectivo" in {
            nomina.getReceptor.getSalarioBaseCotApor shouldEqual BigDecimal("23.232").bigDecimal
          }
          "Atributo opcional para expresar el salario que se integra con los pagos hechos en efectivo" in {
            nomina.getReceptor.getSalarioDiarioIntegrado shouldEqual BigDecimal("32.35").bigDecimal
          }
          "Atributo requerido para expresar la clave de la entidad federativa en donde el receptor del recibo prestó el servicio." in {
            nomina.getReceptor.getClaveEntFed shouldEqual CEstado.valueOf("AGU")
          }
          "subcontratacion" - {
            "Atributo requerido para expresar el RFC de la persona que subcontrata." in {
              nomina.getReceptor.getSubContratacion.get(0).getRfcLabora shouldEqual "RFC"
            }
            "Atributo requerido para expresar el porcentaje del tiempo que prestó sus servicios con el RFC que lo subcontrata." in {
              nomina.getReceptor.getSubContratacion.get(0).getPorcentajeTiempo shouldEqual BigDecimal("34.23").bigDecimal
            }
          }
        }
        "percepciones" - {
          "Atributo condicional para expresar el total de percepciones brutas por sueldos y salarios y conceptos asimilados a salarios." in {
            nomina.getPercepciones.getTotalSueldos shouldEqual BigDecimal("345").bigDecimal
          }
          "Atributo condicional para expresar el importe exento y gravado de las claves tipo percepción Prima por Antigüedad, Pagos por separación y Indemnizaciones" in {
            nomina.getPercepciones.getTotalSeparacionIndemnizacion shouldEqual BigDecimal("345").bigDecimal
          }
          "Atributo condicional para expresar el importe exento y gravado de las claves tipo percepción Jubilaciones, pensiones o haberes de retiro en una exhibición" in {
            nomina.getPercepciones.getTotalJubilacionPensionRetiro shouldEqual BigDecimal("345").bigDecimal
          }
          "Atributo requerido para expresar el total de percepciones gravadas que se relacionan en el comprobante." in {
            nomina.getPercepciones.getTotalGravado shouldEqual BigDecimal("24").bigDecimal
          }
          "Atributo requerido para expresar el total de percepciones exentas que se relacionan en el comprobante." in {
            nomina.getPercepciones.getTotalExento shouldEqual BigDecimal("24").bigDecimal
          }
          "percepcion" - {
            "Atributo requerido para expresar la Clave agrupadora bajo la cual se clasifica la percepción." in {
              nomina.getPercepciones.getPercepcion.get(0).getTipoPercepcion shouldEqual "tipoPersepcion"
            }
            "Atributo requerido para expresar la clave de percepción de nómina propia de la contabilidad de cada patrón" in {
              nomina.getPercepciones.getPercepcion.get(0).getClave shouldEqual "clave"
            }
            "Atributo requerido para la descripción del concepto de percepción" in {
              nomina.getPercepciones.getPercepcion.get(0).getConcepto shouldEqual "concepto"
            }
            "Atributo requerido, representa el importe gravado de un concepto de percepción." in {
              nomina.getPercepciones.getPercepcion.get(0).getImporteGravado shouldEqual BigDecimal("34").bigDecimal
            }
            "Atributo requerido, representa el importe exento de un concepto de percepción." in {
              nomina.getPercepciones.getPercepcion.get(0).getImporteExento shouldEqual BigDecimal("242").bigDecimal
            }
            "accionesOTitulos" - {
              "Atributo requerido para expresar el valor de mercado de las Acciones o Títulos valor al ejercer la opción." in {
                nomina.getPercepciones.getPercepcion.get(0).getAccionesOTitulos.getValorMercado shouldEqual BigDecimal("23.21").bigDecimal
              }
              "Atributo requerido para expresar el precio establecido al otorgarse la opción de ingresos en acciones o títulos valor." in {
                nomina.getPercepciones.getPercepcion.get(0).getAccionesOTitulos.getPrecioAlOtorgarse shouldEqual BigDecimal("32.65").bigDecimal
              }
            }
            "horasExtra" - {
              "Atributo requerido para expresar el número de días en que el trabajador realizó horas extra en el periodo." in {
                nomina.getPercepciones.getPercepcion.get(0).getHorasExtra.get(0).getDias shouldEqual 23
              }
              "Atributo requerido para expresar el tipo de pago de las horas extra." in {
                nomina.getPercepciones.getPercepcion.get(0).getHorasExtra.get(0).getTipoHoras shouldEqual "tipoHoras"
              }
              "Atributo requerido para expresar el número de horas extra trabajadas en el periodo." in {
                nomina.getPercepciones.getPercepcion.get(0).getHorasExtra.get(0).getHorasExtra shouldEqual 43
              }
              "Atributo requerido para expresar el importe pagado por las horas extra." in {
                nomina.getPercepciones.getPercepcion.get(0).getHorasExtra.get(0).getImportePagado shouldEqual BigDecimal("43").bigDecimal
              }
            }
          }
          "jubilacionPensionRetiro" - {
            "Atributo condicional que indica el monto total del pago cuando se realiza en una sola exhibición." in {
              nomina.getPercepciones.getJubilacionPensionRetiro.getTotalUnaExhibicion shouldEqual BigDecimal("34.87").bigDecimal
            }
            "Atributo condicional para expresar los ingresos totales por pago cuando se hace en parcialidades." in {
              nomina.getPercepciones.getJubilacionPensionRetiro.getTotalParcialidad shouldEqual BigDecimal("34.87").bigDecimal
            }
            "Atributo condicional para expresar el monto diario percibido por jubilación, pensiones o haberes de retiro cuando se realiza en parcialidades." in {
              nomina.getPercepciones.getJubilacionPensionRetiro.getMontoDiario shouldEqual BigDecimal("34.87").bigDecimal
            }
            "Atributo requerido para expresar los ingresos acumulables." in {
              nomina.getPercepciones.getJubilacionPensionRetiro.getIngresoAcumulable shouldEqual BigDecimal("34.87").bigDecimal
            }
            "Atributo requerido para expresar los ingresos no acumulables." in {
              nomina.getPercepciones.getJubilacionPensionRetiro.getIngresoNoAcumulable shouldEqual BigDecimal("34.87").bigDecimal
            }
          }
          "separacionIndemnizacion" - {
            "Atributo requerido que indica el monto total del pago." in {
              nomina.getPercepciones.getSeparacionIndemnizacion.getTotalPagado shouldEqual BigDecimal("34.87").bigDecimal
            }
            "Atributo requerido para expresar el número de años de servicio del trabajador. " in {
              nomina.getPercepciones.getSeparacionIndemnizacion.getNumAñosServicio shouldEqual 4
            }
            "Atributo requerido que indica el último sueldo mensual ordinario." in {
              nomina.getPercepciones.getSeparacionIndemnizacion.getUltimoSueldoMensOrd shouldEqual BigDecimal("34.87").bigDecimal
            }
            "Atributo requerido para expresar los ingresos acumulables." in {
              nomina.getPercepciones.getSeparacionIndemnizacion.getIngresoAcumulable shouldEqual BigDecimal("34.87").bigDecimal
            }
            "Atributo requerido que indica los ingresos no acumulables." in {
              nomina.getPercepciones.getSeparacionIndemnizacion.getIngresoNoAcumulable shouldEqual BigDecimal("34.87").bigDecimal
            }
          }
        }
        "deducciones" - {
          "Atributo condicional para expresar el total de deducciones que se relacionan en el comprobante, donde la clave de tipo de deducción sea distinta a la 002 correspondiente a ISR." in {
            nomina.getDeducciones.getTotalOtrasDeducciones shouldEqual BigDecimal("34.87").bigDecimal
          }
          "Atributo condicional para expresar el total de los impuestos federales retenidos" in {
            nomina.getDeducciones.getTotalImpuestosRetenidos shouldEqual BigDecimal("34.87").bigDecimal
          }
          "deduccion" - {
            "Atributo requerido para registrar la clave agrupadora que clasifica la deducción." in {
              nomina.getDeducciones.getDeduccion.get(0).getTipoDeduccion shouldEqual "tipoDeduccion"
            }
            "Atributo requerido para la clave de deducción de nómina propia de la contabilidad de cada patrón" in {
              nomina.getDeducciones.getDeduccion.get(0).getClave shouldEqual "clave"
            }
            "Atributo requerido para la descripción del concepto de deducción." in {
              nomina.getDeducciones.getDeduccion.get(0).getConcepto shouldEqual "concepto"
            }
            "Atributo requerido para registrar el importe del concepto de deducción." in {
              nomina.getDeducciones.getDeduccion.get(0).getImporte shouldEqual BigDecimal("34.87").bigDecimal
            }
          }
        }
        "otros pagos" - {
          "otro pago" - {
            "Atributo requerido para expresar la clave agrupadora bajo la cual se clasifica el otro pago." in {
              nomina.getOtrosPagos.getOtroPago.get(0).getTipoOtroPago shouldEqual "tipoOtroPago"
            }
            "Atributo requerido, representa la clave de otro pago de nómina propia de la contabilidad de cada patrón" in {
              nomina.getOtrosPagos.getOtroPago.get(0).getClave shouldEqual "clave"
            }
            "Atributo requerido para la descripción del concepto de otro pago." in {
              nomina.getOtrosPagos.getOtroPago.get(0).getConcepto shouldEqual "concepto"
            }
            "Atributo requerido para expresar el importe del concepto de otro pago." in {
              nomina.getOtrosPagos.getOtroPago.get(0).getImporte shouldEqual BigDecimal("34.87").bigDecimal
            }
            "subsidioAlEmpleo" - {
              "Atributo requerido para expresar el subsidio causado conforme a la tabla del subsidio para el empleo publicada en el Anexo 8 de la RMF vigente." in {
                nomina.getOtrosPagos.getOtroPago.get(0).getSubsidioAlEmpleo.getSubsidioCausado shouldEqual BigDecimal("24").bigDecimal
              }
            }
            "compensacionSaldosAFavor" - {
              "Atributo requerido para expresar el saldo a favor determinado por el patrón al trabajador en periodos o ejercicios anteriores." in {
                nomina.getOtrosPagos.getOtroPago.get(0).getCompensacionSaldosAFavor.getSaldoAFavor shouldEqual BigDecimal("345").bigDecimal
              }
              "Atributo requerido para expresar el año en que se determinó el saldo a favor del trabajador por el patrón que se incluye en el campo “RemanenteSalFav”." in {
                nomina.getOtrosPagos.getOtroPago.get(0).getCompensacionSaldosAFavor.getAño shouldEqual 2020
              }
              "Atributo requerido para expresar el remanente del saldo a favor del trabajador." in {
                nomina.getOtrosPagos.getOtroPago.get(0).getCompensacionSaldosAFavor.getRemanenteSalFav shouldEqual BigDecimal("345").bigDecimal
              }
            }
          }
        }
        "incapacidades" - {
          "incapacidad" - {
            "Atributo requerido para expresar el número de días enteros que el trabajador se incapacitó en el periodo." in {
              nomina.getIncapacidades.getIncapacidad.get(0).getDiasIncapacidad shouldEqual 4
            }
            "Atributo requerido para expresar la razón de la incapacidad." in {
              nomina.getIncapacidades.getIncapacidad.get(0).getTipoIncapacidad shouldEqual "TipoIncapacidad"
            }
            "Atributo condicional para expresar el monto del importe monetario de la incapacidad." in {
              nomina.getIncapacidades.getIncapacidad.get(0).getImporteMonetario shouldEqual BigDecimal("345").bigDecimal
            }
          }
        }
      }
    }
  }
}
