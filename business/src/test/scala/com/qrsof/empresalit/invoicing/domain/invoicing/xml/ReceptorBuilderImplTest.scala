package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.generateinvoice.ReceptorInvoice
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.{CUsoCFDI, ObjectFactory}
import org.mockito.Mockito
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class ReceptorBuilderImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "ReceptorBuilderImpl" - {
    val usoCfdiFactory = mock[UsoCfdiFactory]
    val factory = new ObjectFactory
    val receptorBuilderImpl = new ReceptorBuilderImpl(usoCfdiFactory)
    val receptor = ReceptorInvoice(
      rfc = "XAXX010101000",
      usoCfdi = "G01",
      name = Some("razonsocial")
    )

    Mockito.when(usoCfdiFactory.getInstance("G01")) thenReturn CUsoCFDI.G_01

    "execute" in {
      val receptorResult = receptorBuilderImpl.execute(factory, receptor)
      receptorResult.getRfc shouldEqual receptor.rfc
      receptorResult.getUsoCFDI shouldEqual CUsoCFDI.G_01
      receptorResult.getNombre shouldEqual receptor.name.get
    }
  }

}
