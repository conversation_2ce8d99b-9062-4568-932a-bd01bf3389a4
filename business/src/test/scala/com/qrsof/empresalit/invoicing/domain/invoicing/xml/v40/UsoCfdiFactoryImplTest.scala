package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.PathAnyFreeSpecAppTest
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CUsoCFDI

class UsoCfdiFactoryImplTest extends PathAnyFreeSpecAppTest {

  "UsoCfdiFactoryImpl" - {
    "getInstance" in {
      val usoCfdiFactoryImpl = new UsoCfdiFactoryImpl
      usoCfdiFactoryImpl.getInstance("G01") shouldEqual CUsoCFDI.G_01
    }
  }
}
