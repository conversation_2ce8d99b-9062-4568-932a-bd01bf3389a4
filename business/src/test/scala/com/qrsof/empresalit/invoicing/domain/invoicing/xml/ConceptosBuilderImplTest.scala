package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.conceptos.impuestos.{ConceptoRetencion, ConceptoTraslado}
import com.qrsof.empresalit.invoicing.domain.conceptos.{Concepto, ConceptosImpuestos}
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.{CTipoFactor, ObjectFactory}
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class ConceptosBuilderImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "ConceptosBuilderImpl" - {
    val conceptosBuilderImpl = new ConceptosBuilderImpl
    val factory = new ObjectFactory
    val conceptos = Seq(
      Concepto(
        claveProductoServicio = "clave",
        cantidad = BigDecimal(1),
        claveUnidad = "unidad1",
        valorUnitario = BigDecimal(1),
        descripcion = "descripcion1",
        descuento = Some(BigDecimal("2")),
        importe = BigDecimal(1)
      ),
      Concepto(
        claveProductoServicio = "clave2",
        cantidad = BigDecimal(2),
        claveUnidad = "unidad2",
        valorUnitario = BigDecimal(2),
        descripcion = "descripcion2",
        importe = BigDecimal(2),
        impuestos = Some(ConceptosImpuestos(
          traslados = Seq(ConceptoTraslado(BigDecimal(2), "002", "Tasa", BigDecimal("0.160000"), BigDecimal(3))),
          retenciones = Seq(ConceptoRetencion(BigDecimal(2), "tipo", "Tasa", BigDecimal("0.160000"), BigDecimal(4)))
        ))
      )
    )

    "when: bind conceptos from request" in {
      val conceptosResult = conceptosBuilderImpl.execute(factory, conceptos)
      conceptosResult.getConcepto.get(0).getClaveProdServ shouldEqual conceptos(0).claveProductoServicio
      conceptosResult.getConcepto.get(0).getClaveUnidad shouldEqual conceptos(0).claveUnidad
      conceptosResult.getConcepto.get(0).getDescripcion shouldEqual conceptos(0).descripcion
      conceptosResult.getConcepto.get(0).getCantidad.doubleValue() shouldEqual conceptos(0).cantidad.doubleValue
      conceptosResult.getConcepto.get(0).getValorUnitario.doubleValue shouldEqual conceptos(0).valorUnitario.doubleValue
      conceptosResult.getConcepto.get(0).getImporte.doubleValue shouldEqual conceptos(0).importe.doubleValue
      conceptosResult.getConcepto.get(0).getDescuento.toString shouldEqual conceptos(0).descuento.get.toString()

      conceptosResult.getConcepto.get(1).getClaveProdServ shouldEqual conceptos(1).claveProductoServicio
      conceptosResult.getConcepto.get(1).getClaveUnidad shouldEqual conceptos(1).claveUnidad
      conceptosResult.getConcepto.get(1).getDescripcion shouldEqual conceptos(1).descripcion
      conceptosResult.getConcepto.get(1).getCantidad.doubleValue shouldEqual conceptos(1).cantidad
      conceptosResult.getConcepto.get(1).getValorUnitario.doubleValue shouldEqual conceptos(1).valorUnitario
      conceptosResult.getConcepto.get(1).getImporte.doubleValue shouldEqual conceptos(1).importe

      conceptosResult.getConcepto.get(1).getImpuestos.getRetenciones.getRetencion.get(0).getBase.doubleValue() shouldEqual BigDecimal(2).doubleValue
      conceptosResult.getConcepto.get(1).getImpuestos.getRetenciones.getRetencion.get(0).getImporte.doubleValue() shouldEqual BigDecimal(4).doubleValue
      conceptosResult.getConcepto.get(1).getImpuestos.getRetenciones.getRetencion.get(0).getImpuesto shouldEqual "tipo"
      conceptosResult.getConcepto.get(1).getImpuestos.getRetenciones.getRetencion.get(0).getTasaOCuota.doubleValue() shouldEqual BigDecimal("0.160000").doubleValue
      conceptosResult.getConcepto.get(1).getImpuestos.getRetenciones.getRetencion.get(0).getTipoFactor shouldEqual CTipoFactor.fromValue("Tasa")

      conceptosResult.getConcepto.get(1).getImpuestos.getTraslados.getTraslado.get(0).getBase.doubleValue() shouldEqual BigDecimal(2).doubleValue
      conceptosResult.getConcepto.get(1).getImpuestos.getTraslados.getTraslado.get(0).getImporte.doubleValue() shouldEqual BigDecimal(3).doubleValue
      conceptosResult.getConcepto.get(1).getImpuestos.getTraslados.getTraslado.get(0).getImpuesto shouldEqual "002"
      conceptosResult.getConcepto.get(1).getImpuestos.getTraslados.getTraslado.get(0).getTasaOCuota.doubleValue() shouldEqual BigDecimal("0.160000").doubleValue
      conceptosResult.getConcepto.get(1).getImpuestos.getTraslados.getTraslado.get(0).getTipoFactor shouldEqual CTipoFactor.fromValue("Tasa")
    }
  }

}
