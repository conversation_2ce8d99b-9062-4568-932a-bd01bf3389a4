package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.generateinvoice.PrivateKeyFactoryImpl
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class SignOriginalChainImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "SignOriginalChainImpl" - {
    val signOriginalChainImpl = new SignOriginalChainImpl

    val urlResource = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/CSD_XOCHILT_CASAS_CHAVEZ_CACX7605101P8_20190528_173544.key")
    val privateKeyContent = urlResource.openStream().readAllBytes()

    val privateKeyFactoryImpl = new PrivateKeyFactoryImpl
    val privateKey = privateKeyFactoryImpl.getInstance(privateKeyContent, "12345678a")

    "execute" in {
      val signedResult = signOriginalChainImpl.execute("original".getBytes, privateKey)
      println(signedResult)
      signedResult shouldNot (be(null))
    }
  }
}
