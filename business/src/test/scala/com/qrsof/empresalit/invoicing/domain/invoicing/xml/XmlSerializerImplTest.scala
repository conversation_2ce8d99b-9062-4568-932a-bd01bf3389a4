package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.sat.models.cfdi33.Comprobante
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j

class XmlSerializerImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  private val logger = slf4j.LoggerFactory.getLogger(classOf[XmlSerializerImplTest])

  "XmlSerializerImpl" - {
    val xmlSerializerImpl = new XmlSerializerImpl
    "execute" in {
      val comprobante = new Comprobante
      val xml = xmlSerializerImpl.execute(comprobante)
      val xmlString = new String(xml)
      logger.info("XML: {}", xmlString)
      xmlString shouldNot be(null)
      // "<cfdi:Comprobante xmlns:cfdi=\"http://www.sat.gob.mx/cfd/3\" xmlns:cartaporte20=\"http://www.sat.gob.mx/CartaPorte20\" xmlns:pago10=\"http://www.sat.gob.mx/Pagos\" xmlns:tfd=\"http://www.sat.gob.mx/TimbreFiscalDigital\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"http://www.sat.gob.mx/cfd/3 http://www.sat.gob.mx/sitio_internet/cfd/3/cfdv33.xsd\"/>"
    }
  }
}
