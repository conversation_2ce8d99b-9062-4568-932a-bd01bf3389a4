package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.complementos.Complementos
import com.qrsof.empresalit.invoicing.domain.complementos.pagos.{DoctoRelacionado, Pago, RecepcionPago}
import com.qrsof.empresalit.invoicing.domain.invoicing.v30.GenerateInvoiceRequest
import com.qrsof.empresalit.invoicing.generateinvoice.ReceptorInvoice
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers

class FixNameSpacesXmlImplTest extends PathAnyFreeSpec with Matchers {


  "Fix xml name spaces" - {
    val xml = "<cfdi:Comprobante xmlns:cfdi=\"http://www.sat.gob.mx/cfd/3\" xmlns:pago10=\"http://www.sat.gob.mx/Pagos\" xmlns:tfd=\"http://www.sat.gob.mx/TimbreFiscalDigital\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" Version=\"3.3\" Fecha=\"2021-04-15T14:31:40\" Sello=\"EgnluOnPhM6yxUW12nnau1RoHAOIcovu2KrJEe6OI6TsHXb9GHZyE5TxWSY1SjsWD56dlVCGSlujBK0Fu1l45QkMTvtlRT3xOvJvya+C+u/RPROhbEhusf+HBD8Ro9eg1KHbzvpJ3W7MruSdmmqObhOah+HC6HyOTWRnJTB4RGXbBPR/tZ07nc0VUBWxbNI3t5Fxd4QyDTkPk/8x3566BmDD2pJ8uOU1W8+9/E6IR4Nark42+bdL9g1p8hhgotYYsK5BWRmHPh/c/o/Xh2Rp9m/fkurQlBJN4ToDGvgSp55kZmPIfblW4apGEwL8bQY2hvJPseFp2l0ArrZk546sdg==\" FormaPago=\"02\" NoCertificado=\"30001000000400002335\" Certificado=\"MIIFijCCA3KgAwIBAgIUMzAwMDEwMDAwMDA0MDAwMDIzMzUwDQYJKoZIhvcNAQELBQAwggErMQ8wDQYDVQQDDAZBQyBVQVQxLjAsBgNVBAoMJVNFUlZJQ0lPIERFIEFETUlOSVNUUkFDSU9OIFRSSUJVVEFSSUExGjAYBgNVBAsMEVNBVC1JRVMgQXV0aG9yaXR5MSgwJgYJKoZIhvcNAQkBFhlvc2Nhci5tYXJ0aW5lekBzYXQuZ29iLm14MR0wGwYDVQQJDBQzcmEgY2VycmFkYSBkZSBjYWRpejEOMAwGA1UEEQwFMDYzNzAxCzAJBgNVBAYTAk1YMRkwFwYDVQQIDBBDSVVEQUQgREUgTUVYSUNPMREwDwYDVQQHDAhDT1lPQUNBTjERMA8GA1UELRMIMi41LjQuNDUxJTAjBgkqhkiG9w0BCQITFnJlc3BvbnNhYmxlOiBBQ0RNQS1TQVQwHhcNMTkwNTI5MTk1MDAxWhcNMjMwNTI5MTk1MDAxWjCBsTEdMBsGA1UEAxMUWE9DSElMVCBDQVNBUyBDSEFWRVoxHTAbBgNVBCkTFFhPQ0hJTFQgQ0FTQVMgQ0hBVkVaMR0wGwYDVQQKExRYT0NISUxUIENBU0FTIENIQVZFWjEWMBQGA1UELRMNQ0FDWDc2MDUxMDFQODEbMBkGA1UEBRMSQ0FDWDc2MDUxME1HVFNIQzA0MR0wGwYDVQQLExRYT0NISUxUIENBU0FTIENIQVZFWjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKvYaTyUuvtUIwymg88xS3Ri7W2J758lusEgwUaxdyIyxLQK2736yrK6RotjDu7pfbzqD5CyJ6gkT70x29QNe5MHUgcNPnARoYK2+0a0kWjpweNqjb0pCOMevUCzblF72c7MkErbW5qolIRIsI4UFSZOlLDI9O9lKm0Tk85Ab0siaqUefGl9lOOkjPT7Pr/CkHk4jxZJOzY2cWVey00vgFh/t9xxbF6Rvi738dfZ9R9h2TVEpaRRYb+m4rpTMos6HEfmhuKSsNe2/M8NDxC4zkcwdC5WS9NhNd4kUQ+7TFLmIo14tOXXSBZVVsg49/L+lq/eh44K1Ze3iSW9M7Ii8mECAwEAAaMdMBswDAYDVR0TAQH/BAIwADALBgNVHQ8EBAMCBsAwDQYJKoZIhvcNAQELBQADggIBAK1bJ6vhkqIF0Y4XnDUFQ/nZUOsQCXbs+czwu62kVaOffHWcKhJ1mTaSwkmFoqykV3VAib7RYKYTXcERow21uGEfnOhNxeSi4l2An7y6PtJOGy4wTjAX++iAeoh+ZDel3VBhvNYv6IZAcsVqdTl0Mfs/E7EuCc6YqumEBTbFTMcp92A31HWHqkI+UnXcogYndsaIK2m+iER6AHhUokfOjOiSJmSEovaXmaJVkmjbv3g07FeMB2fZ8fp3rrRtHjgTzbZyPY2LjhBnV/0vaTnGZH4l1RWZ+dgFn5/09GJJYLgaTBHpuRNBI6JRQ9/iR4NMjOEbNXDIpKibnIg3zG1yqQtUwQBUic0lF958n1KOg7fM+Msgq9Fjg2FY3aI2DAoGWKJR8PTiFbXy+Arpzd669QWzgIusT7KLwlFhKm+a+9dmE2lp0WUj2QH0drJdfSuvqA9ZIu3I0yvnpUT0jlun0PZyoloTOd5X+8z2kLSAxbZaOu9I4XATcwZntZK0FIueh4Htom524ne/MNp6nEXzpxV4HPiW626VoykB4AHJwIp7ljcg8D4IJ7oIF/0UEduwbsx3amAfDeg/9YtPRC4j5M0h3l7zLcJ2/BFwr8qYM74d0v00Hm4msirCPkE+kr58oL1NjwN7vvPA96HbSeBohH+f/X8FcZ965FhmjmZWgj0W\" SubTotal=\"10\" Moneda=\"MXN\" Total=\"11.6\" TipoDeComprobante=\"I\" MetodoPago=\"PUE\" LugarExpedicion=\"42083\" xsi:schemaLocation=\"http://www.sat.gob.mx/cfd/3 http://www.sat.gob.mx/sitio_internet/cfd/3/cfdv33.xsd\"><cfdi:Emisor Rfc=\"CACX7605101P8\" Nombre=\"XOCHILT CASAS CHAVEZ\" RegimenFiscal=\"621\"/><cfdi:Receptor Rfc=\"AAA010101AAA\" UsoCFDI=\"G01\"/><cfdi:Conceptos><cfdi:Concepto ClaveProdServ=\"81111500\" Cantidad=\"1\" ClaveUnidad=\"E48\" Descripcion=\"descripcion\" ValorUnitario=\"10\" Importe=\"10\"><cfdi:Impuestos><cfdi:Traslados><cfdi:Traslado Base=\"10\" Impuesto=\"002\" TipoFactor=\"Tasa\" TasaOCuota=\"0.160000\" Importe=\"1.6\"/></cfdi:Traslados></cfdi:Impuestos></cfdi:Concepto></cfdi:Conceptos><cfdi:Impuestos TotalImpuestosTrasladados=\"1.6\"><cfdi:Traslados><cfdi:Traslado Impuesto=\"002\" TipoFactor=\"Tasa\" TasaOCuota=\"0.160000\" Importe=\"1.6\"/></cfdi:Traslados></cfdi:Impuestos><cfdi:Complemento><pago10:Pagos Version=\"1.0\"><pago10:Pago FechaPago=\"2021-04-09T23:25:42\" FormaDePagoP=\"01\" MonedaP=\"MXN\" Monto=\"12768.00\"/></pago10:Pagos></cfdi:Complemento></cfdi:Comprobante>"
    val fixNameSpacesXmlImpl = new FixNameSpacesXmlImpl
    val generateInvoiceRequest = GenerateInvoiceRequest(
      version = "3.3",
      companyKey = "emisorKey",
      lugarExpedicion = "42083",
      formaPago = Some("FP"),
      metodoPago = Some("PUE"),
      moneda = "MXN",
      tipoComprobante = "I",
      subTotal = BigDecimal(1),
      total = BigDecimal(2),
      receptor = Some(ReceptorInvoice(
        rfc = "AAA010101AAA",
        usoCfdi = "Gastos en general"
      )),
      conceptos = Nil,
      complementos = Some(Complementos(
        recepcionPagos = Some(RecepcionPago(
          version = "1.0",
          Seq(
            Pago(
              fechaPago = "2021-04-09T23:25:42",
              formaDePagoP = "01",
              monedaP = "MXN",
              monto = BigDecimal("12768.00"),
              doctosRelacionesdos = Seq(
                DoctoRelacionado(
                  idDocumento = "43EA82E4-FCC5-421F-879E-0D0C1A5807E5",
                  monedaDR = "MXN",
                  tipoCambioDR = Some(BigDecimal("10.20")),
                  metodoDePagoDR = "PUE"
                )
              )
            )
          )
        ))
      ))
    )
    val fixedXml = fixNameSpacesXmlImpl.execute(xml.getBytes, generateInvoiceRequest)
    "then fix namesapaces" in {
      new String(fixedXml) shouldEqual "<cfdi:Comprobante xmlns:cfdi=\"http://www.sat.gob.mx/cfd/3\" xmlns:pago10=\"http://www.sat.gob.mx/Pagos\" xmlns:tfd=\"http://www.sat.gob.mx/TimbreFiscalDigital\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" Version=\"3.3\" Fecha=\"2021-04-15T14:31:40\" Sello=\"EgnluOnPhM6yxUW12nnau1RoHAOIcovu2KrJEe6OI6TsHXb9GHZyE5TxWSY1SjsWD56dlVCGSlujBK0Fu1l45QkMTvtlRT3xOvJvya+C+u/RPROhbEhusf+HBD8Ro9eg1KHbzvpJ3W7MruSdmmqObhOah+HC6HyOTWRnJTB4RGXbBPR/tZ07nc0VUBWxbNI3t5Fxd4QyDTkPk/8x3566BmDD2pJ8uOU1W8+9/E6IR4Nark42+bdL9g1p8hhgotYYsK5BWRmHPh/c/o/Xh2Rp9m/fkurQlBJN4ToDGvgSp55kZmPIfblW4apGEwL8bQY2hvJPseFp2l0ArrZk546sdg==\" FormaPago=\"02\" NoCertificado=\"30001000000400002335\" Certificado=\"MIIFijCCA3KgAwIBAgIUMzAwMDEwMDAwMDA0MDAwMDIzMzUwDQYJKoZIhvcNAQELBQAwggErMQ8wDQYDVQQDDAZBQyBVQVQxLjAsBgNVBAoMJVNFUlZJQ0lPIERFIEFETUlOSVNUUkFDSU9OIFRSSUJVVEFSSUExGjAYBgNVBAsMEVNBVC1JRVMgQXV0aG9yaXR5MSgwJgYJKoZIhvcNAQkBFhlvc2Nhci5tYXJ0aW5lekBzYXQuZ29iLm14MR0wGwYDVQQJDBQzcmEgY2VycmFkYSBkZSBjYWRpejEOMAwGA1UEEQwFMDYzNzAxCzAJBgNVBAYTAk1YMRkwFwYDVQQIDBBDSVVEQUQgREUgTUVYSUNPMREwDwYDVQQHDAhDT1lPQUNBTjERMA8GA1UELRMIMi41LjQuNDUxJTAjBgkqhkiG9w0BCQITFnJlc3BvbnNhYmxlOiBBQ0RNQS1TQVQwHhcNMTkwNTI5MTk1MDAxWhcNMjMwNTI5MTk1MDAxWjCBsTEdMBsGA1UEAxMUWE9DSElMVCBDQVNBUyBDSEFWRVoxHTAbBgNVBCkTFFhPQ0hJTFQgQ0FTQVMgQ0hBVkVaMR0wGwYDVQQKExRYT0NISUxUIENBU0FTIENIQVZFWjEWMBQGA1UELRMNQ0FDWDc2MDUxMDFQODEbMBkGA1UEBRMSQ0FDWDc2MDUxME1HVFNIQzA0MR0wGwYDVQQLExRYT0NISUxUIENBU0FTIENIQVZFWjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKvYaTyUuvtUIwymg88xS3Ri7W2J758lusEgwUaxdyIyxLQK2736yrK6RotjDu7pfbzqD5CyJ6gkT70x29QNe5MHUgcNPnARoYK2+0a0kWjpweNqjb0pCOMevUCzblF72c7MkErbW5qolIRIsI4UFSZOlLDI9O9lKm0Tk85Ab0siaqUefGl9lOOkjPT7Pr/CkHk4jxZJOzY2cWVey00vgFh/t9xxbF6Rvi738dfZ9R9h2TVEpaRRYb+m4rpTMos6HEfmhuKSsNe2/M8NDxC4zkcwdC5WS9NhNd4kUQ+7TFLmIo14tOXXSBZVVsg49/L+lq/eh44K1Ze3iSW9M7Ii8mECAwEAAaMdMBswDAYDVR0TAQH/BAIwADALBgNVHQ8EBAMCBsAwDQYJKoZIhvcNAQELBQADggIBAK1bJ6vhkqIF0Y4XnDUFQ/nZUOsQCXbs+czwu62kVaOffHWcKhJ1mTaSwkmFoqykV3VAib7RYKYTXcERow21uGEfnOhNxeSi4l2An7y6PtJOGy4wTjAX++iAeoh+ZDel3VBhvNYv6IZAcsVqdTl0Mfs/E7EuCc6YqumEBTbFTMcp92A31HWHqkI+UnXcogYndsaIK2m+iER6AHhUokfOjOiSJmSEovaXmaJVkmjbv3g07FeMB2fZ8fp3rrRtHjgTzbZyPY2LjhBnV/0vaTnGZH4l1RWZ+dgFn5/09GJJYLgaTBHpuRNBI6JRQ9/iR4NMjOEbNXDIpKibnIg3zG1yqQtUwQBUic0lF958n1KOg7fM+Msgq9Fjg2FY3aI2DAoGWKJR8PTiFbXy+Arpzd669QWzgIusT7KLwlFhKm+a+9dmE2lp0WUj2QH0drJdfSuvqA9ZIu3I0yvnpUT0jlun0PZyoloTOd5X+8z2kLSAxbZaOu9I4XATcwZntZK0FIueh4Htom524ne/MNp6nEXzpxV4HPiW626VoykB4AHJwIp7ljcg8D4IJ7oIF/0UEduwbsx3amAfDeg/9YtPRC4j5M0h3l7zLcJ2/BFwr8qYM74d0v00Hm4msirCPkE+kr58oL1NjwN7vvPA96HbSeBohH+f/X8FcZ965FhmjmZWgj0W\" SubTotal=\"10\" Moneda=\"MXN\" Total=\"11.6\" TipoDeComprobante=\"I\" MetodoPago=\"PUE\" LugarExpedicion=\"42083\" xsi:schemaLocation=\"http://www.sat.gob.mx/Pagos http://www.sat.gob.mx/sitio_internet/cfd/Pagos/Pagos10.xsd http://www.sat.gob.mx/cfd/3 http://www.sat.gob.mx/sitio_internet/cfd/3/cfdv33.xsd\"><cfdi:Emisor Rfc=\"CACX7605101P8\" Nombre=\"XOCHILT CASAS CHAVEZ\" RegimenFiscal=\"621\"/><cfdi:Receptor Rfc=\"AAA010101AAA\" UsoCFDI=\"G01\"/><cfdi:Conceptos><cfdi:Concepto ClaveProdServ=\"81111500\" Cantidad=\"1\" ClaveUnidad=\"E48\" Descripcion=\"descripcion\" ValorUnitario=\"10\" Importe=\"10\"><cfdi:Impuestos><cfdi:Traslados><cfdi:Traslado Base=\"10\" Impuesto=\"002\" TipoFactor=\"Tasa\" TasaOCuota=\"0.160000\" Importe=\"1.6\"/></cfdi:Traslados></cfdi:Impuestos></cfdi:Concepto></cfdi:Conceptos><cfdi:Impuestos TotalImpuestosTrasladados=\"1.6\"><cfdi:Traslados><cfdi:Traslado Impuesto=\"002\" TipoFactor=\"Tasa\" TasaOCuota=\"0.160000\" Importe=\"1.6\"/></cfdi:Traslados></cfdi:Impuestos><cfdi:Complemento><pago10:Pagos Version=\"1.0\"><pago10:Pago FechaPago=\"2021-04-09T23:25:42\" FormaDePagoP=\"01\" MonedaP=\"MXN\" Monto=\"12768.00\"/></pago10:Pagos></cfdi:Complemento></cfdi:Comprobante>"
    }
  }

}
