package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.PathAnyFreeSpecAppTest
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.{GenerateInvoiceRequest, ReceptorInvoice}

class FixNameSpacesXmlImplTest extends PathAnyFreeSpecAppTest {

  "Fix xml name spaces" - {
    val xml = "<cfdi:Comprobante xmlns:cfdi=\"http://www.sat.gob.mx/cfd/4\" xmlns:pago20=\"http://www.sat.gob.mx/Pagos20\" xmlns:cartaporte20=\"http://www.sat.gob.mx/CartaPorte20\" xmlns:nomina12=\"http://www.sat.gob.mx/nomina12\" xmlns:tfd=\"http://www.sat.gob.mx/TimbreFiscalDigital\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"http://www.sat.gob.mx/cfd/4 http://www.sat.gob.mx/sitio_internet/cfd/4/cfdv40.xsd\"/>"
    val fixNameSpacesXmlImpl = new FixNameSpacesXmlImpl
    val generateInvoiceRequest = GenerateInvoiceRequest(
      companyKey = "emisorKey",
      lugarExpedicion = "42083",
      formaPago = Some("FP"),
      metodoPago = Some("PUE"),
      moneda = "MXN",
      tipoComprobante = "I",
      subTotal = BigDecimal(1),
      total = BigDecimal(2),
      receptor = Some(ReceptorInvoice(
        rfc = "AAA010101AAA",
        nombre = "nombrereceptor",
        domicilioFiscalReceptor = "42083",
        regimenFiscalReceptor = "regimen",
        usoCfdi = "Gastos en general",
      )),
      conceptos = Nil,
      exportacion = "0"
    )
    val fixedXml = fixNameSpacesXmlImpl.execute(xml.getBytes, generateInvoiceRequest)
    "then fix namesapaces" in {
      new String(fixedXml) shouldEqual "<cfdi:Comprobante xmlns:cfdi=\"http://www.sat.gob.mx/cfd/4\" xmlns:pago20=\"http://www.sat.gob.mx/Pagos20\" xmlns:cartaporte20=\"http://www.sat.gob.mx/CartaPorte20\" xmlns:nomina12=\"http://www.sat.gob.mx/nomina12\" xmlns:tfd=\"http://www.sat.gob.mx/TimbreFiscalDigital\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"http://www.sat.gob.mx/cfd/4 http://www.sat.gob.mx/sitio_internet/cfd/4/cfdv40.xsd\"/>"
    }
  }

}
