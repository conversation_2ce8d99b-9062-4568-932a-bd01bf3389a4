package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.complementos.Complementos
import com.qrsof.empresalit.invoicing.domain.complementos.pagos.{DoctoRelacionado, Pago, RecepcionPago}
import com.qrsof.empresalit.invoicing.domain.conceptos.Concepto
import com.qrsof.empresalit.invoicing.domain.impuestos.Impuestos
import com.qrsof.empresalit.invoicing.domain.invoicing.IssuerInvoicingData
import com.qrsof.empresalit.invoicing.domain.invoicing.v30.GenerateInvoiceRequest
import com.qrsof.empresalit.invoicing.generateinvoice.ReceptorInvoice
import com.qrsof.empresalit.invoicing.sat.models.cfdi33
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.Comprobante.{Conceptos, Emisor}
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.{CMetodoPago, CMoneda, CTipoDeComprobante, Comprobante}
import org.mockito.ArgumentMatchers
import org.mockito.Mockito.{verify, when}
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import java.security.PrivateKey
import javax.xml.datatype.XMLGregorianCalendar

class ComprobanteBinderImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "ComprobanteBinderImpl" - {

    val emisorBuilder = mock[EmisorBuilder]
    val receptorBuilder = mock[ReceptorBuilder]
    val impuestosBuilder = mock[ImpuestosBuilder]
    val conceptosBuilder = mock[ConceptosBuilder]
    val complementosBuilder = mock[ComplementosBuilder]
    val monedaFactory = mock[MonedaFactory]
    val metodoPagoFactory = mock[MetodoPagoFactory]
    val tipoComprobanteFactory = mock[TipoComprobanteFactory]
    val emisorKey = "emisorKey"
    val privateKey = mock[PrivateKey]
    val xmlCurrentTime = mock[XmlTimeUtils]
    val complemento = mock[Comprobante.Complemento]

    val moneda = CMoneda.MXN
    val tipoComprobante = CTipoDeComprobante.I
    val metodoPago = CMetodoPago.PUE

    val issuerInvoicingData = IssuerInvoicingData(
      certificateNumber = "nocertificado",
      privateKey = privateKey,
      publicKey = "publickey".getBytes,
      rfc = "rfc",
      name = "razonsocial",
      regimenFiscalClave = "regFiscal"
    )
    val impuestos = Impuestos()
    val generateInvoiceRequest = GenerateInvoiceRequest(
      version = "3.3",
      companyKey = emisorKey,
      serie = Some("A"),
      folio = Some("1"),
      lugarExpedicion = "42083",
      condicionesDePago = Some("condicionesDePago"),
      formaPago = Some("FP"),
      metodoPago = Some("PUE"),
      moneda = "MXN",
      tipoComprobante = "I",
      subTotal = BigDecimal(1),
      total = BigDecimal(2),
      descuento = Some(BigDecimal(100)),
      tipoCambio = Some(BigDecimal(12)),
      confirmacion = Some("confirmacion"),
      receptor = Some(
        ReceptorInvoice(
          rfc = "AAA010101AAA",
          usoCfdi = "Gastos en general"
        )
      ),
      conceptos = Seq(
        Concepto(
          claveProductoServicio = "clave",
          cantidad = BigDecimal(1),
          claveUnidad = "unidad",
          valorUnitario = BigDecimal(2),
          descripcion = "descripcion",
          importe = BigDecimal(2)
        )
      ),
      impuestos = Some(impuestos),
      complementos = Some(
        Complementos(
          recepcionPagos = Some(
            RecepcionPago(
              version = "1.0",
              Seq(
                Pago(
                  fechaPago = "2021-04-09T23:25:42",
                  formaDePagoP = "01",
                  monedaP = "MXN",
                  monto = BigDecimal("12768.00"),
                  doctosRelacionesdos = Seq(
                    DoctoRelacionado(
                      idDocumento = "43EA82E4-FCC5-421F-879E-0D0C1A5807E5",
                      monedaDR = "MXN",
                      tipoCambioDR = Some(BigDecimal("10.20")),
                      metodoDePagoDR = "PUE"
                    )
                  )
                )
              )
            )
          )
        )
      )
    )

    val comprobanteBinderImpl = new ComprobanteBinderImpl(
      emisorBuilder,
      receptorBuilder,
      conceptosBuilder,
      xmlCurrentTime,
      monedaFactory,
      tipoComprobanteFactory,
      metodoPagoFactory,
      impuestosBuilder,
      complementosBuilder
    )

    val gregorianCalendar = mock[XMLGregorianCalendar]

    val emisor = new Emisor
    when(emisorBuilder.execute(ArgumentMatchers.any(classOf[cfdi33.ObjectFactory]), ArgumentMatchers.eq(issuerInvoicingData))) thenReturn emisor
    val receptor = new Comprobante.Receptor
    when(receptorBuilder.execute(ArgumentMatchers.any(classOf[cfdi33.ObjectFactory]), ArgumentMatchers.eq(generateInvoiceRequest.receptor.get))) thenReturn receptor
    val conceptos = new Conceptos
    val concepto = new Conceptos.Concepto()
    concepto.setDescripcion("descripcion")
    conceptos.getConcepto.add(concepto)

    when(conceptosBuilder.execute(ArgumentMatchers.any(classOf[cfdi33.ObjectFactory]), ArgumentMatchers.eq(generateInvoiceRequest.conceptos))) thenReturn conceptos
    when(complementosBuilder.execute(ArgumentMatchers.any(classOf[cfdi33.ObjectFactory]), ArgumentMatchers.eq(generateInvoiceRequest.complementos.get))) thenReturn complemento
    when(xmlCurrentTime.getCurrentDate()) thenReturn gregorianCalendar
    when(monedaFactory.getInstace(generateInvoiceRequest.moneda)) thenReturn moneda
    when(tipoComprobanteFactory.getInstance(generateInvoiceRequest.tipoComprobante)) thenReturn tipoComprobante
    when(metodoPagoFactory.getInstance(generateInvoiceRequest.metodoPago.get)) thenReturn metodoPago

    "when: bind comprobante from parameters" - {
      val comprobante = comprobanteBinderImpl.execute(issuerInvoicingData, generateInvoiceRequest)

      "should: build emisor" in {
        verify(emisorBuilder).execute(ArgumentMatchers.any(classOf[cfdi33.ObjectFactory]), ArgumentMatchers.eq(issuerInvoicingData))
        comprobante.getEmisor shouldEqual emisor
      }

      "and: build receptor" in {
        verify(receptorBuilder).execute(ArgumentMatchers.any(classOf[cfdi33.ObjectFactory]), ArgumentMatchers.eq(generateInvoiceRequest.receptor.get))
        comprobante.getReceptor shouldEqual receptor
      }

      "and: comprobante condicionesDePago" in {
        comprobante.getCondicionesDePago shouldEqual "condicionesDePago"
      }

      "and: build impuestos" in {
        verify(impuestosBuilder).execute(ArgumentMatchers.any(classOf[cfdi33.ObjectFactory]), ArgumentMatchers.eq(generateInvoiceRequest.impuestos.get))
        comprobante.getReceptor shouldEqual receptor
      }

      "and: build conceptos" in {
        verify(conceptosBuilder).execute(ArgumentMatchers.any(classOf[cfdi33.ObjectFactory]), ArgumentMatchers.eq(generateInvoiceRequest.conceptos))
        comprobante.getConceptos shouldEqual conceptos
      }

      "and: version should be 3.3" in {
        comprobante.getVersion shouldEqual "3.3"
      }

      "and: set current date" in {
        verify(xmlCurrentTime).getCurrentDate()
        comprobante.getFecha shouldEqual gregorianCalendar
      }

      "and: set certificate number" in {
        verify(xmlCurrentTime).getCurrentDate()
        comprobante.getNoCertificado shouldEqual issuerInvoicingData.certificateNumber
      }

      "and: set subtotal" in {
        verify(xmlCurrentTime).getCurrentDate()
        comprobante.getSubTotal.doubleValue() shouldEqual generateInvoiceRequest.subTotal.doubleValue
      }

      "and: set total" in {
        verify(xmlCurrentTime).getCurrentDate()
        comprobante.getTotal.doubleValue() shouldEqual generateInvoiceRequest.total.doubleValue
      }

      "and: set moneda" in {
        verify(monedaFactory).getInstace(generateInvoiceRequest.moneda)
        comprobante.getMoneda shouldEqual moneda
      }

      "and: set tipo comprobante" in {
        verify(tipoComprobanteFactory).getInstance(generateInvoiceRequest.tipoComprobante)
        comprobante.getTipoDeComprobante shouldEqual tipoComprobante
      }

      "and: set lugar expedicion" in {
        comprobante.getLugarExpedicion shouldEqual generateInvoiceRequest.lugarExpedicion
      }

      "and: set forma pago" in {
        comprobante.getFormaPago shouldEqual generateInvoiceRequest.formaPago.get
      }

      "and: set metodo pago" in {
        verify(metodoPagoFactory).getInstance(generateInvoiceRequest.metodoPago.get)
        comprobante.getMetodoPago shouldEqual metodoPago
      }

      "and: set certificado" in {
        comprobante.getCertificado shouldEqual "cHVibGlja2V5"
      }

      "and: build complementos" in {
        verify(complementosBuilder).execute(ArgumentMatchers.any(classOf[cfdi33.ObjectFactory]), ArgumentMatchers.eq(generateInvoiceRequest.complementos.get))
        comprobante.getComplemento.get(0) shouldEqual complemento
      }

      "and: set descuento" in {
        comprobante.getDescuento.toString shouldEqual "100"
      }

      "and: set tipo cambio" in {
        comprobante.getTipoCambio.toString shouldEqual "12"
      }

      "and: set confirmacion" in {
        comprobante.getConfirmacion shouldEqual "confirmacion"
      }

    }

  }

}
