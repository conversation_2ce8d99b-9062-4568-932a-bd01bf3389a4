package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.invoicing.IssuerInvoicingData
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.ObjectFactory
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import java.security.PrivateKey

class EmisorBuilderImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "EmisorBuilderImpl" - {
    val emisorBuilderImpl = new EmisorBuilderImpl
    val issuerInvoicingData = IssuerInvoicingData(
      certificateNumber = "nocertificado",
      privateKey = mock[PrivateKey],
      publicKey = "publickey".getBytes,
      rfc = "rfc",
      name = "razonsocial",
      regimenFiscalClave = "regFiscal"
    )
    val factory = new ObjectFactory
    "when: build emisor from request" in {
      val emisorResult = emisorBuilderImpl.execute(factory, issuerInvoicingData)
      emisorResult.getNombre shouldEqual issuerInvoicingData.name
      emisorResult.getRegimenFiscal shouldEqual issuerInvoicingData.regimenFiscalClave
      emisorResult.getRfc shouldEqual issuerInvoicingData.rfc
    }
  }

}
