package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.PathAnyFreeSpecAppTest
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CMetodoPago

class MetodoPagoFactoryImplTest extends PathAnyFreeSpecAppTest {

  "MetodoPagoFactory" - {
    val impl = new MetodoPagoFactoryImpl
    "getInstance" in {
      impl.getInstance("PUE") shouldEqual CMetodoPago.PUE
      impl.getInstance("PPD") shouldEqual CMetodoPago.PPD
    }
  }

}
