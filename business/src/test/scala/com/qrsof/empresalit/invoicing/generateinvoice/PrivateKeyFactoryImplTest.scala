package com.qrsof.empresalit.invoicing.generateinvoice

import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import java.security.PrivateKey

class PrivateKeyFactoryImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "PrivateKeyFactoryImpl" - {
    val privateKeyFactoryImpl = new PrivateKeyFactoryImpl

    "when: generate private key instance" in {
      val urlResource = this.getClass.getResource("/RFC-PAC-SC/Personas Fisicas/FIEL_CACX7605101P8_20190528152826/CSD_CACX7605101P8_20190528173620/CSD_XOCHILT_CASAS_CHAVEZ_CACX7605101P8_20190528_173544.key")
      val privateKeyContent = urlResource.openStream().readAllBytes()
      val privateKey: PrivateKey = privateKeyFactoryImpl.getInstance(privateKeyContent, "12345678a")
      privateKey shouldNot be(null)
      privateKey.getEncoded
    }

  }

}
