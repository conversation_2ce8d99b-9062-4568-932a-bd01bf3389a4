package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.sat.models.cfdi33.CTipoDeComprobante
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class TipoComprobanteFactoryImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "Methods tests" - {
    val tipoComprobanteFactoryImpl = new TipoComprobanteFactoryImpl
    "getInstance" in {
      tipoComprobanteFactoryImpl.getInstance("I") shouldEqual CTipoDeComprobante.I
    }
  }
}
