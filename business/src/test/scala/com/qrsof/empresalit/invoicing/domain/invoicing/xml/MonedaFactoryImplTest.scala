package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.sat.models.cfdi33.CMoneda
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class MonedaFactoryImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "MonedaFactoryImpl" - {
    val impl = new MonedaFactoryImpl
    "getInstace" in {
      impl.getInstace("MXN") shouldEqual CMoneda.MXN
      impl.getInstace("USD") shouldEqual CMoneda.USD
    }
  }
}
