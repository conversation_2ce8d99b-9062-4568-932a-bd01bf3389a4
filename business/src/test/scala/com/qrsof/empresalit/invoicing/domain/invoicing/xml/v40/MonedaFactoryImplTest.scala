package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.PathAnyFreeSpecAppTest
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.CMoneda

class MonedaFactoryImplTest extends PathAnyFreeSpecAppTest {

  "MonedaFactoryImpl" - {
    val impl = new MonedaFactoryImpl
    "getInstace" in {
      impl.getInstace("MXN") shouldEqual CMoneda.MXN
      impl.getInstace("USD") shouldEqual CMoneda.USD
    }
  }

}
