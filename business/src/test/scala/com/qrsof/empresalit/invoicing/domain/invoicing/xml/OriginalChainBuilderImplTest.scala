package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class OriginalChainBuilderImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {
//
//  "OriginalChainBuilderImpl" - {
//    val xmlSerializer = mock[XmlSerializer]
//    val originalChainBuilderImpl = new OriginalChainBuilderImpl(xmlSerializer)
//    val comprobante = new Comprobante
//
//    val xml = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<cfdi:Comprobante xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n    xmlns:tfd=\"http://www.sat.gob.mx/TimbreFiscalDigital\"\n    xsi:schemaLocation=\"http://www.sat.gob.mx/cfd/3 http://www.sat.gob.mx/sitio_internet/cfd/3/cfdv33.xsd\"\n    Version=\"3.3\" Folio=\"0002402\" Fecha=\"2020-12-01T16:21:09\"\n    Sello=\"BgzEDdN7GuNTfLmtJ7HHdgpy4lcG188ky7ey21krWXKpt8AoFVfUVjk9/rJXH4AZ11kZCssmqiHFx/ZC5/iugzShySNTALTkNnMZFMYV6Y9tfkeqjEsfgaSeUvm1qp/US0p+yiSXWXcXR9BJIrTIYxKU46wmNkRyUsgVj74JmtLiyYLTBnCeZQg7EEjGHLCtldGb2yQNRZvs5aIw0ch2ut5FSK6pJAtFkqW/xLKfU3io+fIP38mANJjzVr1+CzRyL4dRFicoxYY6RSkXs8nefBOMHbsgXUzf2kQ7QzDBTF++iVp3mAc7lm8+dA4lrNPK/bGo3y6xOHMgyl9SGh2FAQ==\"\n    FormaPago=\"04\" NoCertificado=\"00001000000504510786\"\n    Certificado=\"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\"\n    SubTotal=\"246.56\" Moneda=\"MXN\" Total=\"286.00\" TipoDeComprobante=\"I\" MetodoPago=\"PUE\"\n    LugarExpedicion=\"14400\" xmlns:cfdi=\"http://www.sat.gob.mx/cfd/3\">\n    <cfdi:Emisor Rfc=\"SSO080613H4A\" Nombre=\"SPROUT SOLUTIONS SA DE CV\" RegimenFiscal=\"601\"/>\n    <cfdi:Receptor Rfc=\"HEPC880430CJ1\" Nombre=\"CARLOS HERNANDEZ PEREZ\" UsoCFDI=\"G03\"/>\n    <cfdi:Conceptos>\n        <cfdi:Concepto ClaveProdServ=\"45121610\" Cantidad=\"2\" ClaveUnidad=\"H87\" Unidad=\"Pieza\"\n            Descripcion=\"CABLE SIAMES C/CONECTORES BNC HEMBRA A MACHO CCTV 10MTS F25\"\n            ValorUnitario=\"84.48\" Importe=\"168.97\">\n            <cfdi:Impuestos>\n                <cfdi:Traslados>\n                    <cfdi:Traslado Base=\"168.97\" Impuesto=\"002\" TipoFactor=\"Tasa\"\n                        TasaOCuota=\"0.160000\" Importe=\"27.03\"/>\n                </cfdi:Traslados>\n            </cfdi:Impuestos>\n        </cfdi:Concepto>\n        <cfdi:Concepto ClaveProdServ=\"78102203\" Cantidad=\"1\" ClaveUnidad=\"SX\" Unidad=\"Envío\"\n            Descripcion=\"MERCADO ENVíOS\" ValorUnitario=\"77.59\" Importe=\"77.59\">\n            <cfdi:Impuestos>\n                <cfdi:Traslados>\n                    <cfdi:Traslado Base=\"77.59\" Impuesto=\"002\" TipoFactor=\"Tasa\"\n                        TasaOCuota=\"0.160000\" Importe=\"12.41\"/>\n                </cfdi:Traslados>\n            </cfdi:Impuestos>\n        </cfdi:Concepto>\n    </cfdi:Conceptos>\n    <cfdi:Impuestos TotalImpuestosTrasladados=\"39.44\">\n        <cfdi:Traslados>\n            <cfdi:Traslado Impuesto=\"002\" TipoFactor=\"Tasa\" TasaOCuota=\"0.160000\" Importe=\"39.44\"/>\n        </cfdi:Traslados>\n    </cfdi:Impuestos>\n    <cfdi:Complemento>\n        <tfd:TimbreFiscalDigital\n            xsi:schemaLocation=\"http://www.sat.gob.mx/TimbreFiscalDigital http://www.sat.gob.mx/sitio_internet/cfd/TimbreFiscalDigital/TimbreFiscalDigitalv11.xsd\"\n            Version=\"1.1\" UUID=\"e7da172f-a70d-4352-bff2-0386af292802\"\n            FechaTimbrado=\"2020-12-01T16:21:09\" RfcProvCertif=\"LSO1306189R5\"\n            SelloCFD=\"BgzEDdN7GuNTfLmtJ7HHdgpy4lcG188ky7ey21krWXKpt8AoFVfUVjk9/rJXH4AZ11kZCssmqiHFx/ZC5/iugzShySNTALTkNnMZFMYV6Y9tfkeqjEsfgaSeUvm1qp/US0p+yiSXWXcXR9BJIrTIYxKU46wmNkRyUsgVj74JmtLiyYLTBnCeZQg7EEjGHLCtldGb2yQNRZvs5aIw0ch2ut5FSK6pJAtFkqW/xLKfU3io+fIP38mANJjzVr1+CzRyL4dRFicoxYY6RSkXs8nefBOMHbsgXUzf2kQ7QzDBTF++iVp3mAc7lm8+dA4lrNPK/bGo3y6xOHMgyl9SGh2FAQ==\"\n            NoCertificadoSAT=\"00001000000408254801\"\n            SelloSAT=\"Xu1AMWsbGcNMXn+uDUzb3p8T+C7ncuGQ24f/FehtdJQmEgrrLPy17qXXRORq4Sr+7PTsvPGyiMj4d/8x4L/RCwYpcbHXLNpMHut9d6HPCCJjjd1/KhZh1c7RLMr/4eE4W0ytP4WaN6pWneEP42XNt91L44fmMG/4lfwmXVaMS7b93R5PxQQQ51nMWQTuQ2wsQHrOsuWMrV2n8ae2ryCnRheYoB6XbNDvFXJDNr6kEU3mGiJqw/F/Y3sr1JOJE20Mttbg7jarQDT3C6CI9MyMDnZn2ffh+lv+GlYTxXZxSj9MB3u7ClU/VjXFyaGTtQWZhLYH8x4+/ygd6f9vJzDxsQ==\"\n        />\n    </cfdi:Complemento>\n</cfdi:Comprobante>"
//    val expectedOriginalChain = "||3.3|0002402|2020-12-01T16:21:09|04|00001000000504510786|246.56|MXN|286.00|I|PUE|14400|SSO080613H4A|SPROUT SOLUTIONS SA DE CV|601|HEPC880430CJ1|CARLOS HERNANDEZ PEREZ|G03|45121610|2|H87|Pieza|CABLE SIAMES C/CONECTORES BNC HEMBRA A MACHO CCTV 10MTS F25|84.48|168.97|168.97|002|Tasa|0.160000|27.03|78102203|1|SX|Envío|MERCADO ENVíOS|77.59|77.59|77.59|002|Tasa|0.160000|12.41|002|Tasa|0.160000|39.44|39.44||"
//    Mockito.when(xmlSerializer.execute(comprobante)) thenReturn xml.getBytes
//    "execute" in {
//      val originalChain = originalChainBuilderImpl.execute(comprobante)
//      new String(originalChain) shouldEqual new String(expectedOriginalChain)
//    }
//  }
}
