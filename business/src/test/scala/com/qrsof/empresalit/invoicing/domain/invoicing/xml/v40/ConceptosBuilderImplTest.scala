package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.PathAnyFreeSpecAppTest
import com.qrsof.empresalit.invoicing.domain.conceptos.impuestos.{ConceptoRetencion, ConceptoTraslado}
import com.qrsof.empresalit.invoicing.domain.conceptos.{ConceptosImpuestos, CuentaPredial, InformacionAduanera}
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.conceptos.{ACuentaTerceros, Concepto, Parte}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.{CTipoFactor, ObjectFactory}

class ConceptosBuilderImplTest extends PathAnyFreeSpecAppTest {

  "Bind coneptos data" - {
    val factory = new ObjectFactory
    val conceptosBuilderImpl = new ConceptosBuilderImpl
    val concepto = Concepto(
      claveProductoServicio = "claveProductoServicio",
      noIdentificacion = Some("noIdentificacion"),
      cantidad = BigDecimal(1),
      claveUnidad = "claveUnidad",
      unidad = Some("unidad"),
      descripcion = "descripcion",
      valorUnitario = BigDecimal(2),
      importe = BigDecimal(3),
      descuento = Some(BigDecimal(4)),
      objetoImp = "objetoImp",
      impuestos = Some(ConceptosImpuestos(
        traslados = Seq(ConceptoTraslado(
          base = BigDecimal(1),
          impuesto = "impuesto",
          tipoFactor = "Tasa",
          tasaCuota = BigDecimal(2),
          importe = BigDecimal(3)
        )),
        retenciones = List(ConceptoRetencion(
          base = BigDecimal(1),
          impuesto = "impuesto",
          tipoFactor = "Tasa",
          tasaCuota = BigDecimal(2),
          importe = BigDecimal(3)
        ))
      )),
      aCuentaTerceros = Some(ACuentaTerceros(
        rfcACuentaTerceros = "rfcACuentaTerceros",
        nombreACuentaTerceros = "nombreACuentaTerceros",
        regimenFiscalACuentaTerceros = "regimenFiscalACuentaTerceros",
        domicilioFiscalACuentaTerceros = "domicilioFiscalACuentaTerceros"
      )),
      informacionAduanera = List(InformacionAduanera("numeroPedimento")),
      cuentaPredial = List(CuentaPredial("numero")),
      parte = List(Parte(
        claveProdServ = "claveProdServ",
        noIdentificacion = Some("noIdentificacion"),
        cantidad = BigDecimal(1),
        unidad = Some("unidad"),
        descripcion = "descripcion",
        valorUnitario = Some(BigDecimal(2)),
        importe = Some(BigDecimal(3)),
        informacionAduanera = List(InformacionAduanera("numeroPedimento"))
      ))
    )
    val conceptos = conceptosBuilderImpl.execute(factory, List(concepto))
    "concepto" - {
      "claveProductoServicio" in {
        conceptos.getConcepto.get(0).getClaveProdServ shouldEqual "claveProductoServicio"
      }
      "noIdentificacion" in {
        conceptos.getConcepto.get(0).getNoIdentificacion shouldEqual "noIdentificacion"
      }
      "cantidad" in {
        conceptos.getConcepto.get(0).getCantidad.toString shouldEqual "1"
      }
      "claveUnidad" in {
        conceptos.getConcepto.get(0).getClaveUnidad shouldEqual "claveUnidad"
      }
      "unidad" in {
        conceptos.getConcepto.get(0).getUnidad shouldEqual "unidad"
      }
      "descripcion" in {
        conceptos.getConcepto.get(0).getDescripcion shouldEqual "descripcion"
      }
      "valorUnitario" in {
        conceptos.getConcepto.get(0).getValorUnitario.toString shouldEqual "2"
      }
      "importe" in {
        conceptos.getConcepto.get(0).getImporte.toString shouldEqual "3"
      }
      "descuento" in {
        conceptos.getConcepto.get(0).getDescuento.toString shouldEqual "4"
      }
      "objetoImp" in {
        conceptos.getConcepto.get(0).getObjetoImp shouldEqual "objetoImp"
      }
      "impuestos" - {
        "traslados" - {
          "base" in {
            conceptos.getConcepto.get(0).getImpuestos.getTraslados.getTraslado.get(0).getBase.toString shouldEqual "1"
          }
          "impuesto" in {
            conceptos.getConcepto.get(0).getImpuestos.getTraslados.getTraslado.get(0).getImpuesto shouldEqual "impuesto"
          }
          "tipoFactor" in {
            conceptos.getConcepto.get(0).getImpuestos.getTraslados.getTraslado.get(0).getTipoFactor shouldEqual CTipoFactor.TASA
          }
          "tasaCuota" in {
            conceptos.getConcepto.get(0).getImpuestos.getTraslados.getTraslado.get(0).getTasaOCuota.toString shouldEqual "2"
          }
          "importe" in {
            conceptos.getConcepto.get(0).getImpuestos.getTraslados.getTraslado.get(0).getImporte.toString shouldEqual "3"
          }
        }
      }
      "aCuentaTerceros" - {
        "rfcACuentaTerceros" in {
          conceptos.getConcepto.get(0).getACuentaTerceros.getRfcACuentaTerceros shouldEqual "rfcACuentaTerceros"
        }
        "nombreACuentaTerceros" in {
          conceptos.getConcepto.get(0).getACuentaTerceros.getNombreACuentaTerceros shouldEqual "nombreACuentaTerceros"
        }
        "regimenFiscalACuentaTerceros" in {
          conceptos.getConcepto.get(0).getACuentaTerceros.getRegimenFiscalACuentaTerceros shouldEqual "regimenFiscalACuentaTerceros"
        }
        "domicilioFiscalACuentaTerceros" in {
          conceptos.getConcepto.get(0).getACuentaTerceros.getDomicilioFiscalACuentaTerceros shouldEqual "domicilioFiscalACuentaTerceros"
        }
      }
      "informacionAduanera" - {
        "numeroPedimento" in {
          conceptos.getConcepto.get(0).getInformacionAduanera.get(0).getNumeroPedimento shouldEqual "numeroPedimento"
        }
      }
      "cuentaPredial" - {
        "numero" in {
          conceptos.getConcepto.get(0).getCuentaPredial.get(0).getNumero shouldEqual "numero"
        }
      }
      "parte" - {
        "claveProdServ" in {
          conceptos.getConcepto.get(0).getParte.get(0).getClaveProdServ shouldEqual "claveProdServ"
        }
        "noIdentificacion" in {
          conceptos.getConcepto.get(0).getParte.get(0).getNoIdentificacion shouldEqual "noIdentificacion"
        }
        "cantidad" in {
          conceptos.getConcepto.get(0).getParte.get(0).getCantidad.toString shouldEqual "1"
        }
        "unidad" in {
          conceptos.getConcepto.get(0).getParte.get(0).getUnidad shouldEqual "unidad"
        }
        "descripcion" in {
          conceptos.getConcepto.get(0).getParte.get(0).getDescripcion shouldEqual "descripcion"
        }
        "valorUnitario" in {
          conceptos.getConcepto.get(0).getParte.get(0).getValorUnitario.toString shouldEqual "2"
        }
        "importe" in {
          conceptos.getConcepto.get(0).getParte.get(0).getImporte.toString shouldEqual "3"
        }
        "informacionAduanera" - {
          "numeroPedimento" in {
            conceptos.getConcepto.get(0).getParte.get(0).getInformacionAduanera.get(0).getNumeroPedimento shouldEqual "numeroPedimento"
          }
        }
      }
    }
  }

}
