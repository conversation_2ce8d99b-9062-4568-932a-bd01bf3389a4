package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.sat.models.cfdi33.CMetodoPago
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class MetodoPagoFactoryImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "MetodoPagoFactory" - {
    val impl = new MetodoPagoFactoryImpl
    "getInstance" in {
      impl.getInstance("PUE") shouldEqual CMetodoPago.PUE
      impl.getInstance("PPD") shouldEqual CMetodoPago.PPD
    }
  }

}
