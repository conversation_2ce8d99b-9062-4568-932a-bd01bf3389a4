package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.PathAnyFreeSpecAppTest
import com.qrsof.empresalit.invoicing.domain.complementos.Complementos
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.impuestos.Impuestos
import com.qrsof.empresalit.invoicing.domain.invoicing.v40.{CfdiRelacionado, CfdiRelacionados, GenerateInvoiceRequest, InformacionGlobal}
import com.qrsof.empresalit.invoicing.domain.invoicing.xml.XmlTimeUtils
import com.qrsof.empresalit.invoicing.domain.invoicing.{IssuerInvoicingData, v40}
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.*
import org.mockito.{ArgumentMatchers, Mockito}

import java.security.PrivateKey
import javax.xml.datatype.XMLGregorianCalendar

class ComprobanteBinderImplTest extends PathAnyFreeSpecAppTest {

  "When: bind data form the invoice request to the comprobante dto" - {
    val emisorBuilder = mock[EmisorBuilder]
    val receptorBuilder = mock[ReceptorBuilder]
    val conceptosBuilder = mock[ConceptosBuilder]
    val xmlTimeUtils = mock[XmlTimeUtils]
    val monedaFactory = mock[MonedaFactory]
    val tipoComprobanteFactory = mock[TipoComprobanteFactory]
    val metodoPagoFactory = mock[MetodoPagoFactory]
    val impuestosBuilder = mock[ImpuestosBuilder]
    val complementosBuilder = mock[ComplementosBuilder]
    val privateKey = mock[PrivateKey]
    val gregorianCalendar = mock[XMLGregorianCalendar]

    val emisorKey = "emisorKey"

    val issuerInvoicingData = IssuerInvoicingData(
      certificateNumber = "nocertificado",
      privateKey = privateKey,
      publicKey = "publickey".getBytes,
      rfc = "rfc",
      name = "razonsocial",
      regimenFiscalClave = "regFiscal"
    )

    val receptorInvoice = v40.ReceptorInvoice(
      rfc = "AAA010101AAA",
      usoCfdi = "Gastos en general",
      nombre = "nombre",
      domicilioFiscalReceptor = "11111",
      residenciaFiscal = Some("Mexico"),
      numRegIdTrib = Some("numRegIdTrib"),
      regimenFiscalReceptor = "regimenFiscalReceptor"
    )
    val conceptos = Nil
    val impuestos = mock[Impuestos]
    val complemento = mock[Complementos]
    val informacionGlobal = InformacionGlobal(
      periodicidad = "periodicidad",
      meses = "meses",
      anio = 2020
    )
    val relacionados = CfdiRelacionados(
      tipoRelacion = "tipoRelacion",
      cfdiRelacionado = List(
        CfdiRelacionado(
          "uuid"
        )
      )
    )
    val generateInvoiceRequest = GenerateInvoiceRequest(
      companyKey = emisorKey,
      serie = Some("A"),
      folio = Some("1"),
      fecha = None,
      formaPago = Some("FP"),
      condicionesDePago = Some("condicionesDePago"),
      subTotal = BigDecimal(1),
      descuento = Some(BigDecimal(100)),
      moneda = "MXN",
      tipoCambio = Some(BigDecimal(12)),
      total = BigDecimal(2),
      tipoComprobante = "I",
      exportacion = "exportacion",
      metodoPago = Some("PUE"),
      lugarExpedicion = "42083",
      confirmacion = Some("confirmacion"),
      informacionGlobal = Some(informacionGlobal),
      cfdiRelacionados = Some(List(relacionados)),
      receptor = Some(receptorInvoice),
      conceptos = conceptos,
      impuestos = Some(impuestos),
      complementos = Some(complemento)
    )

    val comprobanteBinderImpl = new ComprobanteBinderImpl(
      emisorBuilder,
      receptorBuilder,
      conceptosBuilder,
      xmlTimeUtils,
      monedaFactory,
      tipoComprobanteFactory,
      metodoPagoFactory,
      impuestosBuilder,
      complementosBuilder
    )

    Mockito.when(xmlTimeUtils.getCurrentDate()).thenReturn(gregorianCalendar)
    Mockito.when(monedaFactory.getInstace("MXN")).thenReturn(CMoneda.MXN)
    Mockito.when(tipoComprobanteFactory.getInstance("I")).thenReturn(CTipoDeComprobante.I)
    Mockito.when(metodoPagoFactory.getInstance("PUE")).thenReturn(CMetodoPago.PUE)

    val comprobante = comprobanteBinderImpl.execute(issuerInvoicingData, generateInvoiceRequest)

    "then: version should be 4.0" in {
      comprobante.getVersion shouldEqual "4.0"
    }

    "and: serie" in {
      comprobante.getSerie shouldEqual "A"
    }
    "and: folio" in {
      comprobante.getFolio shouldEqual "1"
    }
    "and: fecha" in {
      comprobante.getFecha shouldEqual gregorianCalendar
    }
    "and: formaPago" in {
      comprobante.getFormaPago shouldEqual "FP"
    }
    "and: condicionesDePago" in {
      comprobante.getCondicionesDePago shouldEqual "condicionesDePago"
    }
    "and: subTotal" in {
      comprobante.getSubTotal.toString shouldEqual "1"
    }
    "and: descuento" in {
      comprobante.getDescuento.toString shouldEqual "100"
    }
    "and: moneda" in {
      comprobante.getMoneda.toString shouldEqual "MXN"
    }
    "and: tipoCambio" in {
      comprobante.getTipoCambio.toString shouldEqual "12"
    }
    "and: total" in {
      comprobante.getTotal.toString shouldEqual "2"
    }
    "and: tipoComprobante" in {
      comprobante.getTipoDeComprobante.toString shouldEqual "I"
    }
    "and: exportacion" in {
      comprobante.getExportacion shouldEqual "exportacion"
    }
    "and: metodoPago" in {
      comprobante.getMetodoPago.toString shouldEqual "PUE"
    }
    "and: lugarExpedicion" in {
      comprobante.getLugarExpedicion shouldEqual "42083"
    }
    "and: confirmacion" in {
      comprobante.getConfirmacion shouldEqual "confirmacion"
    }
    "and: informacionGlobal" - {
      "Periodicidad" in {
        comprobante.getInformacionGlobal.getPeriodicidad shouldEqual informacionGlobal.periodicidad
      }
      "Meses" in {
        comprobante.getInformacionGlobal.getMeses shouldEqual informacionGlobal.meses
      }
      "Año" in {
        comprobante.getInformacionGlobal.getAño shouldEqual informacionGlobal.anio
      }
    }

    "cfdiRelacionados" - {
      "TipoRelacion" in {
        comprobante.getCfdiRelacionados.get(0).getTipoRelacion shouldEqual relacionados.tipoRelacion
      }
      "CfdiRelacionado" - {
        "UUID" in {
          comprobante.getCfdiRelacionados.get(0).getCfdiRelacionado.get(0).getUUID shouldEqual relacionados.cfdiRelacionado.head.uuid
        }
      }
    }

    "and: receptor" in {
      Mockito.verify(receptorBuilder).execute(ArgumentMatchers.any(classOf[ObjectFactory]), ArgumentMatchers.eq(receptorInvoice))
    }
    "and: emisor" in {
      Mockito.verify(emisorBuilder).execute(ArgumentMatchers.any(classOf[ObjectFactory]), ArgumentMatchers.eq(issuerInvoicingData))
    }
    "and: conceptosBuilder" in {
      Mockito.verify(conceptosBuilder).execute(ArgumentMatchers.any(classOf[ObjectFactory]), ArgumentMatchers.eq(conceptos))
    }
    "and: impuestos" in {
      Mockito.verify(impuestosBuilder).execute(ArgumentMatchers.any(classOf[ObjectFactory]), ArgumentMatchers.eq(impuestos))
    }
    "and: complemento" in {
      Mockito.verify(complementosBuilder).execute(ArgumentMatchers.any(classOf[ObjectFactory]), ArgumentMatchers.eq(complemento))
    }
  }

}
