package com.qrsof.empresalit.invoicing.domain.invoicing.xml.v40

import com.qrsof.empresalit.PathAnyFreeSpecAppTest
import com.qrsof.empresalit.invoicing.domain.invoicing.IssuerInvoicingData
import com.qrsof.empresalit.invoicing.sat.models.cfdi40.{Comprobante, ObjectFactory}

import java.security.PrivateKey

class EmisorBuilderImplTest extends PathAnyFreeSpecAppTest {

  "When: build emisor" - {
    val emisorBuilderImpl = new EmisorBuilderImpl
    val issuerInvoicingData = IssuerInvoicingData(
      certificateNumber = "certificateNumber", privateKey = mock[PrivateKey], publicKey = "pk".getBytes(), rfc = "rfc", name = "name", regimenFiscalClave = "regimenFiscalClave"
    )
    trait Execution {
      val emisor: Comprobante.Emisor = emisorBuilderImpl.execute(new ObjectFactory, issuerInvoicingData)
    }

    "then: set nombre" in new Execution {
      emisor.getNombre shouldEqual issuerInvoicingData.name
    }

    "then: set rfc" in new Execution {
      emisor.getRfc shouldEqual issuerInvoicingData.rfc
    }

    "then: set regimen fiscal" in new Execution {
      emisor.getRegimenFiscal shouldEqual issuerInvoicingData.regimenFiscalClave
    }

  }
}
