package com.qrsof.empresalit.invoicing.domain.invoicing.xml

import com.qrsof.empresalit.invoicing.domain.impuestos.{Impuestos, Retencion, Traslado}
import com.qrsof.empresalit.invoicing.sat.models.cfdi33.{CTipoFactor, ObjectFactory}
import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class ImpuestosBuilderImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "ImpuestosBuilderImpl" - {
    val impuestosBuilderImpl = new ImpuestosBuilderImpl
    "execute" in {
      val objectFactory = new ObjectFactory
      val impTraslado = BigDecimal(1)
      val impRetenido = BigDecimal(2)
      val impuestos = Impuestos(
        totalImpuestosTrasladados = Some(impTraslado),
        totalImpuestosRetenidos = Some(impRetenido),
        retenciones = Some(Seq(Retencion("iva", impRetenido))),
        traslados = Some(Seq(Traslado("iva", "Ta<PERSON>", BigDecimal("0.16000"), impTraslado)))
      )
      val impuestosResult = impuestosBuilderImpl.execute(objectFactory, impuestos)
      impuestosResult.getTotalImpuestosRetenidos.floatValue() shouldEqual impRetenido.floatValue
      impuestosResult.getTotalImpuestosTrasladados.floatValue() shouldEqual impTraslado.floatValue
      impuestosResult.getTraslados.getTraslado.get(0).getImporte.floatValue() shouldEqual impTraslado.floatValue
      impuestosResult.getTraslados.getTraslado.get(0).getImpuesto shouldEqual "iva"
      impuestosResult.getTraslados.getTraslado.get(0).getTasaOCuota.floatValue() shouldEqual BigDecimal("0.16000").floatValue
      impuestosResult.getTraslados.getTraslado.get(0).getTipoFactor shouldEqual CTipoFactor.fromValue("Tasa")
      impuestosResult.getRetenciones.getRetencion.get(0).getImporte.doubleValue() shouldEqual impRetenido.doubleValue
      impuestosResult.getRetenciones.getRetencion.get(0).getImpuesto shouldEqual "iva"
    }
  }
}
