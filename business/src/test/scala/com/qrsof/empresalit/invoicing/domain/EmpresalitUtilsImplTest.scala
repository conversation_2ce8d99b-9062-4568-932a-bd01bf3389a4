package com.qrsof.empresalit.invoicing.domain

import org.scalatest.freespec.PathAnyFreeSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class EmpresalitUtilsImplTest extends PathAnyFreeSpec with MockitoSugar with Matchers {

  "EmpreslitUtilsImpl" - {
    val empreslitUtilsImpl = new EmpresalitUtilsImpl
    "getDate" in {
      val date = empreslitUtilsImpl.getDate
      date shouldNot (be(null))
    }

    "generateKey" in {
      val generateKey = empreslitUtilsImpl.generateKey()
      val generateKey2 = empreslitUtilsImpl.generateKey()
      generateKey shouldNot (equal(generateKey2))
    }
  }
}
